#!/usr/bin/env python3
"""
性能测试脚本
"""
import asyncio
import aiohttp
import time
import json
import statistics
from concurrent.futures import ThreadPoolExecutor
import requests

# API配置
BASE_URL = "http://localhost:8000/api/v1"
TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxIiwiZXhwIjoxNzUyODY5MDQyfQ.zmspM5MCpV40B7-nYuDuQSZgAy51EFcIe2qP3gda9aw"

# 请求头
headers = {
    "Authorization": f"Bearer {TOKEN}",
    "Content-Type": "application/json"
}

def test_single_request():
    """测试单个请求的响应时间"""
    start_time = time.time()
    try:
        response = requests.get(f"{BASE_URL}/crawler/status", headers=headers)
        end_time = time.time()
        
        if response.status_code == 200:
            return {
                "success": True,
                "response_time": end_time - start_time,
                "status_code": response.status_code,
                "data_size": len(response.content)
            }
        else:
            return {
                "success": False,
                "response_time": end_time - start_time,
                "status_code": response.status_code,
                "error": response.text
            }
    except Exception as e:
        end_time = time.time()
        return {
            "success": False,
            "response_time": end_time - start_time,
            "error": str(e)
        }

def test_concurrent_requests(num_requests=10):
    """测试并发请求性能"""
    print(f"🔄 开始并发测试 ({num_requests} 个请求)...")
    
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=num_requests) as executor:
        futures = [executor.submit(test_single_request) for _ in range(num_requests)]
        results = [future.result() for future in futures]
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 分析结果
    successful_requests = [r for r in results if r["success"]]
    failed_requests = [r for r in results if not r["success"]]
    
    if successful_requests:
        response_times = [r["response_time"] for r in successful_requests]
        avg_response_time = statistics.mean(response_times)
        min_response_time = min(response_times)
        max_response_time = max(response_times)
        median_response_time = statistics.median(response_times)
    else:
        avg_response_time = min_response_time = max_response_time = median_response_time = 0
    
    print(f"✅ 并发测试完成!")
    print(f"📊 测试结果:")
    print(f"   总请求数: {num_requests}")
    print(f"   成功请求: {len(successful_requests)}")
    print(f"   失败请求: {len(failed_requests)}")
    print(f"   总耗时: {total_time:.2f}s")
    print(f"   平均响应时间: {avg_response_time:.3f}s")
    print(f"   最快响应时间: {min_response_time:.3f}s")
    print(f"   最慢响应时间: {max_response_time:.3f}s")
    print(f"   中位数响应时间: {median_response_time:.3f}s")
    print(f"   请求/秒: {num_requests/total_time:.2f}")
    
    if failed_requests:
        print(f"❌ 失败请求详情:")
        for i, req in enumerate(failed_requests[:3]):  # 只显示前3个失败请求
            print(f"   失败 {i+1}: {req.get('error', 'Unknown error')}")
    
    return {
        "total_requests": num_requests,
        "successful_requests": len(successful_requests),
        "failed_requests": len(failed_requests),
        "total_time": total_time,
        "avg_response_time": avg_response_time,
        "min_response_time": min_response_time,
        "max_response_time": max_response_time,
        "median_response_time": median_response_time,
        "requests_per_second": num_requests/total_time
    }

def test_different_endpoints():
    """测试不同端点的性能"""
    endpoints = [
        ("/auth/me", "用户信息"),
        ("/crawler/test", "简单测试"),
        ("/crawler/test-db", "数据库测试"),
        ("/crawler/status", "爬虫状态")
    ]
    
    print("🔍 测试不同端点性能...")
    results = {}
    
    for endpoint, name in endpoints:
        print(f"   测试 {name} ({endpoint})...")
        start_time = time.time()
        
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
            end_time = time.time()
            response_time = end_time - start_time
            
            results[name] = {
                "endpoint": endpoint,
                "success": response.status_code == 200,
                "response_time": response_time,
                "status_code": response.status_code,
                "data_size": len(response.content) if response.status_code == 200 else 0
            }
            
            print(f"      ✅ {response_time:.3f}s ({response.status_code})")
            
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            results[name] = {
                "endpoint": endpoint,
                "success": False,
                "response_time": response_time,
                "error": str(e)
            }
            print(f"      ❌ {response_time:.3f}s (错误: {e})")
    
    return results

def main():
    """主测试函数"""
    print("🚀 开始性能测试...")
    print("=" * 50)
    
    # 1. 测试单个请求
    print("1️⃣ 单个请求测试")
    single_result = test_single_request()
    if single_result["success"]:
        print(f"✅ 单个请求成功: {single_result['response_time']:.3f}s")
    else:
        print(f"❌ 单个请求失败: {single_result.get('error', 'Unknown error')}")
    
    print("\n" + "=" * 50)
    
    # 2. 测试不同端点
    print("2️⃣ 不同端点性能测试")
    endpoint_results = test_different_endpoints()
    
    print("\n" + "=" * 50)
    
    # 3. 测试并发性能
    print("3️⃣ 并发性能测试")
    
    # 测试不同并发级别
    for concurrency in [5, 10, 20]:
        print(f"\n🔄 并发级别: {concurrency}")
        concurrent_result = test_concurrent_requests(concurrency)
        
        # 如果成功率低于80%，停止更高并发测试
        success_rate = concurrent_result["successful_requests"] / concurrent_result["total_requests"]
        if success_rate < 0.8:
            print(f"⚠️ 成功率过低 ({success_rate:.1%})，停止更高并发测试")
            break
    
    print("\n" + "=" * 50)
    print("✅ 性能测试完成!")

if __name__ == "__main__":
    main()
