// 前端功能测试脚本
import axios from 'axios';

const FRONTEND_URL = 'http://localhost:3000';

async function testFrontend() {
  console.log('🧪 开始测试前端应用...\n');

  try {
    // 测试前端服务器是否运行
    console.log('1. 测试前端服务器连接...');
    const response = await axios.get(FRONTEND_URL, {
      timeout: 5000,
      validateStatus: () => true // 接受所有状态码
    });
    
    if (response.status === 200) {
      console.log('✅ 前端服务器运行正常');
      console.log(`   状态码: ${response.status}`);
      console.log(`   内容类型: ${response.headers['content-type']}`);
    } else {
      console.log(`⚠️  前端服务器响应异常，状态码: ${response.status}`);
    }

    // 检查是否包含React应用的标识
    if (response.data.includes('root') || response.data.includes('React')) {
      console.log('✅ 检测到React应用结构');
    }

  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ 前端服务器未启动或无法连接');
      console.log('   请确保运行了 npm run dev 命令');
    } else {
      console.log(`❌ 测试失败: ${error.message}`);
    }
  }

  console.log('\n📋 前端测试完成');
  console.log('\n🌐 访问地址:');
  console.log(`   本地访问: ${FRONTEND_URL}`);
  console.log('   网络访问: 使用 --host 参数暴露网络访问');
  
  console.log('\n📚 功能说明:');
  console.log('   ✅ 登录页面 - /login');
  console.log('   ✅ 仪表盘 - /');
  console.log('   ✅ 账号管理 - /accounts');
  console.log('   🚧 笔记管理 - /notes (开发中)');
  console.log('   🚧 留言管理 - /comments (开发中)');
  console.log('   🚧 爬虫管理 - /crawler (开发中)');
}

// 运行测试
testFrontend();
