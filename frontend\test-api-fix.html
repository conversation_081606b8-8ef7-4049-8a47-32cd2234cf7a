<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 15px 0;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API修复验证</h1>
        
        <div class="test-result loading" id="status">
            <p>⏳ 正在测试API连接...</p>
        </div>

        <button onclick="runTest()">重新测试</button>
        <button onclick="testLogin()">测试登录</button>
        <button onclick="testNotes()">测试笔记API</button>

        <div class="log" id="log"></div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function showStatus(success, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `test-result ${success ? 'success' : 'error'}`;
            statusDiv.innerHTML = `<p>${success ? '✅' : '❌'} ${message}</p>`;
        }

        async function testHealthCheck() {
            log('测试健康检查API...');
            try {
                const response = await fetch('/api/v1/health');
                if (response.ok) {
                    const data = await response.json();
                    log(`健康检查成功: ${JSON.stringify(data)}`);
                    return true;
                } else {
                    log(`健康检查失败: ${response.status} ${response.statusText}`);
                    return false;
                }
            } catch (error) {
                log(`健康检查异常: ${error.message}`);
                return false;
            }
        }

        async function testLogin() {
            log('测试登录API...');
            try {
                const formData = new FormData();
                formData.append('username', 'testuser2');
                formData.append('password', 'testpass123');

                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    body: formData,
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`登录成功: ${JSON.stringify(data)}`);
                    if (data.success && data.data && data.data.access_token) {
                        window.testToken = data.data.access_token;
                        showStatus(true, '登录成功，获得认证token');
                        return true;
                    } else {
                        showStatus(false, '登录响应格式错误');
                        return false;
                    }
                } else {
                    const errorText = await response.text();
                    log(`登录失败: ${response.status} ${errorText}`);
                    showStatus(false, `登录失败: ${response.status}`);
                    return false;
                }
            } catch (error) {
                log(`登录异常: ${error.message}`);
                showStatus(false, `登录异常: ${error.message}`);
                return false;
            }
        }

        async function testNotes() {
            log('测试笔记API...');
            
            if (!window.testToken) {
                log('没有认证token，先进行登录...');
                const loginSuccess = await testLogin();
                if (!loginSuccess) {
                    return false;
                }
            }

            try {
                const response = await fetch('/api/v1/notes', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${window.testToken}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`笔记API成功: ${JSON.stringify(data)}`);
                    const noteCount = data.data ? data.data.length : 0;
                    showStatus(true, `笔记API成功，获取到 ${noteCount} 条笔记`);
                    return true;
                } else {
                    const errorText = await response.text();
                    log(`笔记API失败: ${response.status} ${errorText}`);
                    showStatus(false, `笔记API失败: ${response.status}`);
                    return false;
                }
            } catch (error) {
                log(`笔记API异常: ${error.message}`);
                showStatus(false, `笔记API异常: ${error.message}`);
                return false;
            }
        }

        async function runTest() {
            log('开始完整测试...');
            showStatus(false, '正在测试...');
            
            // 1. 健康检查
            const healthOk = await testHealthCheck();
            if (!healthOk) {
                showStatus(false, '健康检查失败，API连接有问题');
                return;
            }

            // 2. 登录测试
            const loginOk = await testLogin();
            if (!loginOk) {
                showStatus(false, '登录失败，认证有问题');
                return;
            }

            // 3. 笔记API测试
            const notesOk = await testNotes();
            if (!notesOk) {
                showStatus(false, '笔记API失败');
                return;
            }

            showStatus(true, '所有测试通过！API连接正常');
            log('✅ 所有测试通过！');
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            log('页面加载完成，开始自动测试...');
            setTimeout(runTest, 1000);
        };
    </script>
</body>
</html>
