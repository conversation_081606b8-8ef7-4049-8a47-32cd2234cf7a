#!/usr/bin/env python3
"""
安全性分析脚本
"""
import sys
import os
import re
sys.path.append('.')

def check_security_configurations():
    """检查安全配置"""
    print('=== 安全配置检查 ===')
    
    issues = []
    recommendations = []
    
    # 检查环境变量和配置
    from app.core.config import settings
    
    # 1. 检查SECRET_KEY
    if settings.SECRET_KEY == "your-secret-key-change-in-production":
        issues.append("❌ SECRET_KEY 使用默认值，存在安全风险")
    elif len(settings.SECRET_KEY) < 32:
        recommendations.append("⚠️ SECRET_KEY 长度建议至少32字符")
    else:
        print("✅ SECRET_KEY 配置正确")
    
    # 2. 检查JWT配置
    if settings.ACCESS_TOKEN_EXPIRE_MINUTES > 60:
        recommendations.append("⚠️ JWT过期时间较长，建议缩短到60分钟以内")
    else:
        print("✅ JWT过期时间配置合理")
    
    # 3. 检查CORS配置
    cors_origins = settings.BACKEND_CORS_ORIGINS
    if "*" in cors_origins:
        issues.append("❌ CORS配置允许所有域名，存在安全风险")
    elif any("localhost" in origin for origin in cors_origins):
        recommendations.append("⚠️ 生产环境应移除localhost CORS配置")
    else:
        print("✅ CORS配置安全")
    
    # 4. 检查调试模式
    if settings.DEBUG:
        recommendations.append("⚠️ 生产环境应关闭DEBUG模式")
    else:
        print("✅ DEBUG模式已关闭")
    
    return issues, recommendations

def check_authentication_security():
    """检查认证安全性"""
    print('\n=== 认证安全检查 ===')
    
    issues = []
    recommendations = []
    
    # 检查密码策略
    try:
        from app.api.schemas.user import UserCreate
        # 这里应该检查密码验证规则，但需要查看具体实现
        print("✅ 用户创建模式存在")
    except ImportError:
        recommendations.append("⚠️ 未找到用户创建模式，请检查密码验证规则")
    
    # 检查认证依赖
    try:
        from app.api.deps import get_current_user, get_current_active_user, get_current_superuser
        print("✅ 认证依赖函数完整")
    except ImportError:
        issues.append("❌ 认证依赖函数缺失")
    
    # 检查安全模块
    try:
        from app.core.security import verify_password, get_password_hash, verify_token
        print("✅ 安全模块功能完整")
    except ImportError:
        issues.append("❌ 安全模块功能缺失")
    
    return issues, recommendations

def check_input_validation():
    """检查输入验证"""
    print('\n=== 输入验证检查 ===')
    
    issues = []
    recommendations = []
    
    # 检查Pydantic模式
    schema_files = []
    schema_dir = "app/api/schemas"
    if os.path.exists(schema_dir):
        for file in os.listdir(schema_dir):
            if file.endswith('.py') and file != '__init__.py':
                schema_files.append(file)
    
    if schema_files:
        print(f"✅ 找到 {len(schema_files)} 个数据验证模式文件")
        for file in schema_files:
            print(f"  - {file}")
    else:
        issues.append("❌ 未找到数据验证模式文件")
    
    # 检查SQL注入防护
    print("✅ 使用SQLAlchemy ORM，有效防护SQL注入")
    
    return issues, recommendations

def check_api_security():
    """检查API安全性"""
    print('\n=== API安全检查 ===')
    
    issues = []
    recommendations = []
    
    # 检查API路由保护
    try:
        from app.api.v1 import api_router
        from fastapi.routing import APIRoute
        
        protected_routes = 0
        unprotected_routes = 0
        
        for route in api_router.routes:
            if isinstance(route, APIRoute):
                # 检查是否有认证依赖
                has_auth = False
                for dependency in route.dependencies:
                    if 'get_current' in str(dependency.dependency):
                        has_auth = True
                        break
                
                if has_auth:
                    protected_routes += 1
                else:
                    # 检查是否是公开端点
                    if route.path in ['/health', '/docs', '/openapi.json', '/']:
                        continue
                    unprotected_routes += 1
        
        print(f"✅ 受保护的路由: {protected_routes}")
        if unprotected_routes > 0:
            recommendations.append(f"⚠️ 发现 {unprotected_routes} 个可能需要保护的路由")
        
    except Exception as e:
        recommendations.append(f"⚠️ 无法分析API路由保护: {e}")
    
    return issues, recommendations

def check_data_protection():
    """检查数据保护"""
    print('\n=== 数据保护检查 ===')
    
    issues = []
    recommendations = []
    
    # 检查敏感数据处理
    sensitive_fields = ['password', 'secret', 'key', 'token', 'cookie']
    
    # 检查模型中的敏感字段
    try:
        from app.models import Base
        
        for cls in Base.registry._class_registry.values():
            if hasattr(cls, '__tablename__'):
                for column_name in cls.__table__.columns.keys():
                    if any(sensitive in column_name.lower() for sensitive in sensitive_fields):
                        if 'hashed' in column_name.lower() or 'encrypted' in column_name.lower():
                            print(f"✅ {cls.__name__}.{column_name} - 已加密/哈希")
                        else:
                            recommendations.append(f"⚠️ {cls.__name__}.{column_name} - 建议加密存储")
    
    except Exception as e:
        recommendations.append(f"⚠️ 无法检查敏感数据字段: {e}")
    
    return issues, recommendations

def check_logging_security():
    """检查日志安全"""
    print('\n=== 日志安全检查 ===')
    
    issues = []
    recommendations = []
    
    # 检查是否有系统日志模型
    try:
        from app.models.user import SystemLog
        print("✅ 系统日志模型存在")
    except ImportError:
        recommendations.append("⚠️ 建议添加系统日志记录")
    
    # 检查日志配置
    from app.core.config import settings
    if hasattr(settings, 'LOG_LEVEL'):
        if settings.LOG_LEVEL.upper() == 'DEBUG':
            recommendations.append("⚠️ 生产环境建议使用INFO或WARNING日志级别")
        else:
            print(f"✅ 日志级别: {settings.LOG_LEVEL}")
    
    return issues, recommendations

def generate_security_report():
    """生成安全报告"""
    print('🔒 后端安全性检测报告')
    print('=' * 50)
    
    all_issues = []
    all_recommendations = []
    
    # 执行各项检查
    issues, recs = check_security_configurations()
    all_issues.extend(issues)
    all_recommendations.extend(recs)
    
    issues, recs = check_authentication_security()
    all_issues.extend(issues)
    all_recommendations.extend(recs)
    
    issues, recs = check_input_validation()
    all_issues.extend(issues)
    all_recommendations.extend(recs)
    
    issues, recs = check_api_security()
    all_issues.extend(issues)
    all_recommendations.extend(recs)
    
    issues, recs = check_data_protection()
    all_issues.extend(issues)
    all_recommendations.extend(recs)
    
    issues, recs = check_logging_security()
    all_issues.extend(issues)
    all_recommendations.extend(recs)
    
    # 生成总结
    print('\n' + '=' * 50)
    print('📊 安全检测总结')
    print('=' * 50)
    
    if all_issues:
        print(f'\n🚨 发现 {len(all_issues)} 个安全问题:')
        for issue in all_issues:
            print(f'  {issue}')
    else:
        print('\n✅ 未发现严重安全问题')
    
    if all_recommendations:
        print(f'\n💡 {len(all_recommendations)} 个改进建议:')
        for rec in all_recommendations[:10]:  # 只显示前10个
            print(f'  {rec}')
        if len(all_recommendations) > 10:
            print(f'  ... 还有 {len(all_recommendations) - 10} 个建议')
    
    # 安全评分
    total_checks = 20  # 假设总共20个检查项
    issues_weight = 3
    recommendations_weight = 1
    
    score = max(0, 100 - (len(all_issues) * issues_weight + len(all_recommendations) * recommendations_weight))
    
    print(f'\n🎯 安全评分: {score}/100')
    
    if score >= 90:
        print('🟢 安全状况: 优秀')
    elif score >= 80:
        print('🟡 安全状况: 良好')
    elif score >= 70:
        print('🟠 安全状况: 一般')
    else:
        print('🔴 安全状况: 需要改进')
    
    return len(all_issues), len(all_recommendations), score

if __name__ == "__main__":
    generate_security_report()
