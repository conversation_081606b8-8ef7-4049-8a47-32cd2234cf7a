#!/usr/bin/env python3
"""
简单测试模板API
"""
import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def login():
    """登录获取token"""
    login_data = {
        "username": "testuser2",
        "password": "testpass123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
    if response.status_code == 200:
        result = response.json()
        if "data" in result and "access_token" in result["data"]:
            return result["data"]["access_token"]
    return None

def test_template_api():
    """测试模板API"""
    token = login()
    if not token:
        print("❌ 登录失败")
        return
    
    headers = {"Authorization": f"Bearer {token}"}
    
    print("🔍 测试模板API...")
    
    # 直接访问模板API
    try:
        response = requests.get(f"{BASE_URL}/ai/templates", headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {response.headers}")
        print(f"响应内容: {response.text}")
        
        if response.status_code != 200:
            print("❌ API调用失败")
        else:
            print("✅ API调用成功")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_template_api()
