import { request } from './api';

export const accountsService = {
  // 获取账号列表
  getAccounts: async (params = {}) => {
    const response = await request.get('/xiaohongshu/accounts', { params });
    return response;
  },

  // 获取账号详情
  getAccountById: async (id) => {
    const response = await request.get(`/xiaohongshu/accounts/${id}`);
    return response;
  },

  // 创建账号
  createAccount: async (accountData) => {
    const response = await request.post('/xiaohongshu/accounts', accountData);
    return response;
  },

  // 更新账号
  updateAccount: async (id, accountData) => {
    const response = await request.put(`/xiaohongshu/accounts/${id}`, accountData);
    return response;
  },

  // 删除账号
  deleteAccount: async (id) => {
    const response = await request.delete(`/xiaohongshu/accounts/${id}`);
    return response;
  },

  // 更新账号Cookies
  updateCookies: async (id, cookies) => {
    const response = await request.post(`/xiaohongshu/accounts/${id}/cookies`, {
      cookies: cookies,
    });
    return response;
  },

  // 获取账号Cookies
  getCookies: async (id) => {
    const response = await request.get(`/xiaohongshu/accounts/${id}/cookies`);
    return response;
  },

  // 激活账号
  activateAccount: async (id) => {
    const response = await request.post(`/xiaohongshu/accounts/${id}/activate`);
    return response;
  },

  // 停用账号
  deactivateAccount: async (id) => {
    const response = await request.post(`/xiaohongshu/accounts/${id}/deactivate`);
    return response;
  },

  // 测试账号连接
  testAccount: async (id) => {
    const response = await request.post(`/xiaohongshu/accounts/${id}/test`);
    return response;
  },

  // 获取账号统计
  getAccountStats: async (id) => {
    const response = await request.get(`/xiaohongshu/accounts/${id}/stats`);
    return response;
  },

  // 批量操作账号
  batchOperation: async (operation, accountIds) => {
    const response = await request.post('/xiaohongshu/accounts/batch', {
      operation,
      account_ids: accountIds,
    });
    return response;
  },
};
