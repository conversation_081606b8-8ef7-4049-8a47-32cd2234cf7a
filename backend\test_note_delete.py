#!/usr/bin/env python3
"""
测试笔记删除功能
"""
import requests
import json

# API配置
BASE_URL = "http://localhost:8000/api/v1"

def get_auth_token():
    """获取认证token"""
    try:
        # 登录获取token
        login_data = {
            'username': 'testuser2',
            'password': 'testpass123'
        }
        
        response = requests.post(f'{BASE_URL}/auth/login', data=login_data)
        if response.status_code == 200:
            data = response.json()
            return data['data']['access_token']
        
        print(f"登录失败: {response.text}")
        return None
        
    except Exception as e:
        print(f"获取token异常: {e}")
        return None

def test_get_notes(headers):
    """获取笔记列表"""
    print("🔍 获取笔记列表...")
    
    try:
        response = requests.get(f"{BASE_URL}/notes", headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 笔记列表获取成功!")
            print(f"笔记数量: {len(data.get('data', []))}")
            return data.get('data', [])
        else:
            print(f"❌ 笔记列表获取失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return []

def test_delete_note(headers, note_id):
    """测试删除笔记"""
    if not note_id:
        print("\n⚠️ 跳过删除测试 - 没有有效的笔记ID")
        return False
        
    print(f"\n🔍 测试删除笔记 (ID: {note_id})...")
    
    try:
        response = requests.delete(f"{BASE_URL}/notes/{note_id}", headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code in [200, 204]:
            data = response.json() if response.content else {}
            print(f"✅ 笔记删除成功!")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 笔记删除失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_create_note(headers):
    """创建测试笔记"""
    print("\n🔍 创建测试笔记...")
    
    note_data = {
        "account_id": 1,  # 假设账号ID为1
        "note_url": "https://www.xiaohongshu.com/explore/test_delete_note_123",
        "title": "测试删除笔记",
        "crawl_interval": 300,
        "auto_reply_enabled": True
    }
    
    try:
        response = requests.post(f"{BASE_URL}/notes", headers=headers, json=note_data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 测试笔记创建成功!")
            note_id = data.get('data', {}).get('id')
            print(f"笔记ID: {note_id}")
            return note_id
        else:
            print(f"❌ 测试笔记创建失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def main():
    """主测试函数"""
    print("🚀 开始笔记删除功能测试...")
    print("=" * 50)
    
    # 获取认证token
    print("0️⃣ 获取认证token")
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证token，测试终止")
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    print(f"✅ 认证token获取成功")
    
    # 1. 获取现有笔记列表
    print("\n" + "=" * 50)
    print("1️⃣ 获取现有笔记列表")
    notes = test_get_notes(headers)
    
    # 2. 如果没有笔记，创建一个测试笔记
    note_id_to_delete = None
    if notes:
        note_id_to_delete = notes[0].get('id')
        print(f"使用现有笔记进行删除测试，ID: {note_id_to_delete}")
    else:
        print("\n" + "=" * 50)
        print("2️⃣ 创建测试笔记")
        note_id_to_delete = test_create_note(headers)
    
    # 3. 测试删除笔记
    print("\n" + "=" * 50)
    print("3️⃣ 测试删除笔记")
    delete_success = test_delete_note(headers, note_id_to_delete)
    
    # 4. 验证删除结果
    print("\n" + "=" * 50)
    print("4️⃣ 验证删除结果")
    final_notes = test_get_notes(headers)
    
    print("\n" + "=" * 50)
    print("✅ 笔记删除功能测试完成!")
    print(f"📊 测试结果总结:")
    print(f"   初始笔记数: {len(notes)}")
    print(f"   最终笔记数: {len(final_notes)}")
    print(f"   删除操作: {'✅ 成功' if delete_success else '❌ 失败'}")
    
    if delete_success and len(final_notes) == len(notes) - 1:
        print("🎉 删除功能验证成功！")
    elif not delete_success:
        print("❌ 删除功能存在问题！")
    else:
        print("⚠️ 删除结果不符合预期！")

if __name__ == "__main__":
    main()
