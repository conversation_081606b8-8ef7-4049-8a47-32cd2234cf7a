.main-layout {
  min-height: 100vh;
}

.layout-sider {
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  background: rgba(255, 255, 255, 0.1);
  margin: 16px;
  border-radius: 8px;
  transition: all 0.3s;
}

.logo-icon {
  font-size: 24px;
  color: #1890ff;
  margin-right: 8px;
}

.logo-text {
  color: white !important;
  margin: 0 !important;
  font-size: 16px;
  font-weight: 600;
}

.layout-menu {
  border-right: none;
  margin-top: 16px;
}

.layout-menu .ant-menu-item {
  margin: 4px 8px;
  border-radius: 6px;
  height: 40px;
  line-height: 40px;
}

.layout-menu .ant-menu-item-selected {
  background-color: #1890ff !important;
}

.site-layout {
  margin-left: 240px;
  transition: all 0.2s;
}

.layout-sider.ant-layout-sider-collapsed + .site-layout {
  margin-left: 80px;
}

.layout-header {
  background: #fff;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  position: sticky;
  top: 0;
  z-index: 99;
}

.header-left {
  display: flex;
  align-items: center;
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #1890ff;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f5f5;
}

.username {
  font-weight: 500;
  color: #333;
}

.layout-content {
  margin: 24px;
  padding: 0;
  min-height: calc(100vh - 112px);
}

.content-wrapper {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  min-height: calc(100vh - 160px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-sider {
    position: fixed;
    z-index: 999;
  }
  
  .site-layout {
    margin-left: 0;
  }
  
  .layout-sider.ant-layout-sider-collapsed + .site-layout {
    margin-left: 0;
  }
  
  .layout-header {
    padding: 0 16px;
  }
  
  .layout-content {
    margin: 16px;
  }
  
  .content-wrapper {
    padding: 16px;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .layout-header {
    background: #141414;
    border-bottom: 1px solid #303030;
  }
  
  .content-wrapper {
    background: #141414;
    color: #fff;
  }
  
  .user-info:hover {
    background-color: #262626;
  }
  
  .username {
    color: #fff;
  }
}
