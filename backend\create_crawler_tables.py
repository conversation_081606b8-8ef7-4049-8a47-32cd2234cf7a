#!/usr/bin/env python3
"""
创建爬虫任务相关数据表
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.database import engine, Base
from app.models.crawler_task import CrawlerTask, CrawlerTaskLog, CrawlerConfig
from app.models.user import User, Xiao<PERSON>shuAccount
from app.models.note import Note
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_crawler_tables():
    """创建爬虫任务相关表"""
    try:
        logger.info("开始创建爬虫任务相关数据表...")
        
        # 创建枚举类型（如果不存在）
        with engine.connect() as conn:
            # 检查并创建TaskStatus枚举
            result = conn.execute(text("""
                SELECT 1 FROM pg_type WHERE typname = 'taskstatus'
            """))
            if not result.fetchone():
                conn.execute(text("""
                    CREATE TYPE taskstatus AS ENUM (
                        'pending', 'running', 'completed', 'failed', 'cancelled', 'paused'
                    )
                """))
                logger.info("创建TaskStatus枚举类型")
            
            # 检查并创建TaskType枚举
            result = conn.execute(text("""
                SELECT 1 FROM pg_type WHERE typname = 'tasktype'
            """))
            if not result.fetchone():
                conn.execute(text("""
                    CREATE TYPE tasktype AS ENUM (
                        'manual', 'scheduled', 'auto'
                    )
                """))
                logger.info("创建TaskType枚举类型")
            
            conn.commit()
        
        # 创建表
        Base.metadata.create_all(bind=engine)
        logger.info("✅ 爬虫任务相关数据表创建成功！")
        
        # 验证表是否创建成功
        with engine.connect() as conn:
            tables = ['crawler_configs', 'crawler_tasks', 'crawler_task_logs']
            for table in tables:
                result = conn.execute(text(f"""
                    SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_name = '{table}'
                """))
                count = result.scalar()
                if count > 0:
                    logger.info(f"✅ 表 {table} 创建成功")
                else:
                    logger.error(f"❌ 表 {table} 创建失败")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建爬虫任务表失败: {e}")
        return False


def clear_crawler_data():
    """清理爬虫任务数据"""
    try:
        logger.info("开始清理爬虫任务数据...")
        
        with engine.connect() as conn:
            # 清理任务日志
            result = conn.execute(text("DELETE FROM crawler_task_logs"))
            logs_deleted = result.rowcount
            logger.info(f"清理任务日志: {logs_deleted} 条")
            
            # 清理爬虫任务
            result = conn.execute(text("DELETE FROM crawler_tasks"))
            tasks_deleted = result.rowcount
            logger.info(f"清理爬虫任务: {tasks_deleted} 条")
            
            # 清理爬虫配置
            result = conn.execute(text("DELETE FROM crawler_configs"))
            configs_deleted = result.rowcount
            logger.info(f"清理爬虫配置: {configs_deleted} 条")
            
            conn.commit()
            
        logger.info("✅ 爬虫任务数据清理完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 清理爬虫任务数据失败: {e}")
        return False


def create_sample_data():
    """创建示例数据"""
    try:
        from app.models.database import SessionLocal
        from app.services.crawler_task_service import CrawlerTaskService
        from app.models.crawler_task import TaskType, TaskStatus
        
        logger.info("开始创建示例数据...")
        
        db = SessionLocal()
        try:
            # 获取第一个用户和账号
            user = db.query(User).first()
            if not user:
                logger.warning("没有找到用户，跳过创建示例数据")
                return True
            
            account = db.query(XiaohongshuAccount).filter(
                XiaohongshuAccount.user_id == user.id
            ).first()
            if not account:
                logger.warning("没有找到小红书账号，跳过创建示例数据")
                return True
            
            # 获取第一个笔记
            note = db.query(Note).filter(Note.account_id == account.id).first()
            
            task_service = CrawlerTaskService(db)
            
            # 创建示例任务
            sample_tasks = [
                {
                    "task_name": "美妆分享笔记爬虫",
                    "task_type": TaskType.MANUAL,
                    "note_id": note.id if note else None
                },
                {
                    "task_name": "时尚穿搭笔记爬虫",
                    "task_type": TaskType.SCHEDULED,
                    "note_id": note.id if note else None
                },
                {
                    "task_name": "批量笔记爬虫任务",
                    "task_type": TaskType.AUTO,
                    "note_id": None
                }
            ]
            
            created_tasks = []
            for task_data in sample_tasks:
                task = task_service.create_task(
                    user_id=user.id,
                    account_id=account.id,
                    **task_data
                )
                created_tasks.append(task)
                logger.info(f"创建示例任务: {task.task_name}")
            
            # 更新一些任务的状态和进度
            if len(created_tasks) >= 2:
                # 第一个任务设为运行中
                task_service.update_task_status(
                    created_tasks[0].id,
                    TaskStatus.RUNNING
                )
                task_service.update_task_progress(
                    created_tasks[0].id,
                    progress=75,
                    processed_items=20,
                    success_items=18,
                    failed_items=2,
                    comments_found=45,
                    new_comments=12
                )
                
                # 第二个任务设为已完成
                task_service.update_task_status(
                    created_tasks[1].id,
                    TaskStatus.COMPLETED
                )
                task_service.update_task_progress(
                    created_tasks[1].id,
                    progress=100,
                    processed_items=30,
                    success_items=28,
                    failed_items=2,
                    comments_found=67,
                    new_comments=23
                )
            
            logger.info(f"✅ 创建了 {len(created_tasks)} 个示例任务")
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ 创建示例数据失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 爬虫任务数据表管理工具")
    print("=" * 50)
    
    # 1. 创建表
    print("1️⃣ 创建爬虫任务相关数据表")
    if not create_crawler_tables():
        print("❌ 创建表失败，程序退出")
        return
    
    # 2. 清理数据
    print("\n2️⃣ 清理现有爬虫任务数据")
    if not clear_crawler_data():
        print("❌ 清理数据失败，程序退出")
        return
    
    # 3. 创建示例数据
    print("\n3️⃣ 创建示例数据")
    if not create_sample_data():
        print("❌ 创建示例数据失败")
    
    print("\n" + "=" * 50)
    print("🎉 爬虫任务数据表管理完成！")
    print("✅ 数据表已创建并清理")
    print("✅ 示例数据已添加")
    print("✅ 现在可以测试爬虫管理页面的真实数据对接")


if __name__ == "__main__":
    main()
