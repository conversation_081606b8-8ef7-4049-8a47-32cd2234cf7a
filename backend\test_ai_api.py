#!/usr/bin/env python3
"""
测试AI功能API
"""
import requests
import json
import sys

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def login():
    """登录获取token"""
    login_data = {
        "username": "testuser2",
        "password": "testpass123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
    if response.status_code == 200:
        result = response.json()
        print(f"登录响应: {result}")
        # 检查不同的可能字段
        if "access_token" in result:
            return result["access_token"]
        elif "data" in result and "access_token" in result["data"]:
            return result["data"]["access_token"]
        elif "token" in result:
            return result["token"]
        else:
            print(f"❌ 响应中找不到token: {result}")
            return None
    else:
        print(f"❌ 登录失败: {response.status_code}")
        print(response.text)
        return None

def test_ai_templates(token):
    """测试回复模板API"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n📝 测试回复模板API")
    print("-" * 30)
    
    # 1. 获取模板列表
    print("🔍 获取模板列表...")
    response = requests.get(f"{BASE_URL}/ai/templates", headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 获取模板列表成功!")
        
        if result.get("success") and result.get("data"):
            templates = result["data"]["templates"]
            total = result["data"]["total"]
            print(f"📊 模板统计: 共 {total} 个模板")
            
            print(f"\n📋 模板列表:")
            for template in templates:
                status = "✅ 启用" if template.get("is_active") else "❌ 禁用"
                ai_flag = "🤖 AI生成" if template.get("is_ai_generated") else "👤 手动创建"
                print(f"   - ID: {template['id']}, 名称: {template['name']}")
                print(f"     分类: {template['category']}, {status} {ai_flag}")
                print(f"     使用次数: {template.get('usage_count', 0)}, 成功率: {template.get('success_rate', 0)}%")
                print(f"     内容: {template['content'][:50]}...")
                if template.get('tags'):
                    print(f"     标签: {', '.join(template['tags'])}")
                print()
        else:
            print("⚠️ 没有找到模板数据")
    else:
        print(f"❌ 获取模板列表失败: {response.text}")
    
    # 2. 创建新模板
    print("\n📝 创建新模板...")
    new_template = {
        "name": "API测试模板",
        "category": "测试",
        "content": "这是通过API创建的测试模板，感谢{user_name}的{action}！",
        "variables": {"user_name": "", "action": ""},
        "tags": ["API测试", "自动化"]
    }
    
    response = requests.post(f"{BASE_URL}/ai/templates", 
                           headers=headers, 
                           json=new_template)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 创建模板成功!")
        if result.get("success") and result.get("data"):
            template = result["data"]
            print(f"   新模板ID: {template['id']}")
            print(f"   模板名称: {template['name']}")
            return template['id']
    else:
        print(f"❌ 创建模板失败: {response.text}")
        return None

def test_ai_generate(token):
    """测试AI生成回复"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n🤖 测试AI生成回复")
    print("-" * 30)
    
    # 测试生成回复建议
    print("🔍 生成回复建议...")
    generate_data = {
        "comment_content": "谢谢分享！这个护肤心得太有用了，我也要试试看",
        "context": {
            "note_title": "美妆分享 - 夏日护肤心得",
            "note_category": "美妆护肤"
        }
    }
    
    response = requests.post(f"{BASE_URL}/ai/generate-suggestions", 
                           headers=headers, 
                           json=generate_data)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 生成回复建议成功!")
        
        if result.get("success") and result.get("data"):
            suggestions = result["data"]
            print(f"📝 生成的回复建议:")
            if isinstance(suggestions, list):
                for i, suggestion in enumerate(suggestions, 1):
                    print(f"   {i}. {suggestion}")
            else:
                print(f"   {suggestions}")
        else:
            print("⚠️ 没有生成回复建议")
    else:
        print(f"❌ 生成回复建议失败: {response.text}")

def test_ai_batch_generate(token):
    """测试批量生成回复"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n🔄 测试批量生成回复")
    print("-" * 30)
    
    # 批量生成回复
    print("🔍 批量生成回复...")
    batch_data = {
        "comments": [
            {
                "id": 5,
                "content": "谢谢分享！这个护肤心得太有用了，我也要试试看"
            },
            {
                "id": 6,
                "content": "请问这个面膜是什么牌子的？哪里可以买到？"
            },
            {
                "id": 7,
                "content": "太棒了！我的肌肤也是敏感肌，正好需要这样的护肤建议"
            }
        ]
    }
    
    response = requests.post(f"{BASE_URL}/ai/batch-generate", 
                           headers=headers, 
                           json=batch_data)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 批量生成回复成功!")
        
        if result.get("success") and result.get("data"):
            batch_results = result["data"]
            print(f"📝 批量生成结果:")
            print(f"   数据类型: {type(batch_results)}")
            print(f"   数据内容: {batch_results}")

            if isinstance(batch_results, list):
                for item in batch_results:
                    if isinstance(item, dict):
                        print(f"   留言ID: {item.get('comment_id')}")
                        print(f"   原始内容: {item.get('original_content', '')[:30]}...")
                        print(f"   生成回复: {item.get('generated_reply', '')[:50]}...")
                        print()
                    else:
                        print(f"   项目: {item}")
            else:
                print(f"   结果: {batch_results}")
        else:
            print("⚠️ 没有批量生成结果")
    else:
        print(f"❌ 批量生成回复失败: {response.text}")

def test_ai_config(token):
    """测试AI配置"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n⚙️ 测试AI配置")
    print("-" * 30)
    
    # 获取AI配置
    print("🔍 获取AI配置...")
    response = requests.get(f"{BASE_URL}/ai/config", headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 获取AI配置成功!")
        
        if result.get("success") and result.get("data"):
            config = result["data"]
            print(f"📊 AI配置信息:")
            print(f"   提供商: {config.get('provider', 'N/A')}")
            print(f"   默认模型: {config.get('default_model', 'N/A')}")
            print(f"   温度参数: {config.get('temperature', 'N/A')}")
            print(f"   最大tokens: {config.get('max_tokens', 'N/A')}")
            print(f"   回复语言: {config.get('reply_language', 'N/A')}")
            print(f"   回复语调: {config.get('reply_tone', 'N/A')}")
            print(f"   包含表情: {config.get('include_emoji', 'N/A')}")
            print(f"   内容过滤: {config.get('content_filter', 'N/A')}")
            print(f"   每日请求限制: {config.get('max_daily_requests', 'N/A')}")
            print(f"   当前每日请求: {config.get('current_daily_requests', 'N/A')}")
        else:
            print("⚠️ 没有找到AI配置")
    else:
        print(f"❌ 获取AI配置失败: {response.text}")

def main():
    """主函数"""
    print("🤖 AI功能API测试")
    print("=" * 50)
    
    # 1. 登录
    print("1️⃣ 登录测试")
    token = login()
    if not token:
        print("❌ 登录失败，无法继续测试")
        return
    
    print("✅ 登录成功!")
    
    # 2. 测试回复模板
    test_ai_templates(token)
    
    # 3. 测试AI生成回复
    test_ai_generate(token)
    
    # 4. 测试批量生成回复
    test_ai_batch_generate(token)
    
    # 5. 测试AI配置
    test_ai_config(token)
    
    print("\n🎯 测试结果总结:")
    print("✅ AI功能API测试完成")
    print("📝 回复模板管理功能正常")
    print("🤖 AI生成回复功能已就绪")
    print("⚙️ AI配置管理功能正常")

if __name__ == "__main__":
    main()
