"""Add Ollama support to AI config

Revision ID: 005_add_ollama_support
Revises: 004_add_ai_tables
Create Date: 2024-01-18 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '005_add_ollama_support'
down_revision = '004_add_ai_tables'
branch_labels = None
depends_on = None


def upgrade():
    """添加Ollama支持的字段"""
    
    # 重命名api_provider为provider
    op.alter_column('ai_configs', 'api_provider', new_column_name='provider')
    
    # 更新默认值为ollama
    op.alter_column('ai_configs', 'provider', 
                   server_default='ollama',
                   comment='AI提供商: openai, ollama, anthropic')
    
    # 添加Ollama相关字段
    op.add_column('ai_configs', 
                 sa.Column('ollama_base_url', sa.String(200), nullable=True, 
                          comment='Ollama服务地址'))
    
    op.add_column('ai_configs', 
                 sa.Column('ollama_model', sa.String(100), nullable=True, 
                          comment='Ollama模型名称'))
    
    op.add_column('ai_configs', 
                 sa.Column('ollama_timeout', sa.Integer(), nullable=True, 
                          server_default='60', comment='Ollama请求超时时间'))
    
    # 更新现有记录的默认值
    op.execute("""
        UPDATE ai_configs 
        SET ollama_base_url = 'http://localhost:11434',
            ollama_model = 'llama2',
            ollama_timeout = 60
        WHERE ollama_base_url IS NULL
    """)


def downgrade():
    """回滚Ollama支持"""
    
    # 删除Ollama相关字段
    op.drop_column('ai_configs', 'ollama_timeout')
    op.drop_column('ai_configs', 'ollama_model')
    op.drop_column('ai_configs', 'ollama_base_url')
    
    # 恢复原字段名和默认值
    op.alter_column('ai_configs', 'provider', new_column_name='api_provider')
    op.alter_column('ai_configs', 'api_provider', 
                   server_default='openai',
                   comment='API提供商')
