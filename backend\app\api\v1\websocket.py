import json
import uuid
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query
from sqlalchemy.orm import Session
from loguru import logger

from ...models import get_db
from ...services.websocket_manager import connection_manager, notification_service
from ...core.security import verify_token

router = APIRouter()


async def get_current_user_from_token(token: str, db: Session):
    """从token获取当前用户"""
    try:
        payload = verify_token(token)
        user_id = payload.get("sub")
        if user_id:
            return int(user_id)
    except Exception as e:
        logger.error(f"Token验证失败: {e}")
    return None


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    token: str = Query(...),
    db: Session = Depends(get_db)
):
    """WebSocket连接端点"""
    
    # 验证用户身份
    user_id = await get_current_user_from_token(token, db)
    if not user_id:
        await websocket.close(code=4001, reason="Unauthorized")
        return
    
    connection_id = str(uuid.uuid4())
    
    try:
        # 建立连接
        await connection_manager.connect(websocket, user_id, connection_id)
        
        # 发送在线状态
        await notification_service.send_system_notification(
            f"用户 {user_id} 已上线",
            user_ids=[user_id]
        )
        
        while True:
            try:
                # 接收客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # 处理不同类型的消息
                await handle_websocket_message(user_id, connection_id, message)
                
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Invalid JSON format"
                }))
            except Exception as e:
                logger.error(f"WebSocket消息处理错误: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Message processing error"
                }))
                
    except Exception as e:
        logger.error(f"WebSocket连接错误: {e}")
    finally:
        # 断开连接
        connection_manager.disconnect(user_id, connection_id)
        
        # 发送离线状态
        await notification_service.send_system_notification(
            f"用户 {user_id} 已离线",
            user_ids=[user_id]
        )


async def handle_websocket_message(user_id: int, connection_id: str, message: dict):
    """处理WebSocket消息"""
    
    message_type = message.get("type")
    
    if message_type == "ping":
        # 心跳检测
        await connection_manager.send_personal_message({
            "type": "pong",
            "timestamp": message.get("timestamp")
        }, user_id, connection_id)
        
    elif message_type == "subscribe":
        # 订阅特定类型的通知
        subscription_types = message.get("types", [])
        # 这里可以实现订阅逻辑
        await connection_manager.send_personal_message({
            "type": "subscription_confirmed",
            "subscribed_types": subscription_types
        }, user_id, connection_id)
        
    elif message_type == "unsubscribe":
        # 取消订阅
        unsubscribe_types = message.get("types", [])
        await connection_manager.send_personal_message({
            "type": "unsubscription_confirmed",
            "unsubscribed_types": unsubscribe_types
        }, user_id, connection_id)
        
    elif message_type == "get_status":
        # 获取连接状态
        await connection_manager.send_personal_message({
            "type": "status",
            "user_connections": connection_manager.get_user_connections(user_id),
            "total_connections": connection_manager.get_total_connections(),
            "online_users": len(connection_manager.get_online_users())
        }, user_id, connection_id)
        
    else:
        # 未知消息类型
        await connection_manager.send_personal_message({
            "type": "error",
            "message": f"Unknown message type: {message_type}"
        }, user_id, connection_id)


@router.get("/ws/stats")
async def get_websocket_stats():
    """获取WebSocket统计信息"""
    return {
        "total_connections": connection_manager.get_total_connections(),
        "online_users": len(connection_manager.get_online_users()),
        "online_user_ids": connection_manager.get_online_users()
    }


@router.post("/ws/broadcast")
async def broadcast_message(
    message: str,
    priority: str = "normal",
    user_ids: list = None
):
    """广播消息（管理员功能）"""
    count = await notification_service.send_system_notification(
        message=message,
        user_ids=user_ids,
        priority=priority
    )
    
    return {
        "success": True,
        "message": "消息已广播",
        "sent_count": count
    }


@router.post("/ws/notify/{user_id}")
async def send_notification(
    user_id: int,
    notification_type: str,
    title: str,
    message: str,
    data: dict = None,
    priority: str = "normal"
):
    """发送通知给指定用户"""
    notification = {
        "type": notification_type,
        "title": title,
        "message": message,
        "data": data or {},
        "priority": priority
    }
    
    success = await connection_manager.send_personal_message(notification, user_id)
    
    return {
        "success": success,
        "message": "通知已发送" if success else "发送失败，用户可能不在线"
    }
