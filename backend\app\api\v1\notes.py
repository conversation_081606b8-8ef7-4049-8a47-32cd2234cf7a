"""
笔记管理API路由
"""
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from ...models import get_db, User
from ...services.note_service import NoteService
from ...api.schemas.note import (
    NoteCreate,
    NoteUpdate,
    NoteResponse,
    NoteSearchFilter,
    NoteBatchOperation,
    NoteStatusEnum
)
from ...api.responses import success_response, paginated_response
from ...api.deps import get_current_active_user
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/", response_model=dict)
def create_note(
    note_data: NoteCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """创建笔记"""
    try:
        note_service = NoteService(db)
        note = note_service.create_note(current_user.id, note_data)
        
        # 构造响应数据
        note_response = NoteResponse(
            id=note.id,
            account_id=note.account_id,
            note_id=note.note_id,
            note_url=note.note_url,
            title=note.title,
            content=note.content,
            author_name=note.author_name,
            author_id=note.author_id,
            publish_time=note.publish_time,
            likes_count=note.likes_count,
            comments_count=note.comments_count,
            shares_count=note.shares_count,
            status=note.status,
            crawl_interval=note.crawl_interval,
            auto_reply_enabled=note.auto_reply_enabled,
            last_crawled=note.last_crawled,
            created_at=note.created_at,
            updated_at=note.updated_at
        )
        
        return success_response(
            data=note_response.dict(),
            message="笔记创建成功"
        ).model_dump()
        
    except HTTPException as e:
        logger.warning(f"创建笔记失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"创建笔记时发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="笔记创建失败，请稍后重试"
        )


@router.get("/", response_model=dict)
def get_notes(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    account_id: int = Query(None, description="账号ID"),
    status: NoteStatusEnum = Query(None, description="笔记状态"),
    auto_reply_enabled: bool = Query(None, description="是否启用自动回复"),
    keyword: str = Query(None, description="搜索关键词"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取笔记列表"""
    try:
        note_service = NoteService(db)
        
        # 构建过滤器
        filters = NoteSearchFilter(
            account_id=account_id,
            status=status,
            auto_reply_enabled=auto_reply_enabled,
            keyword=keyword
        )
        
        # 计算偏移量
        skip = (page - 1) * size
        
        # 获取笔记列表和总数
        notes, total = note_service.get_user_notes(current_user.id, filters, skip, size)
        
        # 构造响应数据
        notes_data = []
        for note in notes:
            note_response = NoteResponse(
                id=note.id,
                account_id=note.account_id,
                note_id=note.note_id,
                note_url=note.note_url,
                title=note.title,
                content=note.content,
                author_name=note.author_name,
                author_id=note.author_id,
                publish_time=note.publish_time,
                likes_count=note.likes_count,
                comments_count=note.comments_count,
                shares_count=note.shares_count,
                status=note.status,
                crawl_interval=note.crawl_interval,
                auto_reply_enabled=note.auto_reply_enabled,
                last_crawled=note.last_crawled,
                created_at=note.created_at,
                updated_at=note.updated_at
            )
            notes_data.append(note_response.dict())
        
        return paginated_response(
            data=notes_data,
            page=page,
            size=size,
            total=total,
            message="获取笔记列表成功"
        ).model_dump()
        
    except Exception as e:
        logger.error(f"获取笔记列表时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取笔记列表失败"
        )


@router.get("/{note_id}", response_model=dict)
def get_note(
    note_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取单个笔记详情"""
    try:
        note_service = NoteService(db)
        note = note_service.get_note_by_id(note_id, current_user.id)
        
        if not note:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="笔记不存在"
            )
        
        # 构造响应数据
        note_response = NoteResponse(
            id=note.id,
            account_id=note.account_id,
            note_id=note.note_id,
            note_url=note.note_url,
            title=note.title,
            content=note.content,
            author_name=note.author_name,
            author_id=note.author_id,
            publish_time=note.publish_time,
            likes_count=note.likes_count,
            comments_count=note.comments_count,
            shares_count=note.shares_count,
            status=note.status,
            crawl_interval=note.crawl_interval,
            auto_reply_enabled=note.auto_reply_enabled,
            last_crawled=note.last_crawled,
            created_at=note.created_at,
            updated_at=note.updated_at
        )
        
        return success_response(
            data=note_response.dict(),
            message="获取笔记详情成功"
        ).model_dump()
        
    except HTTPException as e:
        logger.warning(f"获取笔记详情失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"获取笔记详情时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取笔记详情失败"
        )


@router.put("/{note_id}", response_model=dict)
def update_note(
    note_id: int,
    note_data: NoteUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """更新笔记信息"""
    try:
        note_service = NoteService(db)
        note = note_service.update_note(note_id, current_user.id, note_data)
        
        # 构造响应数据
        note_response = NoteResponse(
            id=note.id,
            account_id=note.account_id,
            note_id=note.note_id,
            note_url=note.note_url,
            title=note.title,
            content=note.content,
            author_name=note.author_name,
            author_id=note.author_id,
            publish_time=note.publish_time,
            likes_count=note.likes_count,
            comments_count=note.comments_count,
            shares_count=note.shares_count,
            status=note.status,
            crawl_interval=note.crawl_interval,
            auto_reply_enabled=note.auto_reply_enabled,
            last_crawled=note.last_crawled,
            created_at=note.created_at,
            updated_at=note.updated_at
        )
        
        return success_response(
            data=note_response.dict(),
            message="笔记信息更新成功"
        ).model_dump()
        
    except HTTPException as e:
        logger.warning(f"更新笔记失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"更新笔记时发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="笔记更新失败，请稍后重试"
        )


@router.delete("/{note_id}", response_model=dict)
def delete_note(
    note_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """删除笔记"""
    try:
        note_service = NoteService(db)
        success = note_service.delete_note(note_id, current_user.id)
        
        if success:
            return success_response(
                data=None,
                message="笔记删除成功"
            ).model_dump()
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="笔记删除失败"
            )
            
    except HTTPException as e:
        logger.warning(f"删除笔记失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"删除笔记时发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="笔记删除失败，请稍后重试"
        )


@router.post("/batch", response_model=dict)
def batch_operation(
    operation_data: NoteBatchOperation,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """批量操作笔记"""
    try:
        note_service = NoteService(db)
        result = note_service.batch_operation(current_user.id, operation_data)
        
        return success_response(
            data=result,
            message=f"批量{operation_data.operation}操作完成"
        ).model_dump()
        
    except HTTPException as e:
        logger.warning(f"批量操作失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"批量操作时发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批量操作失败，请稍后重试"
        )


@router.get("/{note_id}/stats", response_model=dict)
def get_note_stats(
    note_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取笔记统计信息"""
    try:
        from ...services.comment_service import CommentService

        note_service = NoteService(db)
        note = note_service.get_note_by_id(note_id, current_user.id)

        if not note:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="笔记不存在"
            )

        # 获取留言统计
        comment_service = CommentService(db)
        comment_stats = comment_service.get_comment_stats(current_user.id, note_id)

        # 构造统计数据
        stats = {
            "note_id": note_id,
            "note_title": note.title,
            "note_status": note.status,
            "likes_count": note.likes_count,
            "comments_count": note.comments_count,
            "shares_count": note.shares_count,
            "last_crawled": note.last_crawled,
            "comment_stats": comment_stats
        }

        return success_response(
            data=stats,
            message="获取笔记统计成功"
        ).model_dump()

    except HTTPException as e:
        logger.warning(f"获取笔记统计失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"获取笔记统计时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取笔记统计失败"
        )
