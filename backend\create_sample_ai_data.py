#!/usr/bin/env python3
"""
创建AI功能示例数据
"""
import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.database import SessionLocal
from app.models.user import User
from app.models.reply_template import ReplyTemplate, AIConfig, AIRule

def create_sample_ai_data():
    """创建AI功能示例数据"""
    db = SessionLocal()
    
    try:
        # 查找testuser2
        test_user = db.query(User).filter(User.username == "testuser2").first()
        if not test_user:
            print("❌ 找不到testuser2用户")
            return
        
        print(f"✅ 找到testuser2用户，ID: {test_user.id}")
        
        # 1. 创建回复模板
        print("\n📝 创建回复模板...")
        
        templates_data = [
            {
                "name": "感谢类回复",
                "category": "感谢",
                "content": "谢谢你的支持！{emoji}很开心你喜欢这个内容，会继续努力分享更多有用的信息~",
                "variables": {"emoji": "😊"},
                "tags": ["感谢", "支持", "鼓励"],
                "usage_count": 15,
                "success_rate": 85,
                "is_active": True,
                "is_ai_generated": False
            },
            {
                "name": "产品推荐回复",
                "category": "推荐",
                "content": "这个产品我用了很久了，效果确实不错！{product_link}你可以参考一下，希望对你有帮助~",
                "variables": {"product_link": ""},
                "tags": ["产品", "推荐", "分享"],
                "usage_count": 8,
                "success_rate": 92,
                "is_active": True,
                "is_ai_generated": False
            },
            {
                "name": "教程解答回复",
                "category": "解答",
                "content": "关于{question}这个问题，我的经验是{answer}。如果还有疑问可以继续问我哦！",
                "variables": {"question": "", "answer": ""},
                "tags": ["教程", "解答", "帮助"],
                "usage_count": 12,
                "success_rate": 88,
                "is_active": True,
                "is_ai_generated": False
            },
            {
                "name": "AI生成通用回复",
                "category": "通用",
                "content": "感谢你的留言！我会认真考虑你的建议，继续为大家分享更好的内容。有什么想了解的可以随时告诉我~",
                "variables": {},
                "tags": ["AI生成", "通用", "友好"],
                "usage_count": 5,
                "success_rate": 75,
                "is_active": True,
                "is_ai_generated": True
            },
            {
                "name": "美妆护肤专用",
                "category": "美妆",
                "content": "关于{skin_type}肌肤的护理，我推荐{product_name}，使用方法是{usage_method}。记得要坚持使用哦！",
                "variables": {"skin_type": "", "product_name": "", "usage_method": ""},
                "tags": ["美妆", "护肤", "专业"],
                "usage_count": 20,
                "success_rate": 90,
                "is_active": True,
                "is_ai_generated": False
            }
        ]
        
        created_templates = []
        for template_data in templates_data:
            template = ReplyTemplate(
                **template_data,
                user_id=test_user.id
            )
            db.add(template)
            db.flush()  # 获取ID
            created_templates.append(template)
            print(f"   ✅ 创建模板: {template.name}")
        
        # 2. 创建AI配置
        print("\n⚙️ 创建AI配置...")
        
        ai_config = AIConfig(
            provider="ollama",
            api_key=None,
            api_base_url=None,
            ollama_base_url="http://localhost:11434",
            ollama_model="llama3.2:3b",
            ollama_timeout=30,
            default_model="llama3.2:3b",
            temperature="0.7",
            max_tokens=150,
            reply_language="zh",
            reply_tone="friendly",
            include_emoji=True,
            content_filter=True,
            max_daily_requests=1000,
            current_daily_requests=0,
            max_monthly_cost=100,
            current_monthly_cost=0,
            user_id=test_user.id
        )
        db.add(ai_config)
        print(f"   ✅ 创建AI配置: Ollama本地模型")
        
        # 3. 创建AI规则
        print("\n📋 创建AI规则...")
        
        rules_data = [
            {
                "name": "感谢类自动回复",
                "description": "检测到感谢、赞美类留言时自动回复",
                "keywords": ["谢谢", "感谢", "太棒了", "很好", "喜欢", "赞"],
                "sentiment_filter": {"positive": True},
                "user_filter": {},
                "time_filter": {},
                "template_ids": [created_templates[0].id],  # 感谢类回复模板
                "ai_prompt": "用友好、感谢的语气回复用户的赞美和感谢",
                "reply_style": "friendly",
                "priority": 1,
                "auto_reply": False,  # 需要审核
                "require_approval": True,
                "daily_limit": 50,
                "usage_count_today": 0,
                "is_active": True
            },
            {
                "name": "产品咨询自动回复",
                "description": "检测到产品咨询类留言时提供相关回复",
                "keywords": ["什么牌子", "哪里买", "多少钱", "推荐", "链接"],
                "sentiment_filter": {"neutral": True, "positive": True},
                "user_filter": {},
                "time_filter": {},
                "template_ids": [created_templates[1].id],  # 产品推荐回复模板
                "ai_prompt": "为用户提供有用的产品信息和购买建议，语气要专业友好",
                "reply_style": "professional",
                "priority": 2,
                "auto_reply": False,
                "require_approval": True,
                "daily_limit": 30,
                "usage_count_today": 0,
                "is_active": True
            },
            {
                "name": "美妆护肤专业回复",
                "description": "针对美妆护肤相关问题的专业回复",
                "keywords": ["护肤", "化妆", "美妆", "肌肤", "保养", "面膜", "精华"],
                "sentiment_filter": {},
                "user_filter": {},
                "time_filter": {},
                "template_ids": [created_templates[4].id],  # 美妆护肤专用模板
                "ai_prompt": "作为美妆护肤专家，为用户提供专业的护肤建议和产品推荐",
                "reply_style": "professional",
                "priority": 3,
                "auto_reply": False,
                "require_approval": True,
                "daily_limit": 20,
                "usage_count_today": 0,
                "is_active": True
            }
        ]
        
        for rule_data in rules_data:
            # 转换列表为JSON
            rule_data["keywords"] = json.dumps(rule_data["keywords"])
            rule_data["sentiment_filter"] = json.dumps(rule_data["sentiment_filter"])
            rule_data["user_filter"] = json.dumps(rule_data["user_filter"])
            rule_data["time_filter"] = json.dumps(rule_data["time_filter"])
            rule_data["template_ids"] = json.dumps(rule_data["template_ids"])
            
            rule = AIRule(
                **rule_data,
                user_id=test_user.id
            )
            db.add(rule)
            print(f"   ✅ 创建AI规则: {rule.name}")
        
        # 提交所有更改
        db.commit()
        
        # 验证创建结果
        print(f"\n🎯 验证创建结果:")
        
        template_count = db.query(ReplyTemplate).filter(ReplyTemplate.user_id == test_user.id).count()
        config_count = db.query(AIConfig).filter(AIConfig.user_id == test_user.id).count()
        rule_count = db.query(AIRule).filter(AIRule.user_id == test_user.id).count()
        
        print(f"   📝 回复模板: {template_count} 个")
        print(f"   ⚙️ AI配置: {config_count} 个")
        print(f"   📋 AI规则: {rule_count} 个")
        
        # 显示模板详情
        templates = db.query(ReplyTemplate).filter(ReplyTemplate.user_id == test_user.id).all()
        print(f"\n📝 模板列表:")
        for template in templates:
            status = "✅ 启用" if template.is_active else "❌ 禁用"
            ai_flag = "🤖 AI生成" if template.is_ai_generated else "👤 手动创建"
            print(f"   - {template.name} ({template.category}) {status} {ai_flag}")
            print(f"     使用次数: {template.usage_count}, 成功率: {template.success_rate}%")
        
        print(f"\n🎉 AI功能示例数据创建完成！")
        
    except Exception as e:
        print(f"❌ 创建示例数据失败: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("🤖 创建AI功能示例数据")
    print("=" * 50)
    create_sample_ai_data()
