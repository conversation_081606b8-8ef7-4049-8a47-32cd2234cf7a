<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI配置管理页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .tabs {
            display: flex;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 30px;
        }
        
        .tab {
            padding: 15px 25px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
            font-weight: bold;
        }
        
        .tab:hover {
            color: #667eea;
            background: #f8f9ff;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .section {
            background: #f8f9ff;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        
        .section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            align-items: end;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        
        input[type="text"], input[type="number"], select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        input[type="text"]:focus, input[type="number"]:focus, select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }
        
        .status {
            padding: 12px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 600;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .model-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .model-card {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .model-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .model-card.selected {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .model-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .model-details {
            color: #666;
            font-size: 0.9em;
            line-height: 1.5;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #667eea;
        }
        
        .loading.show {
            display: block;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none !important;
        }
        
        .config-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .config-summary h4 {
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .config-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .config-item:last-child {
            border-bottom: none;
        }

        /* 统计页面样式 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-card {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .performance-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }

        .model-name {
            width: 200px;
            font-weight: 600;
            color: #333;
        }

        .performance-bar {
            flex: 1;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            margin: 0 15px;
            overflow: hidden;
        }

        .performance-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .performance-score {
            width: 60px;
            text-align: right;
            font-weight: bold;
            color: #667eea;
        }

        .limit-info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e0e0e0;
        }

        .limit-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .limit-progress {
            margin-top: 15px;
        }

        .progress-bar {
            width: 100%;
            height: 25px;
            background: #f0f0f0;
            border-radius: 12px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-radius: 12px;
            transition: width 0.3s ease;
        }

        /* 批量测试结果样式 */
        .batch-result-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .batch-result-item.success {
            border-left: 4px solid #28a745;
        }

        .batch-result-item.error {
            border-left: 4px solid #dc3545;
        }

        .result-comment {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .result-reply {
            color: #666;
            font-style: italic;
            margin-bottom: 8px;
        }

        .result-meta {
            font-size: 0.8em;
            color: #999;
            display: flex;
            justify-content: space-between;
        }

        /* 历史记录样式 */
        .history-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .history-time {
            color: #666;
            font-size: 0.9em;
        }

        .history-model {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
        }

        .history-content {
            border-top: 1px solid #f0f0f0;
            padding-top: 10px;
        }

        .history-comment {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .history-reply {
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI配置管理</h1>
            <p>配置和管理您的AI回复助手</p>
        </div>
        
        <div class="content">
            <!-- 登录状态 -->
            <div id="loginSection" class="section">
                <h3>🔐 用户登录</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" value="testuser2">
                    </div>
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="text" id="password" value="testpass123">
                    </div>
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <button class="btn btn-primary" onclick="login()">登录</button>
                    </div>
                </div>
                <div id="loginStatus"></div>
            </div>
            
            <!-- 当前配置概览 -->
            <div id="configOverview" class="config-summary hidden">
                <h4>📊 当前AI配置</h4>
                <div id="configSummary"></div>
            </div>
            
            <!-- 标签页 -->
            <div class="tabs">
                <button class="tab active" onclick="showTab('ollama')">🏠 Ollama配置</button>
                <button class="tab" onclick="showTab('openai')">☁️ OpenAI配置</button>
                <button class="tab" onclick="showTab('templates')">📝 回复模板</button>
                <button class="tab" onclick="showTab('editor')">✏️ 模板编辑器</button>
                <button class="tab" onclick="showTab('statistics')">📊 使用统计</button>
                <button class="tab" onclick="showTab('batch')">🔄 批量测试</button>
                <button class="tab" onclick="showTab('history')">📋 回复历史</button>
                <button class="tab" onclick="showTab('test')">🧪 功能测试</button>
            </div>
            
            <!-- Ollama配置 -->
            <div id="ollama" class="tab-content active">
                <div class="section">
                    <h3>🔗 Ollama连接设置</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="ollamaUrl">Ollama服务地址</label>
                            <input type="text" id="ollamaUrl" value="http://192.168.1.98:11434">
                        </div>
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button class="btn btn-primary" onclick="testOllamaConnection()">测试连接</button>
                        </div>
                    </div>
                    <div id="connectionStatus"></div>
                </div>
                
                <div class="section">
                    <h3>🤖 模型选择</h3>
                    <button class="btn btn-secondary" onclick="loadOllamaModels()">获取可用模型</button>
                    <div class="loading" id="modelsLoading">
                        <div class="spinner"></div>
                        正在获取模型列表...
                    </div>
                    <div id="modelsGrid" class="model-grid"></div>
                </div>
                
                <div class="section">
                    <h3>⚙️ 高级设置</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="temperature">温度 (0.0-2.0)</label>
                            <input type="number" id="temperature" min="0" max="2" step="0.1" value="0.7">
                        </div>
                        <div class="form-group">
                            <label for="maxTokens">最大Token数</label>
                            <input type="number" id="maxTokens" min="1" max="4000" value="150">
                        </div>
                        <div class="form-group">
                            <label for="replyTone">回复语调</label>
                            <select id="replyTone">
                                <option value="friendly">友好</option>
                                <option value="professional">专业</option>
                                <option value="casual">随意</option>
                                <option value="enthusiastic">热情</option>
                            </select>
                        </div>
                    </div>
                    <button class="btn btn-success" onclick="saveOllamaConfig()" id="saveConfigBtn" disabled>保存配置</button>
                    <div id="saveStatus"></div>
                </div>
            </div>
            
            <!-- OpenAI配置 -->
            <div id="openai" class="tab-content">
                <div class="section">
                    <h3>🔑 OpenAI API设置</h3>
                    <div class="form-group">
                        <label for="openaiKey">API密钥</label>
                        <input type="text" id="openaiKey" placeholder="sk-...">
                    </div>
                    <div class="form-group">
                        <label for="openaiModel">模型</label>
                        <select id="openaiModel">
                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                            <option value="gpt-4">GPT-4</option>
                            <option value="gpt-4-turbo">GPT-4 Turbo</option>
                        </select>
                    </div>
                    <button class="btn btn-success" onclick="saveOpenAIConfig()">保存OpenAI配置</button>
                </div>
            </div>
            
            <!-- 回复模板 -->
            <div id="templates" class="tab-content">
                <div class="section">
                    <h3>📝 回复模板管理</h3>
                    <button class="btn btn-primary" onclick="loadTemplates()">刷新模板列表</button>
                    <div id="templatesList"></div>
                </div>
            </div>

            <!-- 模板编辑器 -->
            <div id="editor" class="tab-content">
                <div class="section">
                    <h3>✏️ 创建新模板</h3>
                    <div class="form-group">
                        <label for="templateName">模板名称</label>
                        <input type="text" id="templateName" placeholder="输入模板名称">
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="templateCategory">分类</label>
                            <select id="templateCategory">
                                <option value="greeting">问候</option>
                                <option value="product">产品咨询</option>
                                <option value="service">服务支持</option>
                                <option value="promotion">促销活动</option>
                                <option value="thanks">感谢回复</option>
                                <option value="custom">自定义</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="templateTags">标签 (用逗号分隔)</label>
                            <input type="text" id="templateTags" placeholder="美妆,护肤,推荐">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="templateContent">模板内容</label>
                        <textarea id="templateContent" rows="4" style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 14px; resize: vertical;" placeholder="输入模板内容，可以使用变量如 {user_name}, {product_name} 等"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="templateVariables">可用变量 (JSON格式)</label>
                        <textarea id="templateVariables" rows="3" style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 14px; resize: vertical;" placeholder='{"user_name": "用户名", "product_name": "产品名称"}'></textarea>
                    </div>
                    <div class="form-row">
                        <button class="btn btn-success" onclick="createTemplate()">创建模板</button>
                        <button class="btn btn-secondary" onclick="clearTemplateForm()">清空表单</button>
                        <button class="btn btn-warning" onclick="previewTemplate()">预览模板</button>
                    </div>
                    <div id="templateCreateStatus"></div>
                </div>

                <div class="section">
                    <h3>📋 模板预览</h3>
                    <div id="templatePreview" style="background: #f8f9ff; padding: 15px; border-radius: 8px; border: 1px solid #e0e0e0; min-height: 60px;">
                        <em style="color: #666;">在上方编辑模板内容，然后点击"预览模板"查看效果</em>
                    </div>
                </div>

                <div class="section">
                    <h3>🔧 编辑现有模板</h3>
                    <div class="form-group">
                        <label for="editTemplateSelect">选择要编辑的模板</label>
                        <select id="editTemplateSelect" onchange="loadTemplateForEdit()">
                            <option value="">请选择模板</option>
                        </select>
                    </div>
                    <div id="editTemplateForm" class="hidden">
                        <div class="form-group">
                            <label for="editTemplateName">模板名称</label>
                            <input type="text" id="editTemplateName">
                        </div>
                        <div class="form-group">
                            <label for="editTemplateContent">模板内容</label>
                            <textarea id="editTemplateContent" rows="4" style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 14px; resize: vertical;"></textarea>
                        </div>
                        <div class="form-row">
                            <button class="btn btn-success" onclick="updateTemplate()">更新模板</button>
                            <button class="btn btn-warning" onclick="deleteTemplate()">删除模板</button>
                        </div>
                    </div>
                    <div id="templateEditStatus"></div>
                </div>
            </div>

            <!-- 使用统计 -->
            <div id="statistics" class="tab-content">
                <div class="section">
                    <h3>📊 AI使用统计</h3>
                    <button class="btn btn-primary" onclick="loadStatistics()">刷新统计数据</button>
                    <div class="loading" id="statisticsLoading">
                        <div class="spinner"></div>
                        正在加载统计数据...
                    </div>
                </div>

                <div class="section">
                    <h3>📈 今日使用情况</h3>
                    <div id="todayStats" class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="todayRequests">-</div>
                            <div class="stat-label">今日请求数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="todaySuccess">-</div>
                            <div class="stat-label">成功率</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="todayTokens">-</div>
                            <div class="stat-label">Token使用量</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="todayCost">-</div>
                            <div class="stat-label">今日成本</div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h3>📅 本月统计</h3>
                    <div id="monthStats" class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="monthRequests">-</div>
                            <div class="stat-label">本月请求数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="monthSuccess">-</div>
                            <div class="stat-label">平均成功率</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="monthTokens">-</div>
                            <div class="stat-label">Token总量</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="monthCost">-</div>
                            <div class="stat-label">本月成本</div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h3>🏆 模型性能对比</h3>
                    <div id="modelPerformance">
                        <div class="performance-item">
                            <div class="model-name">Ollama (qwen3:30b-a3b)</div>
                            <div class="performance-bar">
                                <div class="performance-fill" style="width: 85%"></div>
                            </div>
                            <div class="performance-score">85%</div>
                        </div>
                        <div class="performance-item">
                            <div class="model-name">OpenAI (gpt-3.5-turbo)</div>
                            <div class="performance-bar">
                                <div class="performance-fill" style="width: 92%"></div>
                            </div>
                            <div class="performance-score">92%</div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h3>⚠️ 使用限制</h3>
                    <div class="limit-info">
                        <div class="limit-item">
                            <span>每日请求限制:</span>
                            <span id="dailyLimit">1000</span>
                        </div>
                        <div class="limit-item">
                            <span>已使用:</span>
                            <span id="dailyUsed">0</span>
                        </div>
                        <div class="limit-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" id="dailyProgress" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 批量测试 -->
            <div id="batch" class="tab-content">
                <div class="section">
                    <h3>🔄 批量AI回复测试</h3>
                    <div class="form-group">
                        <label for="batchComments">测试评论列表 (每行一个评论)</label>
                        <textarea id="batchComments" rows="8" style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 14px; resize: vertical;" placeholder="这个产品效果怎么样？&#10;价格有点贵，有优惠吗？&#10;谢谢分享，很有用！&#10;什么时候有新品？&#10;可以推荐类似的产品吗？"></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="batchContext">上下文信息 (JSON格式)</label>
                            <input type="text" id="batchContext" value='{"note_category": "美妆", "note_title": "护肤分享"}'>
                        </div>
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button class="btn btn-primary" onclick="runBatchTest()">开始批量测试</button>
                        </div>
                    </div>
                    <div class="loading" id="batchLoading">
                        <div class="spinner"></div>
                        正在批量生成AI回复...
                    </div>
                    <div id="batchResults"></div>
                </div>
            </div>

            <!-- 回复历史 -->
            <div id="history" class="tab-content">
                <div class="section">
                    <h3>📋 AI回复历史</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="historyFilter">筛选条件</label>
                            <select id="historyFilter">
                                <option value="all">全部</option>
                                <option value="today">今天</option>
                                <option value="week">本周</option>
                                <option value="month">本月</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="historyModel">模型筛选</label>
                            <select id="historyModel">
                                <option value="all">全部模型</option>
                                <option value="ollama">Ollama</option>
                                <option value="openai">OpenAI</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button class="btn btn-primary" onclick="loadHistory()">加载历史记录</button>
                        </div>
                    </div>
                    <div class="loading" id="historyLoading">
                        <div class="spinner"></div>
                        正在加载历史记录...
                    </div>
                    <div id="historyList"></div>
                </div>
            </div>

            <!-- 功能测试 -->
            <div id="test" class="tab-content">
                <div class="section">
                    <h3>🧪 AI回复测试</h3>
                    <div class="form-group">
                        <label for="testComment">测试评论内容</label>
                        <input type="text" id="testComment" value="这个产品看起来不错，请问有什么优惠吗？" placeholder="输入要测试的评论内容">
                    </div>
                    <button class="btn btn-primary" onclick="testAIReply()">生成AI回复</button>
                    <div id="testResult"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        let authToken = '';
        let selectedModel = '';
        let currentConfig = {};

        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的active类
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');
            
            // 添加active类到对应的标签
            event.target.classList.add('active');
        }

        // 登录功能
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success && result.data.access_token) {
                    authToken = result.data.access_token;
                    document.getElementById('loginStatus').innerHTML = 
                        '<div class="status success">✅ 登录成功！欢迎 ' + result.data.user.full_name + '</div>';
                    
                    // 加载当前配置
                    await loadCurrentConfig();
                } else {
                    document.getElementById('loginStatus').innerHTML = 
                        '<div class="status error">❌ 登录失败：' + result.message + '</div>';
                }
            } catch (error) {
                document.getElementById('loginStatus').innerHTML = 
                    '<div class="status error">❌ 登录错误：' + error.message + '</div>';
            }
        }

        // 加载当前配置
        async function loadCurrentConfig() {
            if (!authToken) return;
            
            try {
                const response = await fetch(`${API_BASE}/comments/ai/config`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                const result = await response.json();
                
                if (result.success && result.data) {
                    currentConfig = result.data;
                    updateConfigOverview();
                    
                    // 更新表单值
                    if (currentConfig.ollama_base_url) {
                        document.getElementById('ollamaUrl').value = currentConfig.ollama_base_url;
                    }
                    if (currentConfig.temperature) {
                        document.getElementById('temperature').value = currentConfig.temperature;
                    }
                    if (currentConfig.max_tokens) {
                        document.getElementById('maxTokens').value = currentConfig.max_tokens;
                    }
                    if (currentConfig.reply_tone) {
                        document.getElementById('replyTone').value = currentConfig.reply_tone;
                    }
                }
            } catch (error) {
                console.error('加载配置失败:', error);
            }
        }

        // 更新配置概览
        function updateConfigOverview() {
            const overview = document.getElementById('configOverview');
            const summary = document.getElementById('configSummary');
            
            if (Object.keys(currentConfig).length > 0) {
                overview.classList.remove('hidden');
                
                summary.innerHTML = `
                    <div class="config-item">
                        <span>AI提供商:</span>
                        <span>${currentConfig.provider || 'openai'}</span>
                    </div>
                    <div class="config-item">
                        <span>当前模型:</span>
                        <span>${currentConfig.ollama_model || currentConfig.default_model || 'gpt-3.5-turbo'}</span>
                    </div>
                    <div class="config-item">
                        <span>温度参数:</span>
                        <span>${currentConfig.temperature || 0.7}</span>
                    </div>
                    <div class="config-item">
                        <span>最大Token:</span>
                        <span>${currentConfig.max_tokens || 150}</span>
                    </div>
                    <div class="config-item">
                        <span>回复语调:</span>
                        <span>${currentConfig.reply_tone || 'friendly'}</span>
                    </div>
                `;
            }
        }

        // 测试Ollama连接
        async function testOllamaConnection() {
            if (!authToken) {
                alert('请先登录');
                return;
            }
            
            const ollamaUrl = document.getElementById('ollamaUrl').value;
            
            try {
                const response = await fetch(`${API_BASE}/comments/ai/ollama/test-connection`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ base_url: ollamaUrl })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('connectionStatus').innerHTML = 
                        '<div class="status success">✅ 连接成功！版本: ' + result.data.version + '</div>';
                } else {
                    document.getElementById('connectionStatus').innerHTML = 
                        '<div class="status error">❌ 连接失败：' + result.message + '</div>';
                }
            } catch (error) {
                document.getElementById('connectionStatus').innerHTML = 
                    '<div class="status error">❌ 连接错误：' + error.message + '</div>';
            }
        }

        // 加载Ollama模型
        async function loadOllamaModels() {
            if (!authToken) {
                alert('请先登录');
                return;
            }
            
            const ollamaUrl = document.getElementById('ollamaUrl').value;
            const loading = document.getElementById('modelsLoading');
            const grid = document.getElementById('modelsGrid');
            
            loading.classList.add('show');
            grid.innerHTML = '';
            
            try {
                const response = await fetch(`${API_BASE}/comments/ai/ollama/models?base_url=${encodeURIComponent(ollamaUrl)}`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                const result = await response.json();
                
                if (result.success && result.data.models) {
                    result.data.models.forEach(model => {
                        const card = document.createElement('div');
                        card.className = 'model-card';
                        card.onclick = () => selectModel(model.name, card);
                        
                        card.innerHTML = `
                            <div class="model-name">${model.name}</div>
                            <div class="model-details">
                                <div>大小: ${model.size_gb} GB</div>
                                <div>格式: ${model.details.format || 'N/A'}</div>
                                <div>参数: ${model.details.parameter_size || 'N/A'}</div>
                                <div>修改时间: ${new Date(model.modified_at).toLocaleDateString()}</div>
                            </div>
                        `;
                        
                        grid.appendChild(card);
                    });
                } else {
                    grid.innerHTML = '<div class="status error">❌ 获取模型失败：' + result.message + '</div>';
                }
            } catch (error) {
                grid.innerHTML = '<div class="status error">❌ 获取模型错误：' + error.message + '</div>';
            } finally {
                loading.classList.remove('show');
            }
        }

        // 选择模型
        function selectModel(modelName, cardElement) {
            // 移除其他卡片的选中状态
            document.querySelectorAll('.model-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // 选中当前卡片
            cardElement.classList.add('selected');
            selectedModel = modelName;
            
            // 启用保存按钮
            document.getElementById('saveConfigBtn').disabled = false;
        }

        // 保存Ollama配置
        async function saveOllamaConfig() {
            if (!authToken || !selectedModel) {
                alert('请先登录并选择模型');
                return;
            }
            
            const ollamaUrl = document.getElementById('ollamaUrl').value;
            const temperature = parseFloat(document.getElementById('temperature').value);
            const maxTokens = parseInt(document.getElementById('maxTokens').value);
            const replyTone = document.getElementById('replyTone').value;
            
            try {
                // 保存Ollama配置
                const ollamaResponse = await fetch(`${API_BASE}/comments/ai/ollama/save-config`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        base_url: ollamaUrl,
                        model: selectedModel
                    })
                });
                
                // 更新高级设置
                const configResponse = await fetch(`${API_BASE}/comments/ai/config`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        temperature: temperature,
                        max_tokens: maxTokens,
                        reply_tone: replyTone
                    })
                });
                
                if (ollamaResponse.ok && configResponse.ok) {
                    document.getElementById('saveStatus').innerHTML = 
                        '<div class="status success">✅ 配置保存成功！</div>';
                    
                    // 重新加载配置
                    await loadCurrentConfig();
                } else {
                    document.getElementById('saveStatus').innerHTML = 
                        '<div class="status error">❌ 配置保存失败</div>';
                }
            } catch (error) {
                document.getElementById('saveStatus').innerHTML = 
                    '<div class="status error">❌ 保存错误：' + error.message + '</div>';
            }
        }

        // 测试AI回复
        async function testAIReply() {
            if (!authToken) {
                alert('请先登录');
                return;
            }
            
            const comment = document.getElementById('testComment').value;
            
            try {
                const response = await fetch(`${API_BASE}/comments/ai/generate-reply`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        comment_content: comment,
                        context: { note_category: '美妆' }
                    })
                });
                
                const result = await response.json();
                
                if (result.success && result.data) {
                    document.getElementById('testResult').innerHTML = `
                        <div class="status success">
                            <h4>✅ AI回复生成成功</h4>
                            <p><strong>原评论:</strong> ${comment}</p>
                            <p><strong>AI回复:</strong> ${result.data.reply}</p>
                            <p><strong>使用模型:</strong> ${result.data.model || '未知'}</p>
                            <p><strong>置信度:</strong> ${result.data.confidence_score || 'N/A'}</p>
                        </div>
                    `;
                } else {
                    document.getElementById('testResult').innerHTML = 
                        '<div class="status error">❌ 生成失败：' + result.message + '</div>';
                }
            } catch (error) {
                document.getElementById('testResult').innerHTML = 
                    '<div class="status error">❌ 测试错误：' + error.message + '</div>';
            }
        }

        // 加载回复模板
        async function loadTemplates() {
            if (!authToken) {
                alert('请先登录');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/comments/ai/templates`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                const result = await response.json();
                
                if (result.success && result.data.templates) {
                    const list = document.getElementById('templatesList');
                    list.innerHTML = `
                        <div class="status info">
                            📊 共找到 ${result.data.total} 个模板
                        </div>
                    `;
                    
                    result.data.templates.forEach(template => {
                        const item = document.createElement('div');
                        item.className = 'section';
                        item.innerHTML = `
                            <h4>${template.name}</h4>
                            <p><strong>分类:</strong> ${template.category}</p>
                            <p><strong>内容:</strong> ${template.content}</p>
                            <p><strong>使用次数:</strong> ${template.usage_count}</p>
                            <p><strong>成功率:</strong> ${template.success_rate}%</p>
                        `;
                        list.appendChild(item);
                    });
                } else {
                    document.getElementById('templatesList').innerHTML = 
                        '<div class="status error">❌ 加载模板失败：' + result.message + '</div>';
                }
            } catch (error) {
                document.getElementById('templatesList').innerHTML = 
                    '<div class="status error">❌ 加载错误：' + error.message + '</div>';
            }
        }

        // 创建模板
        async function createTemplate() {
            if (!authToken) {
                alert('请先登录');
                return;
            }

            const name = document.getElementById('templateName').value;
            const category = document.getElementById('templateCategory').value;
            const content = document.getElementById('templateContent').value;
            const tags = document.getElementById('templateTags').value;
            const variables = document.getElementById('templateVariables').value;

            if (!name || !content) {
                alert('请填写模板名称和内容');
                return;
            }

            try {
                let parsedVariables = {};
                if (variables) {
                    parsedVariables = JSON.parse(variables);
                }

                const response = await fetch(`${API_BASE}/comments/ai/templates`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: name,
                        content: content,
                        category: category,
                        tags: tags.split(',').map(t => t.trim()).filter(t => t),
                        variables: parsedVariables
                    })
                });

                if (response.ok) {
                    document.getElementById('templateCreateStatus').innerHTML =
                        '<div class="status success">✅ 模板创建成功！</div>';
                    clearTemplateForm();
                    loadTemplatesForEdit();
                } else {
                    document.getElementById('templateCreateStatus').innerHTML =
                        '<div class="status error">❌ 模板创建失败</div>';
                }
            } catch (error) {
                document.getElementById('templateCreateStatus').innerHTML =
                    '<div class="status error">❌ 创建错误：' + error.message + '</div>';
            }
        }

        // 清空模板表单
        function clearTemplateForm() {
            document.getElementById('templateName').value = '';
            document.getElementById('templateContent').value = '';
            document.getElementById('templateTags').value = '';
            document.getElementById('templateVariables').value = '';
            document.getElementById('templateCategory').value = 'greeting';
        }

        // 预览模板
        function previewTemplate() {
            const content = document.getElementById('templateContent').value;
            const variables = document.getElementById('templateVariables').value;

            let preview = content;

            try {
                if (variables) {
                    const vars = JSON.parse(variables);
                    Object.keys(vars).forEach(key => {
                        const regex = new RegExp(`{${key}}`, 'g');
                        preview = preview.replace(regex, `<span style="background: #fff3cd; padding: 2px 4px; border-radius: 3px;">${vars[key]}</span>`);
                    });
                }
            } catch (error) {
                preview += '<br><span style="color: red;">变量格式错误</span>';
            }

            document.getElementById('templatePreview').innerHTML = preview || '<em style="color: #666;">请输入模板内容</em>';
        }

        // 加载模板用于编辑
        async function loadTemplatesForEdit() {
            if (!authToken) return;

            try {
                const response = await fetch(`${API_BASE}/comments/ai/templates`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                const result = await response.json();

                if (result.success && result.data.templates) {
                    const select = document.getElementById('editTemplateSelect');
                    select.innerHTML = '<option value="">请选择模板</option>';

                    result.data.templates.forEach(template => {
                        const option = document.createElement('option');
                        option.value = template.id;
                        option.textContent = template.name;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载模板失败:', error);
            }
        }

        // 加载选中的模板进行编辑
        function loadTemplateForEdit() {
            const templateId = document.getElementById('editTemplateSelect').value;

            if (!templateId) {
                document.getElementById('editTemplateForm').classList.add('hidden');
                return;
            }

            // 这里应该调用API获取模板详情，暂时使用模拟数据
            document.getElementById('editTemplateForm').classList.remove('hidden');
            document.getElementById('editTemplateName').value = '示例模板';
            document.getElementById('editTemplateContent').value = '感谢您的{action}，{user_name}！';
        }

        // 更新模板
        async function updateTemplate() {
            alert('模板更新功能开发中...');
        }

        // 删除模板
        async function deleteTemplate() {
            if (confirm('确定要删除这个模板吗？')) {
                alert('模板删除功能开发中...');
            }
        }

        // 加载使用统计
        async function loadStatistics() {
            if (!authToken) {
                alert('请先登录');
                return;
            }

            const loading = document.getElementById('statisticsLoading');
            loading.classList.add('show');

            try {
                const response = await fetch(`${API_BASE}/comments/ai/statistics`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                const result = await response.json();

                if (result.success && result.data) {
                    const data = result.data;

                    // 更新今日统计
                    document.getElementById('todayRequests').textContent = data.today.requests;
                    document.getElementById('todaySuccess').textContent = data.today.success_rate + '%';
                    document.getElementById('todayTokens').textContent = data.today.tokens_used.toLocaleString();
                    document.getElementById('todayCost').textContent = '¥' + data.today.cost;

                    // 更新本月统计
                    document.getElementById('monthRequests').textContent = data.month.requests;
                    document.getElementById('monthSuccess').textContent = data.month.success_rate + '%';
                    document.getElementById('monthTokens').textContent = data.month.tokens_used.toLocaleString();
                    document.getElementById('monthCost').textContent = '¥' + data.month.cost;

                    // 更新使用限制
                    document.getElementById('dailyLimit').textContent = data.limits.daily_limit;
                    document.getElementById('dailyUsed').textContent = data.limits.daily_used;
                    document.getElementById('dailyProgress').style.width = data.limits.usage_percentage + '%';

                } else {
                    console.error('获取统计数据失败:', result.message);
                }

            } catch (error) {
                console.error('加载统计失败:', error);
            } finally {
                loading.classList.remove('show');
            }
        }

        // 批量测试
        async function runBatchTest() {
            if (!authToken) {
                alert('请先登录');
                return;
            }

            const comments = document.getElementById('batchComments').value.split('\n').filter(c => c.trim());
            const contextStr = document.getElementById('batchContext').value;

            if (comments.length === 0) {
                alert('请输入要测试的评论');
                return;
            }

            let context = {};
            try {
                context = JSON.parse(contextStr);
            } catch (error) {
                alert('上下文格式错误，请使用正确的JSON格式');
                return;
            }

            const loading = document.getElementById('batchLoading');
            const results = document.getElementById('batchResults');

            loading.classList.add('show');
            results.innerHTML = '';

            try {
                const commentsData = comments.map((content, index) => ({
                    id: index + 1,
                    content: content,
                    context: context
                }));

                const response = await fetch(`${API_BASE}/comments/ai/batch-generate`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ comments: commentsData })
                });

                const result = await response.json();

                if (result.success && result.data.results) {
                    results.innerHTML = `
                        <div class="status info">
                            📊 批量测试完成：成功 ${result.data.success_count}，失败 ${result.data.failed_count}
                        </div>
                    `;

                    result.data.results.forEach(item => {
                        const resultDiv = document.createElement('div');
                        resultDiv.className = `batch-result-item ${item.result.success ? 'success' : 'error'}`;

                        resultDiv.innerHTML = `
                            <div class="result-comment">评论 ${item.comment_id}: ${comments[item.comment_id - 1]}</div>
                            <div class="result-reply">${item.result.success ? item.result.reply : item.result.error}</div>
                            <div class="result-meta">
                                <span>模型: ${item.result.model || 'N/A'}</span>
                                <span>状态: ${item.result.success ? '成功' : '失败'}</span>
                            </div>
                        `;

                        results.appendChild(resultDiv);
                    });
                } else {
                    results.innerHTML = '<div class="status error">❌ 批量测试失败：' + result.message + '</div>';
                }
            } catch (error) {
                results.innerHTML = '<div class="status error">❌ 批量测试错误：' + error.message + '</div>';
            } finally {
                loading.classList.remove('show');
            }
        }

        // 加载回复历史
        async function loadHistory() {
            if (!authToken) {
                alert('请先登录');
                return;
            }

            const loading = document.getElementById('historyLoading');
            const list = document.getElementById('historyList');
            const filter = document.getElementById('historyFilter').value;
            const modelFilter = document.getElementById('historyModel').value;

            loading.classList.add('show');
            list.innerHTML = '';

            try {
                let url = `${API_BASE}/comments/ai/history?page=1&size=20`;
                if (filter !== 'all') {
                    url += `&date_filter=${filter}`;
                }
                if (modelFilter !== 'all') {
                    url += `&model_filter=${modelFilter}`;
                }

                const response = await fetch(url, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                const result = await response.json();

                if (result.success && result.data) {
                    const history = result.data;
                    const total = result.pagination?.total || 0;

                    list.innerHTML = `<div class="status info">📋 共找到 ${total} 条历史记录</div>`;

                    if (history.length > 0) {
                        history.forEach(item => {
                            const historyDiv = document.createElement('div');
                            historyDiv.className = 'history-item';

                            const time = new Date(item.created_at).toLocaleString();
                            const model = item.model_used || 'Unknown';

                            historyDiv.innerHTML = `
                                <div class="history-header">
                                    <span class="history-time">${time}</span>
                                    <span class="history-model">${model}</span>
                                </div>
                                <div class="history-content">
                                    <div class="history-comment">原评论: ${item.comment_content}</div>
                                    <div class="history-reply">AI回复: ${item.generated_reply}</div>
                                    ${item.tokens_used ? `<div style="font-size: 0.8em; color: #999; margin-top: 5px;">Token使用: ${item.tokens_used}</div>` : ''}
                                </div>
                            `;

                            list.appendChild(historyDiv);
                        });
                    } else {
                        list.innerHTML += '<div class="status info">暂无历史记录</div>';
                    }
                } else {
                    list.innerHTML = '<div class="status error">❌ 加载历史失败：' + result.message + '</div>';
                }

            } catch (error) {
                list.innerHTML = '<div class="status error">❌ 加载历史失败：' + error.message + '</div>';
            } finally {
                loading.classList.remove('show');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AI配置页面已加载');
            // 加载模板列表用于编辑
            setTimeout(() => {
                if (authToken) {
                    loadTemplatesForEdit();
                }
            }, 1000);
        });
    </script>
</body>
</html>
