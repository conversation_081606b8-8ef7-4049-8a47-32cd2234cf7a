import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Switch,
  Select,
  InputNumber,
  message,
  Typography,
  Divider,
  Space,
  Alert,
  Tabs,
  Upload,
  Avatar,
  Modal,
  List,
  Tag,
  Row,
  Col,
  Progress,
  Tooltip,
  Badge,
  Popconfirm,
  Statistic,
  Radio,
} from 'antd';
import {
  UserOutlined,
  SettingOutlined,
  BellOutlined,
  SecurityScanOutlined,
  ExportOutlined,
  ImportOutlined,
  UploadOutlined,
  SaveOutlined,
  ReloadOutlined,
  DeleteOutlined,
  DownloadOutlined,
  EditOutlined,
  CameraOutlined,
  LockOutlined,
  MailOutlined,
  PhoneOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  SunOutlined,
  MoonOutlined,
  BgColorsOutlined,
} from '@ant-design/icons';
import { useThemeStore } from '../../stores/themeStore';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 模拟的用户状态管理
const useAuthStore = () => ({
  user: {
    username: 'testuser',
    email: '<EMAIL>',
    full_name: 'Test User',
  }
});

const SettingsPage = () => {
  const [loading, setLoading] = useState(false);
  const [profileForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [systemForm] = Form.useForm();
  const { user } = useAuthStore();
  const { currentTheme, isDark, setTheme, toggleTheme, themes } = useThemeStore();
  
  // 状态管理
  const [avatarUrl, setAvatarUrl] = useState('');
  const [uploading, setUploading] = useState(false);
  const [changePasswordVisible, setChangePasswordVisible] = useState(false);
  const [profileStats] = useState({
    completeness: 75,
    lastLogin: '2024-01-18 10:30:00',
    accountAge: 365,
    totalLogins: 1247,
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = () => {
    // 加载用户设置
    profileForm.setFieldsValue({
      username: user?.username || 'testuser',
      email: user?.email || '<EMAIL>',
      full_name: user?.full_name || 'Test User',
      phone: '+86 138****8888',
      bio: '这是一个测试用户的个人简介，可以在这里介绍自己的兴趣爱好和专业背景。',
      location: '上海市',
      website: 'https://example.com',
      birthday: '1990-01-01',
      gender: 'male',
      occupation: '产品经理',
      company: '某科技公司',
    });
  };

  // 保存个人资料
  const handleSaveProfile = async (values) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('个人资料保存成功');
    } catch (error) {
      message.error('保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 头像上传
  const handleAvatarUpload = async (file) => {
    setUploading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      const newAvatarUrl = URL.createObjectURL(file);
      setAvatarUrl(newAvatarUrl);
      message.success('头像上传成功');
      return false;
    } catch (error) {
      message.error('头像上传失败');
    } finally {
      setUploading(false);
    }
  };

  // 修改密码
  const handleChangePassword = async (values) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('密码修改成功');
      setChangePasswordVisible(false);
      passwordForm.resetFields();
    } catch (error) {
      message.error('密码修改失败');
    } finally {
      setLoading(false);
    }
  };

  // 导出个人数据
  const handleExportData = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      message.success('个人数据导出成功，请检查下载文件');
    } catch (error) {
      message.error('导出失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除账号
  const handleDeleteAccount = async () => {
    Modal.confirm({
      title: '确认删除账号',
      content: '删除账号后，所有数据将无法恢复。请确认您要执行此操作。',
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await new Promise(resolve => setTimeout(resolve, 1000));
          message.success('账号删除请求已提交，将在24小时内处理');
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  // 保存系统配置
  const handleSaveSystemConfig = async () => {
    setLoading(true);
    try {
      // 模拟保存配置到后端
      await new Promise(resolve => setTimeout(resolve, 2000));
      message.success('系统配置保存成功！配置将在下次重启后生效。');
    } catch (error) {
      message.error('配置保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 重置为默认值
  const handleResetToDefault = () => {
    Modal.confirm({
      title: '确认重置配置',
      content: '确定要将所有配置重置为默认值吗？此操作不可撤销。',
      okText: '确认重置',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        // 重置表单值
        systemForm.resetFields();
        message.success('配置已重置为默认值');
      },
    });
  };

  // 导出配置
  const handleExportConfig = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 模拟生成配置文件
      const config = {
        ai_service: {
          provider: 'ollama',
          openai_api_key: '***',
          model: 'llama2',
          ollama_url: 'http://localhost:11434'
        },
        crawler: {
          min_delay: 1,
          max_delay: 3,
          timeout: 30,
          retry_count: 3,
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        database: {
          type: 'postgresql',
          host: '*************:5432',
          name: 'xiaohongshu_auto_reply',
          pool_size: 10
        },
        cache: {
          redis_url: 'localhost:6379',
          expire_time: 3600,
          max_entries: 1000,
          enabled: true
        },
        email: {
          smtp_server: 'smtp.gmail.com',
          smtp_port: 587,
          enable_tls: true,
          enable_ssl: false
        },
        security: {
          token_expire_minutes: 1440,
          cors_origins: ['http://localhost:3000', 'http://127.0.0.1:3000'],
          enable_https: true,
          enable_cors: true
        },
        logging: {
          level: 'INFO',
          file_path: '/var/log/xiaohongshu/app.log',
          max_file_size_mb: 100,
          enable_file_log: true,
          enable_console_log: true
        },
        monitoring: {
          prometheus_port: 9090,
          health_check_interval: 60,
          enable_sentry: false,
          enable_prometheus: false,
          enable_health_check: true
        }
      };

      // 创建下载链接
      const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `xiaohongshu-config-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      message.success('配置文件导出成功');
    } catch (error) {
      message.error('导出失败');
    } finally {
      setLoading(false);
    }
  };

  // 导入配置
  const handleImportConfig = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = e.target.files[0];
      if (!file) return;

      try {
        const text = await file.text();
        const config = JSON.parse(text);

        // 验证配置文件格式
        if (!config.ai_service || !config.crawler || !config.database) {
          throw new Error('配置文件格式不正确');
        }

        // 这里可以设置表单值
        message.success('配置文件导入成功');
      } catch (error) {
        message.error('配置文件格式错误或导入失败');
      }
    };
    input.click();
  };

  // 下载配置文件模板
  const handleDownloadTemplate = () => {
    const template = {
      ai_service: {
        provider: 'ollama',
        openai_api_key: 'your-openai-api-key',
        model: 'llama2',
        ollama_url: 'http://localhost:11434'
      },
      crawler: {
        min_delay: 1,
        max_delay: 3,
        timeout: 30,
        retry_count: 3,
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      // ... 其他配置项
    };

    const blob = new Blob([JSON.stringify(template, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'xiaohongshu-config-template.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    message.success('配置文件模板下载成功');
  };

  // 重启系统
  const handleRestartSystem = () => {
    Modal.confirm({
      title: '确认重启系统',
      content: '重启系统将应用新的配置，但会中断当前所有服务。确定要继续吗？',
      okText: '确认重启',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true);
          await new Promise(resolve => setTimeout(resolve, 3000));
          message.success('系统重启请求已发送，系统将在30秒后重启');
        } catch (error) {
          message.error('重启失败');
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const tabItems = [
    {
      key: 'profile',
      label: (
        <span>
          <UserOutlined />
          个人资料
        </span>
      ),
      children: (
        <div>
          {/* 资料完整度和统计 */}
          <Row gutter={24} style={{ marginBottom: 24 }}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="资料完整度"
                  value={profileStats.completeness}
                  suffix="%"
                  valueStyle={{ color: profileStats.completeness > 80 ? '#52c41a' : '#fa8c16' }}
                />
                <Progress 
                  percent={profileStats.completeness} 
                  size="small" 
                  strokeColor={profileStats.completeness > 80 ? '#52c41a' : '#fa8c16'}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="账号使用天数"
                  value={profileStats.accountAge}
                  suffix="天"
                  prefix={<CalendarOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总登录次数"
                  value={profileStats.totalLogins}
                  prefix={<CheckCircleOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <div>
                  <Text type="secondary">最后登录</Text>
                  <div style={{ fontSize: '16px', fontWeight: 'bold' }}>
                    {profileStats.lastLogin}
                  </div>
                </div>
              </Card>
            </Col>
          </Row>

          <Card title="基本信息">
            <Form
              form={profileForm}
              layout="vertical"
              onFinish={handleSaveProfile}
            >
              <Row gutter={24}>
                <Col span={6}>
                  <div style={{ textAlign: 'center' }}>
                    <Badge count={<CameraOutlined style={{ color: '#1890ff' }} />} offset={[-20, 100]}>
                      <Avatar 
                        size={120} 
                        src={avatarUrl}
                        icon={<UserOutlined />} 
                        style={{ marginBottom: 16 }} 
                      />
                    </Badge>
                    <Upload
                      showUploadList={false}
                      beforeUpload={handleAvatarUpload}
                      accept="image/*"
                    >
                      <Button 
                        icon={<UploadOutlined />} 
                        loading={uploading}
                        block
                      >
                        更换头像
                      </Button>
                    </Upload>
                    <Text type="secondary" style={{ fontSize: '12px', display: 'block', marginTop: 8 }}>
                      支持 JPG、PNG 格式，文件大小不超过 2MB
                    </Text>
                  </div>
                </Col>
                <Col span={18}>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="username"
                        label="用户名"
                        rules={[{ required: true, message: '请输入用户名' }]}
                      >
                        <Input
                          placeholder="请输入用户名"
                          disabled
                          prefix={<UserOutlined />}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="email"
                        label="邮箱地址"
                        rules={[
                          { required: true, message: '请输入邮箱地址' },
                          { type: 'email', message: '请输入有效的邮箱地址' },
                        ]}
                      >
                        <Input
                          placeholder="请输入邮箱地址"
                          prefix={<MailOutlined />}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="full_name"
                        label="姓名"
                        rules={[{ required: true, message: '请输入姓名' }]}
                      >
                        <Input placeholder="请输入姓名" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="phone"
                        label="手机号码"
                        rules={[{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }]}
                      >
                        <Input
                          placeholder="请输入手机号码"
                          prefix={<PhoneOutlined />}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="location"
                        label="所在地区"
                      >
                        <Input placeholder="请输入所在地区" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="website"
                        label="个人网站"
                        rules={[{ type: 'url', message: '请输入有效的网址' }]}
                      >
                        <Input placeholder="https://example.com" />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item
                        name="birthday"
                        label="生日"
                      >
                        <Input
                          placeholder="YYYY-MM-DD"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        name="gender"
                        label="性别"
                      >
                        <Select placeholder="请选择性别">
                          <Option value="male">男</Option>
                          <Option value="female">女</Option>
                          <Option value="other">其他</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        name="occupation"
                        label="职业"
                      >
                        <Input placeholder="请输入职业" />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="company"
                        label="公司/组织"
                      >
                        <Input placeholder="请输入公司或组织名称" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="bio"
                        label="个人简介"
                      >
                        <TextArea
                          rows={3}
                          placeholder="请输入个人简介"
                          maxLength={200}
                          showCount
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Col>
              </Row>

              <Divider />

              <Space>
                <Button type="primary" htmlType="submit" loading={loading} icon={<SaveOutlined />}>
                  保存资料
                </Button>
                <Button onClick={() => setChangePasswordVisible(true)} icon={<LockOutlined />}>
                  修改密码
                </Button>
                <Button onClick={handleExportData} icon={<DownloadOutlined />}>
                  导出数据
                </Button>
                <Popconfirm
                  title="确定要删除账号吗？"
                  description="此操作不可逆，请谨慎操作"
                  onConfirm={handleDeleteAccount}
                  okText="确定"
                  cancelText="取消"
                  okType="danger"
                >
                  <Button danger icon={<DeleteOutlined />}>
                    删除账号
                  </Button>
                </Popconfirm>
              </Space>
            </Form>
          </Card>

          {/* 修改密码模态框 */}
          <Modal
            title="修改密码"
            open={changePasswordVisible}
            onCancel={() => setChangePasswordVisible(false)}
            footer={null}
            width={500}
          >
            <Form
              form={passwordForm}
              layout="vertical"
              onFinish={handleChangePassword}
            >
              <Form.Item
                name="currentPassword"
                label="当前密码"
                rules={[{ required: true, message: '请输入当前密码' }]}
              >
                <Input.Password
                  placeholder="请输入当前密码"
                  prefix={<LockOutlined />}
                />
              </Form.Item>
              <Form.Item
                name="newPassword"
                label="新密码"
                rules={[
                  { required: true, message: '请输入新密码' },
                  { min: 8, message: '密码长度至少8位' },
                  { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, message: '密码必须包含大小写字母和数字' }
                ]}
              >
                <Input.Password
                  placeholder="请输入新密码"
                  prefix={<LockOutlined />}
                />
              </Form.Item>
              <Form.Item
                name="confirmPassword"
                label="确认新密码"
                dependencies={['newPassword']}
                rules={[
                  { required: true, message: '请确认新密码' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('newPassword') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('两次输入的密码不一致'));
                    },
                  }),
                ]}
              >
                <Input.Password
                  placeholder="请再次输入新密码"
                  prefix={<LockOutlined />}
                />
              </Form.Item>
              <Alert
                message="密码安全提示"
                description="为了账号安全，建议使用包含大小写字母、数字和特殊字符的强密码。"
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" loading={loading}>
                    确认修改
                  </Button>
                  <Button onClick={() => setChangePasswordVisible(false)}>
                    取消
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Modal>
        </div>
      ),
    },
    {
      key: 'system',
      label: (
        <span>
          <SettingOutlined />
          系统设置
        </span>
      ),
      children: (
        <div>
          {/* 主题设置 */}
          <Card title={
            <span>
              <BgColorsOutlined style={{ marginRight: 8 }} />
              主题设置
            </span>
          } style={{ marginBottom: 24 }}>
            <Row gutter={24}>
              <Col span={12}>
                <div>
                  <Typography.Title level={5}>主题模式</Typography.Title>
                  <Radio.Group
                    value={currentTheme}
                    onChange={(e) => setTheme(e.target.value)}
                    style={{ width: '100%' }}
                  >
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Radio value="light">
                        <Space>
                          <SunOutlined style={{ color: '#faad14' }} />
                          浅色主题
                        </Space>
                      </Radio>
                      <Radio value="dark">
                        <Space>
                          <MoonOutlined style={{ color: '#722ed1' }} />
                          深色主题
                        </Space>
                      </Radio>
                    </Space>
                  </Radio.Group>
                </div>
              </Col>
              <Col span={12}>
                <div>
                  <Typography.Title level={5}>主题预览</Typography.Title>
                  <Row gutter={12}>
                    {Object.entries(themes).map(([key, theme]) => (
                      <Col span={12} key={key}>
                        <div
                          className={`theme-preview-card ${currentTheme === key ? 'active' : ''}`}
                          onClick={() => setTheme(key)}
                          style={{
                            border: `2px solid ${currentTheme === key ? '#1890ff' : '#d9d9d9'}`,
                            borderRadius: '8px',
                            padding: '12px',
                            cursor: 'pointer',
                            transition: 'all 0.3s ease',
                            position: 'relative',
                            backgroundColor: theme.colors.componentBg,
                          }}
                        >
                          {currentTheme === key && (
                            <CheckCircleOutlined
                              style={{
                                position: 'absolute',
                                top: '8px',
                                right: '8px',
                                color: '#1890ff',
                                fontSize: '16px'
                              }}
                            />
                          )}
                          <div style={{ marginBottom: '8px', fontWeight: 'bold', color: theme.colors.textPrimary }}>
                            {theme.displayName}
                          </div>
                          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                            <div style={{
                              height: '16px',
                              backgroundColor: theme.colors.primary,
                              borderRadius: '4px',
                              opacity: 0.8
                            }} />
                            <div style={{
                              height: '32px',
                              backgroundColor: theme.colors.layoutBg,
                              borderRadius: '4px',
                              opacity: 0.6
                            }} />
                            <div style={{
                              height: '12px',
                              backgroundColor: theme.colors.textSecondary,
                              borderRadius: '4px',
                              opacity: 0.4,
                              width: '60%'
                            }} />
                          </div>
                        </div>
                      </Col>
                    ))}
                  </Row>
                </div>
              </Col>
            </Row>
            <Divider />
            <Row gutter={24}>
              <Col span={24}>
                <Space>
                  <Button
                    type="primary"
                    icon={isDark ? <SunOutlined /> : <MoonOutlined />}
                    onClick={toggleTheme}
                  >
                    切换到{isDark ? '浅色' : '深色'}主题
                  </Button>
                  <Button
                    onClick={() => {
                      const systemTheme = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                      setTheme(systemTheme);
                      message.success('已切换到系统主题');
                    }}
                  >
                    跟随系统主题
                  </Button>
                </Space>
              </Col>
            </Row>
          </Card>

          <Row gutter={24}>
            {/* AI服务配置 */}
            <Col span={12}>
              <Card title="AI服务配置" style={{ marginBottom: 24 }}>
                <Form layout="vertical">
                  <Form.Item label="AI服务提供商">
                    <Select defaultValue="ollama" style={{ width: '100%' }}>
                      <Option value="openai">OpenAI</Option>
                      <Option value="ollama">Ollama (本地)</Option>
                      <Option value="anthropic">Anthropic</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item label="OpenAI API Key">
                    <Input.Password
                      placeholder="请输入OpenAI API Key"
                      visibilityToggle
                    />
                  </Form.Item>
                  <Form.Item label="模型选择">
                    <Select defaultValue="llama2" style={{ width: '100%' }}>
                      <Option value="gpt-3.5-turbo">GPT-3.5 Turbo</Option>
                      <Option value="gpt-4">GPT-4</Option>
                      <Option value="llama2">Llama 2</Option>
                      <Option value="claude-3">Claude 3</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item label="Ollama服务地址">
                    <Input
                      defaultValue="http://localhost:11434"
                      placeholder="请输入Ollama服务地址"
                    />
                  </Form.Item>
                </Form>
              </Card>
            </Col>

            {/* 爬虫配置 */}
            <Col span={12}>
              <Card title="爬虫配置" style={{ marginBottom: 24 }}>
                <Form layout="vertical">
                  <Form.Item label="请求延迟 (秒)">
                    <Row gutter={8}>
                      <Col span={12}>
                        <InputNumber
                          min={1}
                          max={10}
                          defaultValue={1}
                          placeholder="最小延迟"
                          style={{ width: '100%' }}
                        />
                      </Col>
                      <Col span={12}>
                        <InputNumber
                          min={1}
                          max={10}
                          defaultValue={3}
                          placeholder="最大延迟"
                          style={{ width: '100%' }}
                        />
                      </Col>
                    </Row>
                  </Form.Item>
                  <Form.Item label="请求超时 (秒)">
                    <InputNumber
                      min={10}
                      max={120}
                      defaultValue={30}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                  <Form.Item label="重试次数">
                    <InputNumber
                      min={1}
                      max={10}
                      defaultValue={3}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                  <Form.Item label="User Agent">
                    <TextArea
                      rows={2}
                      defaultValue="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                      placeholder="请输入User Agent"
                    />
                  </Form.Item>
                </Form>
              </Card>
            </Col>
          </Row>

          <Row gutter={24}>
            {/* 数据库配置 */}
            <Col span={12}>
              <Card title="数据库配置" style={{ marginBottom: 24 }}>
                <Form layout="vertical">
                  <Form.Item label="数据库类型">
                    <Select defaultValue="postgresql" disabled style={{ width: '100%' }}>
                      <Option value="postgresql">PostgreSQL</Option>
                      <Option value="mysql">MySQL</Option>
                      <Option value="sqlite">SQLite</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item label="数据库地址">
                    <Input
                      defaultValue="*************:5432"
                      placeholder="请输入数据库地址"
                    />
                  </Form.Item>
                  <Form.Item label="数据库名称">
                    <Input
                      defaultValue="xiaohongshu_auto_reply"
                      placeholder="请输入数据库名称"
                    />
                  </Form.Item>
                  <Form.Item label="连接池大小">
                    <InputNumber
                      min={5}
                      max={50}
                      defaultValue={10}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Form>
              </Card>
            </Col>

            {/* 缓存配置 */}
            <Col span={12}>
              <Card title="缓存配置" style={{ marginBottom: 24 }}>
                <Form layout="vertical">
                  <Form.Item label="Redis地址">
                    <Input
                      defaultValue="localhost:6379"
                      placeholder="请输入Redis地址"
                    />
                  </Form.Item>
                  <Form.Item label="缓存过期时间 (秒)">
                    <InputNumber
                      min={60}
                      max={86400}
                      defaultValue={3600}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                  <Form.Item label="最大缓存条目">
                    <InputNumber
                      min={100}
                      max={10000}
                      defaultValue={1000}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                  <Form.Item>
                    <Space>
                      <Switch defaultChecked /> 启用缓存
                    </Space>
                  </Form.Item>
                </Form>
              </Card>
            </Col>
          </Row>

          <Row gutter={24}>
            {/* 邮件配置 */}
            <Col span={12}>
              <Card title="邮件配置" style={{ marginBottom: 24 }}>
                <Form layout="vertical">
                  <Form.Item label="SMTP服务器">
                    <Input
                      defaultValue="smtp.gmail.com"
                      placeholder="请输入SMTP服务器地址"
                    />
                  </Form.Item>
                  <Form.Item label="SMTP端口">
                    <InputNumber
                      min={25}
                      max={65535}
                      defaultValue={587}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                  <Form.Item label="邮箱账号">
                    <Input
                      placeholder="请输入邮箱账号"
                      prefix={<MailOutlined />}
                    />
                  </Form.Item>
                  <Form.Item label="邮箱密码">
                    <Input.Password
                      placeholder="请输入邮箱密码或应用密码"
                      visibilityToggle
                    />
                  </Form.Item>
                  <Form.Item>
                    <Space>
                      <Switch defaultChecked /> 启用TLS
                      <Switch /> 启用SSL
                    </Space>
                  </Form.Item>
                </Form>
              </Card>
            </Col>

            {/* 安全配置 */}
            <Col span={12}>
              <Card title="安全配置" style={{ marginBottom: 24 }}>
                <Form layout="vertical">
                  <Form.Item label="JWT密钥">
                    <Input.Password
                      placeholder="请输入JWT密钥 (至少32个字符)"
                      visibilityToggle
                    />
                  </Form.Item>
                  <Form.Item label="Token过期时间 (分钟)">
                    <InputNumber
                      min={30}
                      max={10080}
                      defaultValue={1440}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                  <Form.Item label="允许的跨域地址">
                    <TextArea
                      rows={3}
                      defaultValue="http://localhost:3000&#10;http://127.0.0.1:3000"
                      placeholder="每行一个地址"
                    />
                  </Form.Item>
                  <Form.Item>
                    <Space>
                      <Switch defaultChecked /> 启用HTTPS
                      <Switch defaultChecked /> 启用CORS
                    </Space>
                  </Form.Item>
                </Form>
              </Card>
            </Col>
          </Row>

          <Row gutter={24}>
            {/* 日志配置 */}
            <Col span={12}>
              <Card title="日志配置" style={{ marginBottom: 24 }}>
                <Form layout="vertical">
                  <Form.Item label="日志级别">
                    <Select defaultValue="INFO" style={{ width: '100%' }}>
                      <Option value="DEBUG">DEBUG</Option>
                      <Option value="INFO">INFO</Option>
                      <Option value="WARNING">WARNING</Option>
                      <Option value="ERROR">ERROR</Option>
                      <Option value="CRITICAL">CRITICAL</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item label="日志文件路径">
                    <Input
                      defaultValue="/var/log/xiaohongshu/app.log"
                      placeholder="请输入日志文件路径"
                    />
                  </Form.Item>
                  <Form.Item label="日志文件大小限制 (MB)">
                    <InputNumber
                      min={1}
                      max={1000}
                      defaultValue={100}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                  <Form.Item>
                    <Space>
                      <Switch defaultChecked /> 启用文件日志
                      <Switch defaultChecked /> 启用控制台日志
                    </Space>
                  </Form.Item>
                </Form>
              </Card>
            </Col>

            {/* 监控配置 */}
            <Col span={12}>
              <Card title="监控配置" style={{ marginBottom: 24 }}>
                <Form layout="vertical">
                  <Form.Item label="Sentry DSN">
                    <Input
                      placeholder="请输入Sentry DSN (可选)"
                    />
                  </Form.Item>
                  <Form.Item label="Prometheus端口">
                    <InputNumber
                      min={1000}
                      max={65535}
                      defaultValue={9090}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                  <Form.Item label="健康检查间隔 (秒)">
                    <InputNumber
                      min={10}
                      max={300}
                      defaultValue={60}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                  <Form.Item>
                    <Space>
                      <Switch /> 启用Sentry
                      <Switch /> 启用Prometheus
                      <Switch defaultChecked /> 启用健康检查
                    </Space>
                  </Form.Item>
                </Form>
              </Card>
            </Col>
          </Row>

          {/* 操作按钮 */}
          <Card>
            <Row justify="space-between" align="middle">
              <Col>
                <Space>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    size="large"
                    loading={loading}
                    onClick={handleSaveSystemConfig}
                  >
                    保存所有配置
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    size="large"
                    onClick={handleResetToDefault}
                  >
                    重置为默认值
                  </Button>
                  <Button
                    icon={<ExportOutlined />}
                    size="large"
                    loading={loading}
                    onClick={handleExportConfig}
                  >
                    导出配置
                  </Button>
                  <Button
                    icon={<ImportOutlined />}
                    size="large"
                    onClick={handleImportConfig}
                  >
                    导入配置
                  </Button>
                </Space>
              </Col>
              <Col>
                <Space>
                  <Button type="dashed" icon={<DownloadOutlined />}>
                    下载配置文件
                  </Button>
                  <Popconfirm
                    title="确定要重启系统吗？"
                    description="重启系统将应用新的配置，可能会中断当前服务"
                    okText="确定重启"
                    cancelText="取消"
                    okType="danger"
                  >
                    <Button danger icon={<ReloadOutlined />}>
                      重启系统
                    </Button>
                  </Popconfirm>
                </Space>
              </Col>
            </Row>
          </Card>
        </div>
      ),
    },
  ];

  return (
    <div className="settings-page">
      <div className="page-header">
        <Title level={2}>系统设置</Title>
        <p>管理个人资料、系统偏好和安全配置</p>
      </div>

      <Tabs 
        defaultActiveKey="profile" 
        type="card"
        items={tabItems}
      />
    </div>
  );
};

export default SettingsPage;
