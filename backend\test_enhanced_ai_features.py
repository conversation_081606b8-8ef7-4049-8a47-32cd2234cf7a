#!/usr/bin/env python3
"""
测试增强的AI功能
"""
import requests
import json
import sys

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def login():
    """登录获取token"""
    login_data = {
        "username": "testuser2",
        "password": "testpass123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
    if response.status_code == 200:
        result = response.json()
        if "data" in result and "access_token" in result["data"]:
            return result["data"]["access_token"]
    return None

def test_create_template(token):
    """测试创建模板"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n📝 测试创建回复模板")
    print("-" * 30)
    
    template_data = {
        "name": "测试模板",
        "content": "感谢{user_name}的{action}！我们会继续努力提供更好的{product_type}。",
        "category": "thanks",
        "tags": ["感谢", "回复", "测试"],
        "variables": {
            "user_name": "用户名",
            "action": "行为",
            "product_type": "产品类型"
        }
    }
    
    response = requests.post(
        f"{BASE_URL}/comments/ai/templates",
        headers=headers,
        json=template_data
    )
    
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 模板创建成功!")
        
        if result.get("success") and result.get("data"):
            data = result["data"]
            print(f"   模板ID: {data.get('id')}")
            print(f"   模板名称: {data.get('name')}")
            print(f"   分类: {data.get('category')}")
            print(f"   标签: {data.get('tags')}")
            return data.get('id')
        
    else:
        result = response.json()
        print(f"❌ 模板创建失败: {result.get('detail', '未知错误')}")
        return None

def test_get_template(token, template_id):
    """测试获取模板详情"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print(f"\n📋 测试获取模板详情 (ID: {template_id})")
    print("-" * 30)
    
    response = requests.get(
        f"{BASE_URL}/comments/ai/templates/{template_id}",
        headers=headers
    )
    
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 获取模板详情成功!")
        
        if result.get("success") and result.get("data"):
            data = result["data"]
            print(f"   模板名称: {data.get('name')}")
            print(f"   内容: {data.get('content')}")
            print(f"   使用次数: {data.get('usage_count')}")
            print(f"   成功率: {data.get('success_rate')}%")
        
        return True
    else:
        result = response.json()
        print(f"❌ 获取模板详情失败: {result.get('detail', '未知错误')}")
        return False

def test_update_template(token, template_id):
    """测试更新模板"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print(f"\n✏️ 测试更新模板 (ID: {template_id})")
    print("-" * 30)
    
    update_data = {
        "name": "更新后的测试模板",
        "content": "感谢{user_name}的{action}！这是更新后的内容。",
        "tags": ["感谢", "回复", "测试", "更新"]
    }
    
    response = requests.put(
        f"{BASE_URL}/comments/ai/templates/{template_id}",
        headers=headers,
        json=update_data
    )
    
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 模板更新成功!")
        
        if result.get("success") and result.get("data"):
            data = result["data"]
            print(f"   新名称: {data.get('name')}")
            print(f"   新内容: {data.get('content')}")
        
        return True
    else:
        result = response.json()
        print(f"❌ 模板更新失败: {result.get('detail', '未知错误')}")
        return False

def test_get_statistics(token):
    """测试获取使用统计"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n📊 测试获取AI使用统计")
    print("-" * 30)
    
    response = requests.get(
        f"{BASE_URL}/comments/ai/statistics",
        headers=headers
    )
    
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 获取统计数据成功!")
        
        if result.get("success") and result.get("data"):
            data = result["data"]
            
            print(f"\n📈 今日统计:")
            print(f"   请求数: {data['today']['requests']}")
            print(f"   成功率: {data['today']['success_rate']}%")
            print(f"   Token使用: {data['today']['tokens_used']}")
            print(f"   成本: ¥{data['today']['cost']}")
            
            print(f"\n📅 本月统计:")
            print(f"   请求数: {data['month']['requests']}")
            print(f"   成功率: {data['month']['success_rate']}%")
            print(f"   Token使用: {data['month']['tokens_used']}")
            print(f"   成本: ¥{data['month']['cost']}")
            
            print(f"\n⚠️ 使用限制:")
            print(f"   每日限制: {data['limits']['daily_limit']}")
            print(f"   已使用: {data['limits']['daily_used']}")
            print(f"   使用率: {data['limits']['usage_percentage']}%")
        
        return True
    else:
        result = response.json()
        print(f"❌ 获取统计失败: {result.get('detail', '未知错误')}")
        return False

def test_get_history(token):
    """测试获取回复历史"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n📋 测试获取AI回复历史")
    print("-" * 30)
    
    response = requests.get(
        f"{BASE_URL}/comments/ai/history?page=1&size=5",
        headers=headers
    )
    
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 获取回复历史成功!")
        
        if result.get("success") and result.get("data"):
            history = result["data"]
            total = result.get("pagination", {}).get("total", 0)
            
            print(f"📊 共找到 {total} 条历史记录")
            
            for i, item in enumerate(history[:3], 1):  # 只显示前3条
                print(f"\n   {i}. 历史记录 ID: {item.get('id')}")
                print(f"      原评论: {item.get('comment_content', '')[:50]}...")
                print(f"      AI回复: {item.get('generated_reply', '')[:50]}...")
                print(f"      使用模型: {item.get('model_used', 'Unknown')}")
                print(f"      Token使用: {item.get('tokens_used', 'N/A')}")
        
        return True
    else:
        result = response.json()
        print(f"❌ 获取历史失败: {result.get('detail', '未知错误')}")
        return False

def test_delete_template(token, template_id):
    """测试删除模板"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print(f"\n🗑️ 测试删除模板 (ID: {template_id})")
    print("-" * 30)
    
    response = requests.delete(
        f"{BASE_URL}/comments/ai/templates/{template_id}",
        headers=headers
    )
    
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 模板删除成功!")
        
        if result.get("success") and result.get("data"):
            print(f"   删除的模板ID: {result['data']['deleted_id']}")
        
        return True
    else:
        result = response.json()
        print(f"❌ 模板删除失败: {result.get('detail', '未知错误')}")
        return False

def main():
    """主函数"""
    print("🚀 增强AI功能测试")
    print("=" * 50)
    
    # 1. 登录
    print("\n1️⃣ 登录测试")
    token = login()
    if not token:
        print("❌ 登录失败，无法继续测试")
        return
    
    print("✅ 登录成功!")
    
    # 2. 测试模板管理功能
    template_id = test_create_template(token)
    
    if template_id:
        test_get_template(token, template_id)
        test_update_template(token, template_id)
    
    # 3. 测试统计功能
    test_get_statistics(token)
    
    # 4. 测试历史功能
    test_get_history(token)
    
    # 5. 清理：删除测试模板
    if template_id:
        test_delete_template(token, template_id)
    
    print("\n🎯 测试结果总结:")
    print("✅ 增强AI功能测试完成")
    print("📝 模板管理功能正常")
    print("📊 使用统计功能正常")
    print("📋 回复历史功能正常")
    print("\n🎉 所有新功能都已成功集成！")

if __name__ == "__main__":
    main()
