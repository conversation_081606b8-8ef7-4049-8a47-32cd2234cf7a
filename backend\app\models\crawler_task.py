"""
爬虫任务相关数据模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, Enum, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from .database import Base
import enum


class TaskStatus(enum.Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待中
    RUNNING = "running"      # 运行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"  # 已取消
    PAUSED = "paused"        # 已暂停


class TaskType(enum.Enum):
    """任务类型枚举"""
    MANUAL = "manual"        # 手动任务
    SCHEDULED = "scheduled"  # 定时任务
    AUTO = "auto"           # 自动任务


class CrawlerTask(Base):
    """爬虫任务表"""
    __tablename__ = "crawler_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    note_id = Column(Integer, ForeignKey("notes.id"), nullable=True)  # 可以为空，表示批量任务
    account_id = Column(Integer, ForeignKey("xiaohongshu_accounts.id"), nullable=False)
    
    # 任务基本信息
    task_name = Column(String(200), nullable=False)  # 任务名称
    task_type = Column(Enum(TaskType), default=TaskType.MANUAL)  # 任务类型
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING)  # 任务状态
    
    # 任务配置
    target_urls = Column(Text)  # 目标URL列表（JSON格式）
    crawl_config = Column(Text)  # 爬虫配置（JSON格式）
    
    # 执行信息
    start_time = Column(DateTime(timezone=True))  # 开始时间
    end_time = Column(DateTime(timezone=True))    # 结束时间
    last_update = Column(DateTime(timezone=True), onupdate=func.now())  # 最后更新时间
    
    # 进度和结果
    progress = Column(Integer, default=0)  # 进度百分比 0-100
    total_items = Column(Integer, default=0)  # 总项目数
    processed_items = Column(Integer, default=0)  # 已处理项目数
    success_items = Column(Integer, default=0)  # 成功项目数
    failed_items = Column(Integer, default=0)  # 失败项目数
    
    # 统计信息
    comments_found = Column(Integer, default=0)  # 找到的评论数
    new_comments = Column(Integer, default=0)    # 新评论数
    error_count = Column(Integer, default=0)     # 错误次数
    
    # 性能指标
    avg_response_time = Column(Float, default=0.0)  # 平均响应时间（秒）
    total_requests = Column(Integer, default=0)      # 总请求数
    
    # 错误信息
    error_message = Column(Text)  # 错误消息
    error_details = Column(Text)  # 错误详情（JSON格式）
    
    # 日志和调试
    log_file_path = Column(String(500))  # 日志文件路径
    debug_info = Column(Text)            # 调试信息（JSON格式）
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    user = relationship("User", back_populates="crawler_tasks")
    note = relationship("Note", back_populates="crawler_tasks")
    account = relationship("XiaohongshuAccount", back_populates="crawler_tasks")
    task_logs = relationship("CrawlerTaskLog", back_populates="task", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<CrawlerTask(id={self.id}, name='{self.task_name}', status='{self.status}')>"
    
    @property
    def success_rate(self):
        """计算成功率"""
        if self.processed_items == 0:
            return 0.0
        return (self.success_items / self.processed_items) * 100
    
    @property
    def is_active(self):
        """判断任务是否活跃"""
        return self.status in [TaskStatus.PENDING, TaskStatus.RUNNING, TaskStatus.PAUSED]
    
    @property
    def duration(self):
        """计算任务持续时间（秒）"""
        if not self.start_time:
            return 0
        end_time = self.end_time or func.now()
        return (end_time - self.start_time).total_seconds()


class CrawlerTaskLog(Base):
    """爬虫任务日志表"""
    __tablename__ = "crawler_task_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, ForeignKey("crawler_tasks.id"), nullable=False)
    
    # 日志信息
    level = Column(String(20), nullable=False)  # 日志级别：info, warning, error, debug
    message = Column(Text, nullable=False)      # 日志消息
    details = Column(Text)                      # 详细信息（JSON格式）
    
    # 上下文信息
    note_id = Column(Integer, ForeignKey("notes.id"), nullable=True)  # 相关笔记ID
    url = Column(String(500))                   # 相关URL
    request_id = Column(String(100))            # 请求ID
    
    # 性能指标
    response_time = Column(Float)               # 响应时间（秒）
    memory_usage = Column(Float)                # 内存使用量（MB）
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关联关系
    task = relationship("CrawlerTask", back_populates="task_logs")
    note = relationship("Note")
    
    def __repr__(self):
        return f"<CrawlerTaskLog(id={self.id}, level='{self.level}', message='{self.message[:50]}...')>"


class CrawlerConfig(Base):
    """爬虫配置表"""
    __tablename__ = "crawler_configs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 配置信息
    config_name = Column(String(100), nullable=False)  # 配置名称
    is_default = Column(Boolean, default=False)        # 是否为默认配置
    is_active = Column(Boolean, default=True)          # 是否启用
    
    # 爬虫参数
    crawl_interval = Column(Integer, default=1800)     # 抓取间隔（秒），默认30分钟
    max_concurrent = Column(Integer, default=3)        # 最大并发数
    request_delay = Column(Float, default=2.0)         # 请求延迟（秒）
    retry_times = Column(Integer, default=3)           # 重试次数
    timeout = Column(Integer, default=30)              # 超时时间（秒）
    
    # 代理设置
    enable_proxy = Column(Boolean, default=False)      # 是否启用代理
    proxy_config = Column(Text)                        # 代理配置（JSON格式）
    
    # 高级设置
    user_agent = Column(String(500))                   # User-Agent
    headers = Column(Text)                             # 请求头（JSON格式）
    cookies = Column(Text)                             # Cookies（JSON格式）
    
    # 自动化设置
    auto_restart = Column(Boolean, default=True)       # 自动重启
    auto_retry = Column(Boolean, default=True)         # 自动重试
    enable_scheduling = Column(Boolean, default=False) # 启用定时任务
    
    # 通知设置
    notify_on_success = Column(Boolean, default=False) # 成功时通知
    notify_on_failure = Column(Boolean, default=True)  # 失败时通知
    notification_config = Column(Text)                 # 通知配置（JSON格式）
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    user = relationship("User", back_populates="crawler_configs")
    
    def __repr__(self):
        return f"<CrawlerConfig(id={self.id}, name='{self.config_name}', user_id={self.user_id})>"
