"""
小红书账号服务层
处理小红书账号相关的业务逻辑
"""
from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status
from datetime import datetime
from ..models.user import <PERSON>hongshuAccount
from ..api.schemas.xiaohongshu import (
    XiaohongshuAccountCreate, 
    XiaohongshuAccountUpdate,
    CookieData
)
import logging
import json

logger = logging.getLogger(__name__)


class XiaohongshuService:
    """小红书账号服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_account(self, user_id: int, account_data: XiaohongshuAccountCreate) -> <PERSON>hongshuAccount:
        """创建小红书账号"""
        try:
            # 检查账号ID是否已存在（如果提供了）
            if account_data.account_id:
                existing_account = self.get_account_by_account_id(account_data.account_id)
                if existing_account:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="该小红书账号ID已存在"
                    )
            
            # 创建新账号
            db_account = XiaohongshuAccount(
                user_id=user_id,
                account_name=account_data.account_name,
                account_id=account_data.account_id,
                login_phone=account_data.login_phone,
                login_email=account_data.login_email
            )
            
            self.db.add(db_account)
            self.db.commit()
            self.db.refresh(db_account)
            
            logger.info(f"新小红书账号创建成功: {account_data.account_name} (用户ID: {user_id})")
            return db_account
            
        except IntegrityError as e:
            self.db.rollback()
            logger.error(f"创建小红书账号时数据库错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="账号创建失败，可能存在重复数据"
            )
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建小红书账号时发生错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="账号创建失败"
            )
    
    def get_account_by_id(self, account_id: int) -> Optional[XiaohongshuAccount]:
        """根据ID获取账号"""
        return self.db.query(XiaohongshuAccount).filter(XiaohongshuAccount.id == account_id).first()
    
    def get_account_by_account_id(self, account_id: str) -> Optional[XiaohongshuAccount]:
        """根据小红书账号ID获取账号"""
        return self.db.query(XiaohongshuAccount).filter(XiaohongshuAccount.account_id == account_id).first()
    
    def get_user_accounts(self, user_id: int, skip: int = 0, limit: int = 100) -> List[XiaohongshuAccount]:
        """获取用户的小红书账号列表"""
        return (
            self.db.query(XiaohongshuAccount)
            .filter(XiaohongshuAccount.user_id == user_id)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_user_accounts_count(self, user_id: int) -> int:
        """获取用户的小红书账号数量"""
        return self.db.query(XiaohongshuAccount).filter(XiaohongshuAccount.user_id == user_id).count()
    
    def update_account(self, account_id: int, user_id: int, account_data: XiaohongshuAccountUpdate) -> Optional[XiaohongshuAccount]:
        """更新小红书账号信息"""
        try:
            account = self.get_account_by_id(account_id)
            if not account:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="账号不存在"
                )
            
            # 检查账号所有权
            if account.user_id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权限操作此账号"
                )
            
            # 检查账号ID唯一性（如果要更新）
            update_data = account_data.dict(exclude_unset=True)
            if 'account_id' in update_data and update_data['account_id']:
                existing_account = self.get_account_by_account_id(update_data['account_id'])
                if existing_account and existing_account.id != account_id:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="该小红书账号ID已存在"
                    )
            
            # 应用更新
            for field, value in update_data.items():
                setattr(account, field, value)
            
            self.db.commit()
            self.db.refresh(account)
            
            logger.info(f"小红书账号信息更新成功: {account.account_name}")
            return account
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新小红书账号信息时发生错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="账号信息更新失败"
            )
    
    def delete_account(self, account_id: int, user_id: int) -> bool:
        """删除小红书账号"""
        try:
            account = self.get_account_by_id(account_id)
            if not account:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="账号不存在"
                )
            
            # 检查账号所有权
            if account.user_id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权限操作此账号"
                )
            
            self.db.delete(account)
            self.db.commit()
            
            logger.info(f"小红书账号删除成功: {account.account_name}")
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除小红书账号时发生错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="账号删除失败"
            )
    
    def update_cookies(self, account_id: int, user_id: int, cookie_data: CookieData) -> bool:
        """更新账号的Cookies"""
        try:
            account = self.get_account_by_id(account_id)
            if not account:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="账号不存在"
                )
            
            # 检查账号所有权
            if account.user_id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权限操作此账号"
                )
            
            # 更新Cookies和会话数据
            account.cookies = cookie_data.cookies
            account.session_data = cookie_data.session_data
            account.last_login = datetime.utcnow()
            
            self.db.commit()
            
            logger.info(f"账号Cookies更新成功: {account.account_name}")
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新账号Cookies时发生错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Cookies更新失败"
            )
    
    def get_account_cookies(self, account_id: int, user_id: int) -> Optional[dict]:
        """获取账号的Cookies"""
        try:
            account = self.get_account_by_id(account_id)
            if not account:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="账号不存在"
                )
            
            # 检查账号所有权
            if account.user_id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权限操作此账号"
                )
            
            if not account.cookies:
                return None
            
            return {
                "cookies": account.cookies,
                "session_data": account.session_data,
                "last_login": account.last_login
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取账号Cookies时发生错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取Cookies失败"
            )
    
    def activate_account(self, account_id: int, user_id: int) -> bool:
        """激活账号"""
        return self._toggle_account_status(account_id, user_id, True)
    
    def deactivate_account(self, account_id: int, user_id: int) -> bool:
        """停用账号"""
        return self._toggle_account_status(account_id, user_id, False)
    
    def _toggle_account_status(self, account_id: int, user_id: int, is_active: bool) -> bool:
        """切换账号状态"""
        try:
            account = self.get_account_by_id(account_id)
            if not account:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="账号不存在"
                )
            
            # 检查账号所有权
            if account.user_id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权限操作此账号"
                )
            
            account.is_active = is_active
            self.db.commit()
            
            status_text = "激活" if is_active else "停用"
            logger.info(f"账号{status_text}成功: {account.account_name}")
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"切换账号状态时发生错误: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="账号状态切换失败"
            )
