<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端后端连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 前端后端连接测试</h1>
        
        <div class="test-section info">
            <h3>测试环境信息</h3>
            <p><strong>前端地址:</strong> http://localhost:3000</p>
            <p><strong>后端地址:</strong> http://localhost:8000</p>
            <p><strong>API基础路径:</strong> /api/v1</p>
        </div>

        <div class="test-section">
            <h3>1. 直接连接测试</h3>
            <button onclick="testDirectConnection()">测试直接连接</button>
            <div id="direct-result"></div>
        </div>

        <div class="test-section">
            <h3>2. 代理连接测试</h3>
            <button onclick="testProxyConnection()">测试代理连接</button>
            <div id="proxy-result"></div>
        </div>

        <div class="test-section">
            <h3>3. 认证测试</h3>
            <button onclick="testAuth()">测试登录认证</button>
            <div id="auth-result"></div>
        </div>

        <div class="test-section">
            <h3>4. 笔记API测试</h3>
            <button onclick="testNotesAPI()">测试笔记API</button>
            <div id="notes-result"></div>
        </div>

        <div class="test-section">
            <h3>5. 完整流程测试</h3>
            <button onclick="testFullFlow()">测试完整流程</button>
            <div id="full-result"></div>
        </div>

        <div class="test-section">
            <h3>📋 测试日志</h3>
            <button onclick="clearLog()">清除日志</button>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        let authToken = null;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function showResult(elementId, success, message) {
            const element = document.getElementById(elementId);
            element.className = success ? 'success' : 'error';
            element.innerHTML = `<p>${success ? '✅' : '❌'} ${message}</p>`;
        }

        async function testDirectConnection() {
            log('开始测试直接连接...');
            try {
                const response = await fetch('http://localhost:8000/api/v1/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`直接连接成功: ${JSON.stringify(data)}`);
                    showResult('direct-result', true, '直接连接成功');
                } else {
                    log(`直接连接失败: ${response.status} ${response.statusText}`);
                    showResult('direct-result', false, `直接连接失败: ${response.status}`);
                }
            } catch (error) {
                log(`直接连接异常: ${error.message}`);
                showResult('direct-result', false, `连接异常: ${error.message}`);
            }
        }

        async function testProxyConnection() {
            log('开始测试代理连接...');
            try {
                const response = await fetch('/api/v1/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`代理连接成功: ${JSON.stringify(data)}`);
                    showResult('proxy-result', true, '代理连接成功');
                } else {
                    log(`代理连接失败: ${response.status} ${response.statusText}`);
                    showResult('proxy-result', false, `代理连接失败: ${response.status}`);
                }
            } catch (error) {
                log(`代理连接异常: ${error.message}`);
                showResult('proxy-result', false, `连接异常: ${error.message}`);
            }
        }

        async function testAuth() {
            log('开始测试认证...');
            try {
                const formData = new FormData();
                formData.append('username', 'testuser2');
                formData.append('password', 'testpass123');

                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    body: formData,
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data && data.data.access_token) {
                        authToken = data.data.access_token;
                        log(`认证成功，获得token: ${authToken.substring(0, 20)}...`);
                        showResult('auth-result', true, '认证成功');
                    } else {
                        log(`认证响应格式错误: ${JSON.stringify(data)}`);
                        showResult('auth-result', false, '认证响应格式错误');
                    }
                } else {
                    const errorText = await response.text();
                    log(`认证失败: ${response.status} ${errorText}`);
                    showResult('auth-result', false, `认证失败: ${response.status}`);
                }
            } catch (error) {
                log(`认证异常: ${error.message}`);
                showResult('auth-result', false, `认证异常: ${error.message}`);
            }
        }

        async function testNotesAPI() {
            log('开始测试笔记API...');
            
            if (!authToken) {
                log('没有认证token，先进行认证...');
                await testAuth();
                if (!authToken) {
                    showResult('notes-result', false, '无法获取认证token');
                    return;
                }
            }

            try {
                const response = await fetch('/api/v1/notes', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`,
                    },
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`笔记API成功: ${JSON.stringify(data)}`);
                    showResult('notes-result', true, `笔记API成功，获取到 ${data.data ? data.data.length : 0} 条笔记`);
                } else {
                    const errorText = await response.text();
                    log(`笔记API失败: ${response.status} ${errorText}`);
                    showResult('notes-result', false, `笔记API失败: ${response.status}`);
                }
            } catch (error) {
                log(`笔记API异常: ${error.message}`);
                showResult('notes-result', false, `笔记API异常: ${error.message}`);
            }
        }

        async function testFullFlow() {
            log('开始完整流程测试...');
            
            // 1. 测试健康检查
            log('1. 测试健康检查...');
            await testProxyConnection();
            
            // 2. 测试认证
            log('2. 测试认证...');
            await testAuth();
            
            // 3. 测试笔记API
            log('3. 测试笔记API...');
            await testNotesAPI();
            
            if (authToken) {
                showResult('full-result', true, '完整流程测试通过');
                log('✅ 完整流程测试通过');
            } else {
                showResult('full-result', false, '完整流程测试失败');
                log('❌ 完整流程测试失败');
            }
        }

        // 页面加载时自动运行基础测试
        window.onload = function() {
            log('页面加载完成，开始自动测试...');
            setTimeout(() => {
                testProxyConnection();
            }, 1000);
        };
    </script>
</body>
</html>
