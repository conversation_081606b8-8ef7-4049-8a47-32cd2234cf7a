import { request, setAuthToken, clearAuthToken } from './api';

export const authService = {
  // 用户登录
  login: async (credentials) => {
    const formData = new FormData();
    formData.append('username', credentials.username);
    formData.append('password', credentials.password);
    
    const response = await request.post('/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    
    return response;
  },

  // 用户注册
  register: async (userData) => {
    const response = await request.post('/auth/register', userData);
    return response;
  },

  // 获取当前用户信息
  getCurrentUser: async () => {
    const response = await request.get('/auth/me');
    return response;
  },

  // 修改密码
  changePassword: async (passwordData) => {
    const response = await request.post('/auth/change-password', passwordData);
    return response;
  },

  // 刷新令牌
  refreshToken: async () => {
    const response = await request.post('/auth/refresh');
    return response;
  },

  // 设置认证令牌
  setAuthToken: (token) => {
    setAuthToken(token);
  },

  // 清除认证令牌
  clearAuthToken: () => {
    clearAuthToken();
  },
};
