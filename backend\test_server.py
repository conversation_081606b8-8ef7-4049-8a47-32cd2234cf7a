"""
测试服务器启动脚本
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app.main import app
    print("✅ FastAPI应用导入成功")
    
    # 测试配置
    from app.core.config import settings
    print(f"✅ 配置加载成功: {settings.APP_NAME}")
    
    # 测试数据库连接
    from app.models import create_tables
    print("✅ 数据库模型导入成功")
    
    print("\n🚀 服务器准备就绪，可以启动了！")
    print(f"应用名称: {settings.APP_NAME}")
    print(f"版本: {settings.APP_VERSION}")
    print(f"环境: {settings.ENVIRONMENT}")
    print(f"调试模式: {settings.DEBUG}")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保已安装所有依赖包")
except Exception as e:
    print(f"❌ 其他错误: {e}")

if __name__ == "__main__":
    import uvicorn
    print("\n🔥 启动开发服务器...")
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
