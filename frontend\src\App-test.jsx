import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';

// 简单的测试组件
const TestPage = () => (
  <div style={{ padding: 20 }}>
    <h1>测试页面</h1>
    <p>应用正常工作！</p>
  </div>
);

const App = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <Routes>
          <Route path="/" element={<TestPage />} />
          <Route path="/test" element={<TestPage />} />
        </Routes>
      </Router>
    </ConfigProvider>
  );
};

export default App;
