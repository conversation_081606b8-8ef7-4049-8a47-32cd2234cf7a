from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base


class ReplyTemplate(Base):
    """回复模板模型"""
    __tablename__ = "reply_templates"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="模板名称")
    category = Column(String(50), nullable=False, comment="模板分类")
    content = Column(Text, nullable=False, comment="模板内容")
    variables = Column(JSON, nullable=True, comment="模板变量")
    tags = Column(JSON, nullable=True, comment="标签列表")
    
    # 使用统计
    usage_count = Column(Integer, default=0, comment="使用次数")
    success_rate = Column(Integer, default=0, comment="成功率百分比")
    
    # 状态控制
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_ai_generated = Column(Boolean, default=False, comment="是否AI生成")
    
    # 关联关系
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    user = relationship("User", back_populates="reply_templates")
    ai_replies = relationship("AIReply", back_populates="template")


class AIReply(Base):
    """AI回复记录模型"""
    __tablename__ = "ai_replies"

    id = Column(Integer, primary_key=True, index=True)
    comment_id = Column(Integer, ForeignKey("comments.id"), nullable=False)
    template_id = Column(Integer, ForeignKey("reply_templates.id"), nullable=True)
    
    # 回复内容
    original_content = Column(Text, nullable=False, comment="原始留言内容")
    generated_reply = Column(Text, nullable=False, comment="AI生成的回复")
    final_reply = Column(Text, nullable=True, comment="最终发送的回复")
    
    # AI参数
    model_name = Column(String(50), nullable=False, comment="使用的AI模型")
    temperature = Column(String(10), nullable=True, comment="温度参数")
    max_tokens = Column(Integer, nullable=True, comment="最大token数")
    prompt_tokens = Column(Integer, nullable=True, comment="提示token数")
    completion_tokens = Column(Integer, nullable=True, comment="完成token数")
    total_tokens = Column(Integer, nullable=True, comment="总token数")
    
    # 质量评估
    confidence_score = Column(Integer, nullable=True, comment="置信度分数")
    sentiment_score = Column(String(20), nullable=True, comment="情感分数")
    quality_rating = Column(Integer, nullable=True, comment="质量评分1-5")
    
    # 状态信息
    status = Column(String(20), default="generated", comment="状态: generated/approved/sent/rejected")
    is_used = Column(Boolean, default=False, comment="是否被使用")
    user_feedback = Column(String(20), nullable=True, comment="用户反馈: good/bad/neutral")
    
    # 关联关系
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    user = relationship("User", back_populates="ai_replies")
    comment = relationship("Comment", back_populates="ai_replies")
    template = relationship("ReplyTemplate", back_populates="ai_replies")


class AIRule(Base):
    """AI回复规则模型"""
    __tablename__ = "ai_rules"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="规则名称")
    description = Column(Text, nullable=True, comment="规则描述")
    
    # 触发条件
    keywords = Column(JSON, nullable=True, comment="关键词列表")
    sentiment_filter = Column(JSON, nullable=True, comment="情感过滤条件")
    user_filter = Column(JSON, nullable=True, comment="用户过滤条件")
    time_filter = Column(JSON, nullable=True, comment="时间过滤条件")
    
    # 回复配置
    template_ids = Column(JSON, nullable=True, comment="关联的模板ID列表")
    ai_prompt = Column(Text, nullable=True, comment="AI提示词")
    reply_style = Column(String(50), nullable=True, comment="回复风格")
    
    # 规则设置
    priority = Column(Integer, default=0, comment="优先级")
    auto_reply = Column(Boolean, default=False, comment="是否自动回复")
    require_approval = Column(Boolean, default=True, comment="是否需要审核")
    
    # 限制条件
    daily_limit = Column(Integer, nullable=True, comment="每日使用限制")
    usage_count_today = Column(Integer, default=0, comment="今日使用次数")
    
    # 状态控制
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 关联关系
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    user = relationship("User", back_populates="ai_rules")


class AIConfig(Base):
    """AI配置模型"""
    __tablename__ = "ai_configs"

    id = Column(Integer, primary_key=True, index=True)
    
    # API配置
    provider = Column(String(50), default="ollama", comment="AI提供商: openai, ollama, anthropic")
    api_key = Column(String(200), nullable=True, comment="API密钥")
    api_base_url = Column(String(200), nullable=True, comment="API基础URL")

    # Ollama配置
    ollama_base_url = Column(String(200), nullable=True, comment="Ollama服务地址")
    ollama_model = Column(String(100), nullable=True, comment="Ollama模型名称")
    ollama_timeout = Column(Integer, default=60, comment="Ollama请求超时时间")
    
    # 模型配置
    default_model = Column(String(50), default="gpt-3.5-turbo", comment="默认模型")
    temperature = Column(String(10), default="0.7", comment="温度参数")
    max_tokens = Column(Integer, default=150, comment="最大token数")
    
    # 回复配置
    reply_language = Column(String(10), default="zh", comment="回复语言")
    reply_tone = Column(String(20), default="friendly", comment="回复语调")
    include_emoji = Column(Boolean, default=True, comment="是否包含表情")
    
    # 安全配置
    content_filter = Column(Boolean, default=True, comment="是否启用内容过滤")
    max_daily_requests = Column(Integer, default=1000, comment="每日最大请求数")
    current_daily_requests = Column(Integer, default=0, comment="当前每日请求数")
    
    # 成本控制
    max_monthly_cost = Column(Integer, default=100, comment="每月最大成本(美元)")
    current_monthly_cost = Column(Integer, default=0, comment="当前月成本(分)")
    
    # 关联关系
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    user = relationship("User", back_populates="ai_config")


# 更新User模型的关系（需要在user.py中添加）
"""
在User模型中添加以下关系：
reply_templates = relationship("ReplyTemplate", back_populates="user")
ai_replies = relationship("AIReply", back_populates="user")
ai_rules = relationship("AIRule", back_populates="user")
ai_config = relationship("AIConfig", back_populates="user", uselist=False)
"""

# 更新Comment模型的关系（需要在comment.py中添加）
"""
在Comment模型中添加以下关系：
ai_replies = relationship("AIReply", back_populates="comment")
"""
