#!/usr/bin/env python3
"""
检查comments表结构
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.database import engine
from sqlalchemy import text, inspect

def check_comments_table():
    """检查comments表结构"""
    try:
        with engine.connect() as conn:
            inspector = inspect(engine)
            
            print("🔍 检查comments表结构")
            print("=" * 50)
            
            # 检查表是否存在
            if 'comments' not in inspector.get_table_names():
                print("❌ comments表不存在")
                return
            
            # 获取表结构
            columns = inspector.get_columns('comments')
            print(f"📋 comments表结构 ({len(columns)} 列):")
            for col in columns:
                col_type = str(col['type'])
                nullable = "NULL" if col['nullable'] else "NOT NULL"
                default = f" DEFAULT {col['default']}" if col['default'] else ""
                print(f"   - {col['name']}: {col_type} {nullable}{default}")
            
            # 检查枚举类型
            print(f"\n🔍 检查枚举类型:")
            enums_result = conn.execute(text("""
                SELECT t.typname, e.enumlabel
                FROM pg_type t 
                JOIN pg_enum e ON t.oid = e.enumtypid  
                WHERE t.typname LIKE '%status%'
                ORDER BY t.typname, e.enumsortorder
            """))
            
            enums = enums_result.fetchall()
            current_enum = None
            for enum in enums:
                if enum.typname != current_enum:
                    current_enum = enum.typname
                    print(f"\n   📄 {enum.typname}:")
                print(f"      - {enum.enumlabel}")
            
            # 查看现有数据的status值
            print(f"\n📊 现有留言的status值:")
            status_result = conn.execute(text("""
                SELECT status, COUNT(*) as count
                FROM comments
                GROUP BY status
                ORDER BY count DESC
            """))
            
            statuses = status_result.fetchall()
            for status in statuses:
                print(f"   - {status.status}: {status.count} 条")
            
            # 查看示例数据
            print(f"\n💬 现有留言示例:")
            sample_result = conn.execute(text("""
                SELECT id, comment_id, user_name, content, status, created_at
                FROM comments
                ORDER BY created_at DESC
                LIMIT 5
            """))
            
            samples = sample_result.fetchall()
            for sample in samples:
                print(f"   - ID: {sample.id}, 状态: {sample.status}")
                print(f"     用户: {sample.user_name}, 内容: {sample.content[:30]}...")
                print(f"     时间: {sample.created_at}")
                print()
                
    except Exception as e:
        print(f"❌ 检查comments表失败: {e}")

if __name__ == "__main__":
    check_comments_table()
