import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Typography, Divider, Space, message, Tabs } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, RobotOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../stores/authStore';
import './LoginPage.css';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const LoginPage = () => {
  const [loginForm] = Form.useForm();
  const [registerForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState('login');
  const navigate = useNavigate();
  const location = useLocation();
  const { login, register, loading, error, clearError } = useAuthStore();

  // 获取重定向路径
  const from = location.state?.from?.pathname || '/';

  useEffect(() => {
    // 清除之前的错误
    clearError();
  }, [clearError]);

  // 处理登录
  const handleLogin = async (values) => {
    const result = await login(values);
    if (result.success) {
      message.success('登录成功！');
      navigate(from, { replace: true });
    }
  };

  // 处理注册
  const handleRegister = async (values) => {
    const { confirmPassword, ...registerData } = values;
    const result = await register(registerData);
    if (result.success) {
      message.success('注册成功！');
      navigate(from, { replace: true });
    }
  };

  // 切换标签页时清除错误
  const handleTabChange = (key) => {
    setActiveTab(key);
    clearError();
  };

  return (
    <div className="login-page">
      <div className="login-background">
        <div className="background-pattern"></div>
      </div>
      
      <div className="login-container">
        <Card className="login-card" bordered={false}>
          <div className="login-header">
            <div className="logo">
              <RobotOutlined className="logo-icon" />
              <Title level={2} className="logo-title">
                小红书助手
              </Title>
            </div>
            <Text type="secondary" className="subtitle">
              智能化的小红书留言管理工具
            </Text>
          </div>

          <Tabs 
            activeKey={activeTab} 
            onChange={handleTabChange}
            centered
            className="auth-tabs"
          >
            <TabPane tab="登录" key="login">
              <Form
                form={loginForm}
                name="login"
                onFinish={handleLogin}
                autoComplete="off"
                size="large"
                className="auth-form"
              >
                <Form.Item
                  name="username"
                  rules={[
                    { required: true, message: '请输入用户名或邮箱' },
                    { min: 3, message: '用户名至少3个字符' },
                  ]}
                >
                  <Input
                    prefix={<UserOutlined />}
                    placeholder="用户名或邮箱"
                    autoComplete="username"
                  />
                </Form.Item>

                <Form.Item
                  name="password"
                  rules={[
                    { required: true, message: '请输入密码' },
                    { min: 6, message: '密码至少6个字符' },
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="密码"
                    autoComplete="current-password"
                  />
                </Form.Item>

                {error && (
                  <div className="error-message">
                    <Text type="danger">{error}</Text>
                  </div>
                )}

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    block
                    className="submit-button"
                  >
                    登录
                  </Button>
                </Form.Item>
              </Form>
            </TabPane>

            <TabPane tab="注册" key="register">
              <Form
                form={registerForm}
                name="register"
                onFinish={handleRegister}
                autoComplete="off"
                size="large"
                className="auth-form"
              >
                <Form.Item
                  name="username"
                  rules={[
                    { required: true, message: '请输入用户名' },
                    { min: 3, message: '用户名至少3个字符' },
                    { max: 20, message: '用户名最多20个字符' },
                    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' },
                  ]}
                >
                  <Input
                    prefix={<UserOutlined />}
                    placeholder="用户名"
                    autoComplete="username"
                  />
                </Form.Item>

                <Form.Item
                  name="email"
                  rules={[
                    { required: true, message: '请输入邮箱地址' },
                    { type: 'email', message: '请输入有效的邮箱地址' },
                  ]}
                >
                  <Input
                    prefix={<MailOutlined />}
                    placeholder="邮箱地址"
                    autoComplete="email"
                  />
                </Form.Item>

                <Form.Item
                  name="full_name"
                  rules={[
                    { required: true, message: '请输入姓名' },
                    { min: 2, message: '姓名至少2个字符' },
                    { max: 50, message: '姓名最多50个字符' },
                  ]}
                >
                  <Input
                    prefix={<UserOutlined />}
                    placeholder="姓名"
                    autoComplete="name"
                  />
                </Form.Item>

                <Form.Item
                  name="password"
                  rules={[
                    { required: true, message: '请输入密码' },
                    { min: 6, message: '密码至少6个字符' },
                    { max: 50, message: '密码最多50个字符' },
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="密码"
                    autoComplete="new-password"
                  />
                </Form.Item>

                <Form.Item
                  name="confirmPassword"
                  dependencies={['password']}
                  rules={[
                    { required: true, message: '请确认密码' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('password') === value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error('两次输入的密码不一致'));
                      },
                    }),
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="确认密码"
                    autoComplete="new-password"
                  />
                </Form.Item>

                {error && (
                  <div className="error-message">
                    <Text type="danger">{error}</Text>
                  </div>
                )}

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    block
                    className="submit-button"
                  >
                    注册
                  </Button>
                </Form.Item>
              </Form>
            </TabPane>
          </Tabs>

          <Divider />
          
          <div className="login-footer">
            <Space direction="vertical" align="center" size="small">
              <Text type="secondary" className="footer-text">
                © 2024 小红书助手. 保留所有权利.
              </Text>
              <Space split={<Divider type="vertical" />}>
                <Text type="secondary" className="footer-link">
                  使用条款
                </Text>
                <Text type="secondary" className="footer-link">
                  隐私政策
                </Text>
                <Text type="secondary" className="footer-link">
                  帮助中心
                </Text>
              </Space>
            </Space>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default LoginPage;
