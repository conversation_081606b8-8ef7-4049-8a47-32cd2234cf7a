import { request } from './api';

export const notesService = {
  // 获取笔记列表
  getNotes: async (params = {}) => {
    const response = await request.get('/notes', { params });
    return response;
  },

  // 获取笔记详情
  getNoteById: async (id) => {
    const response = await request.get(`/notes/${id}`);
    return response;
  },

  // 创建笔记
  createNote: async (noteData) => {
    const response = await request.post('/notes', noteData);
    return response;
  },

  // 更新笔记
  updateNote: async (id, noteData) => {
    const response = await request.put(`/notes/${id}`, noteData);
    return response;
  },

  // 删除笔记
  deleteNote: async (id) => {
    const response = await request.delete(`/notes/${id}`);
    return response;
  },

  // 批量操作笔记
  batchOperation: async (operation, noteIds) => {
    const response = await request.post('/notes/batch', {
      operation,
      note_ids: noteIds,
    });
    return response;
  },

  // 获取笔记统计
  getNoteStats: async (id) => {
    const response = await request.get(`/notes/${id}/stats`);
    return response;
  },

  // 切换监控状态
  toggleMonitoring: async (id, isMonitoring) => {
    const response = await request.put(`/notes/${id}`, {
      is_monitoring: isMonitoring,
    });
    return response;
  },

  // 手动抓取笔记留言
  crawlNote: async (id) => {
    const response = await request.post(`/notes/${id}/crawl`);
    return response;
  },
};
