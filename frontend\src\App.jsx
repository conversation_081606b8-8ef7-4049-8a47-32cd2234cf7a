import React, { useEffect, Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, App as AntdApp, theme } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { useAuthStore } from './stores/authStore';
import { useThemeStore } from './stores/themeStore';

console.log('App.jsx 开始加载');

// 基础组件导入
console.log('导入基础组件...');
import MainLayout from './components/Layout/MainLayout';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import LoginPage from './pages/Auth/LoginPage';
import DashboardPage from './pages/Dashboard/DashboardPage';
console.log('基础组件导入完成');

// 其他页面组件使用懒加载
console.log('设置懒加载组件...');
const AccountsPage = React.lazy(() => import('./pages/Accounts/AccountsPage'));
const NotesPage = React.lazy(() => import('./pages/Notes/NotesPage'));
const CommentsPage = React.lazy(() => import('./pages/Comments/CommentsPage'));
const CrawlerPage = React.lazy(() => import('./pages/Crawler/CrawlerPage'));
const SettingsPage = React.lazy(() => import('./pages/Settings/SettingsPage'));
const TemplatesPage = React.lazy(() => import('./pages/AI/TemplatesPage'));
const ConfigPage = React.lazy(() => import('./pages/AI/ConfigPage'));
const AnalyticsPage = React.lazy(() => import('./pages/Analytics/AnalyticsPage'));
console.log('懒加载组件设置完成');

// 样式导入
import './App.css';
import './styles/themes.css';

function App() {
  console.log('App 组件开始渲染');
  const { initAuth } = useAuthStore();
  const { currentTheme, isDark, initTheme, getCurrentTheme } = useThemeStore();

  useEffect(() => {
    console.log('App useEffect: 开始初始化认证');
    // 应用启动时初始化认证状态
    initAuth();
    console.log('App useEffect: 认证初始化完成');
  }, [initAuth]);

  useEffect(() => {
    console.log('App useEffect: 开始初始化主题');
    // 应用启动时初始化主题
    initTheme();
    console.log('App useEffect: 主题初始化完成');
  }, [initTheme]);

  // 获取当前主题配置
  const currentThemeConfig = getCurrentTheme();

  // Ant Design主题配置
  const antdThemeConfig = {
    algorithm: isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,
    token: {
      colorPrimary: currentThemeConfig.colors.primary,
      borderRadius: 6,
      colorBgBase: currentThemeConfig.colors.componentBg,
      colorTextBase: currentThemeConfig.colors.textPrimary,
      colorBorder: currentThemeConfig.colors.borderColor,
      colorBgContainer: currentThemeConfig.colors.componentBg,
      colorBgLayout: currentThemeConfig.colors.layoutBg,
    },
  };

  console.log('App 组件返回JSX');

  return (
    <ConfigProvider
      locale={zhCN}
      theme={antdThemeConfig}
    >
      <AntdApp>
        <Router>
          <div className="app">
            <Routes>
              {/* 登录页面 */}
              <Route path="/login" element={<LoginPage />} />

              {/* 受保护的路由 - 临时禁用保护 */}
              <Route path="/*" element={
                <MainLayout>
                  <Suspense fallback={<div style={{padding: '20px', textAlign: 'center'}}>加载中...</div>}>
                    <Routes>
                        {/* 仪表盘 */}
                        <Route path="/" element={<DashboardPage />} />

                        {/* 账号管理 */}
                        <Route path="/accounts" element={<AccountsPage />} />

                        {/* 笔记管理 */}
                        <Route path="/notes" element={<NotesPage />} />

                        {/* 留言管理 */}
                        <Route path="/comments" element={<CommentsPage />} />

                        {/* 爬虫管理 */}
                        <Route path="/crawler" element={<CrawlerPage />} />

                        {/* 系统设置 */}
                        <Route path="/settings" element={<SettingsPage />} />

                        {/* AI功能 */}
                        <Route path="/ai/templates" element={<TemplatesPage />} />
                        <Route path="/ai/config" element={<ConfigPage />} />

                        {/* 数据分析 */}
                        <Route path="/analytics" element={<AnalyticsPage />} />

                        {/* 个人资料 */}
                        <Route path="/profile" element={<div>个人资料页面开发中...</div>} />

                        {/* 404页面 */}
                        <Route path="*" element={<Navigate to="/" replace />} />
                    </Routes>
                  </Suspense>
                </MainLayout>
              } />
            </Routes>
          </div>
        </Router>
      </AntdApp>
    </ConfigProvider>
  );
}

export default App;
