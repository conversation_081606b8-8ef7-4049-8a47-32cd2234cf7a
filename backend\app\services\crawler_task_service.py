"""
爬虫任务服务
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func
from datetime import datetime, timedelta
import json
import logging

from ..models.crawler_task import CrawlerTask, CrawlerTaskLog, CrawlerConfig, TaskStatus, TaskType
from ..models.user import User
from ..models.note import Note
from ..models.user import XiaohongshuAccount

logger = logging.getLogger(__name__)


class CrawlerTaskService:
    """爬虫任务服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_task(
        self,
        user_id: int,
        account_id: int,
        task_name: str,
        task_type: TaskType = TaskType.MANUAL,
        note_id: Optional[int] = None,
        target_urls: Optional[List[str]] = None,
        crawl_config: Optional[Dict[str, Any]] = None
    ) -> CrawlerTask:
        """创建爬虫任务"""
        try:
            task = CrawlerTask(
                user_id=user_id,
                account_id=account_id,
                note_id=note_id,
                task_name=task_name,
                task_type=task_type,
                status=TaskStatus.PENDING,
                target_urls=json.dumps(target_urls) if target_urls else None,
                crawl_config=json.dumps(crawl_config) if crawl_config else None,
                created_at=datetime.utcnow()
            )
            
            self.db.add(task)
            self.db.commit()
            self.db.refresh(task)
            
            # 记录日志
            self.add_task_log(
                task.id,
                "info",
                f"任务创建成功: {task_name}",
                {"task_type": task_type.value, "account_id": account_id}
            )
            
            logger.info(f"创建爬虫任务成功: {task.id} - {task_name}")
            return task
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建爬虫任务失败: {e}")
            raise
    
    def get_tasks_by_user(
        self,
        user_id: int,
        status: Optional[TaskStatus] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[CrawlerTask]:
        """获取用户的爬虫任务列表"""
        try:
            query = self.db.query(CrawlerTask).filter(CrawlerTask.user_id == user_id)
            
            if status:
                query = query.filter(CrawlerTask.status == status)
            
            tasks = query.order_by(desc(CrawlerTask.created_at)).offset(offset).limit(limit).all()
            return tasks
            
        except Exception as e:
            logger.error(f"获取用户任务列表失败: {e}")
            return []
    
    def get_task_by_id(self, task_id: int, user_id: int) -> Optional[CrawlerTask]:
        """根据ID获取任务"""
        try:
            task = self.db.query(CrawlerTask).filter(
                and_(CrawlerTask.id == task_id, CrawlerTask.user_id == user_id)
            ).first()
            return task
        except Exception as e:
            logger.error(f"获取任务详情失败: {e}")
            return None
    
    def update_task_status(
        self,
        task_id: int,
        status: TaskStatus,
        error_message: Optional[str] = None,
        progress: Optional[int] = None
    ) -> bool:
        """更新任务状态"""
        try:
            task = self.db.query(CrawlerTask).filter(CrawlerTask.id == task_id).first()
            if not task:
                return False
            
            old_status = task.status
            task.status = status
            task.last_update = datetime.utcnow()
            
            if error_message:
                task.error_message = error_message
                task.error_count = (task.error_count or 0) + 1
            
            if progress is not None:
                task.progress = progress
            
            # 设置开始和结束时间
            if status == TaskStatus.RUNNING and not task.start_time:
                task.start_time = datetime.utcnow()
            elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                task.end_time = datetime.utcnow()
            
            self.db.commit()
            
            # 记录状态变更日志
            self.add_task_log(
                task_id,
                "info" if status == TaskStatus.COMPLETED else "warning" if status == TaskStatus.FAILED else "info",
                f"任务状态变更: {old_status.value} -> {status.value}",
                {"old_status": old_status.value, "new_status": status.value, "error_message": error_message}
            )
            
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新任务状态失败: {e}")
            return False
    
    def update_task_progress(
        self,
        task_id: int,
        progress: int,
        processed_items: Optional[int] = None,
        success_items: Optional[int] = None,
        failed_items: Optional[int] = None,
        comments_found: Optional[int] = None,
        new_comments: Optional[int] = None
    ) -> bool:
        """更新任务进度"""
        try:
            task = self.db.query(CrawlerTask).filter(CrawlerTask.id == task_id).first()
            if not task:
                return False
            
            task.progress = progress
            task.last_update = datetime.utcnow()
            
            if processed_items is not None:
                task.processed_items = processed_items
            if success_items is not None:
                task.success_items = success_items
            if failed_items is not None:
                task.failed_items = failed_items
            if comments_found is not None:
                task.comments_found = comments_found
            if new_comments is not None:
                task.new_comments = new_comments
            
            self.db.commit()
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新任务进度失败: {e}")
            return False
    
    def add_task_log(
        self,
        task_id: int,
        level: str,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        note_id: Optional[int] = None,
        url: Optional[str] = None,
        response_time: Optional[float] = None
    ) -> bool:
        """添加任务日志"""
        try:
            log = CrawlerTaskLog(
                task_id=task_id,
                level=level,
                message=message,
                details=json.dumps(details) if details else None,
                note_id=note_id,
                url=url,
                response_time=response_time,
                created_at=datetime.utcnow()
            )
            
            self.db.add(log)
            self.db.commit()
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"添加任务日志失败: {e}")
            return False
    
    def get_task_logs(
        self,
        task_id: Optional[int] = None,
        user_id: Optional[int] = None,
        level: Optional[str] = None,
        limit: int = 100
    ) -> List[CrawlerTaskLog]:
        """获取任务日志"""
        try:
            query = self.db.query(CrawlerTaskLog)
            
            if task_id:
                query = query.filter(CrawlerTaskLog.task_id == task_id)
            elif user_id:
                # 通过用户ID获取该用户所有任务的日志
                query = query.join(CrawlerTask).filter(CrawlerTask.user_id == user_id)
            
            if level:
                query = query.filter(CrawlerTaskLog.level == level)
            
            logs = query.order_by(desc(CrawlerTaskLog.created_at)).limit(limit).all()
            return logs
            
        except Exception as e:
            logger.error(f"获取任务日志失败: {e}")
            return []
    
    def get_task_statistics(self, user_id: int) -> Dict[str, Any]:
        """获取任务统计信息"""
        try:
            # 基本统计
            total_tasks = self.db.query(CrawlerTask).filter(CrawlerTask.user_id == user_id).count()
            running_tasks = self.db.query(CrawlerTask).filter(
                and_(CrawlerTask.user_id == user_id, CrawlerTask.status == TaskStatus.RUNNING)
            ).count()
            completed_tasks = self.db.query(CrawlerTask).filter(
                and_(CrawlerTask.user_id == user_id, CrawlerTask.status == TaskStatus.COMPLETED)
            ).count()
            failed_tasks = self.db.query(CrawlerTask).filter(
                and_(CrawlerTask.user_id == user_id, CrawlerTask.status == TaskStatus.FAILED)
            ).count()
            
            # 评论统计
            comments_stats = self.db.query(
                func.sum(CrawlerTask.comments_found).label('total_comments'),
                func.sum(CrawlerTask.new_comments).label('new_comments')
            ).filter(CrawlerTask.user_id == user_id).first()
            
            total_comments = comments_stats.total_comments or 0
            new_comments = comments_stats.new_comments or 0
            
            # 成功率计算
            success_rate = 0
            if total_tasks > 0:
                success_rate = round((completed_tasks / total_tasks) * 100, 1)
            
            # 平均响应时间
            avg_response_time = self.db.query(
                func.avg(CrawlerTask.avg_response_time)
            ).filter(
                and_(
                    CrawlerTask.user_id == user_id,
                    CrawlerTask.avg_response_time.isnot(None)
                )
            ).scalar() or 0.0
            
            return {
                "total_tasks": total_tasks,
                "running_tasks": running_tasks,
                "completed_tasks": completed_tasks,
                "failed_tasks": failed_tasks,
                "total_comments": total_comments,
                "new_comments": new_comments,
                "success_rate": success_rate,
                "avg_response_time": round(avg_response_time, 2),
                "crawler_status": "running" if running_tasks > 0 else "stopped"
            }
            
        except Exception as e:
            logger.error(f"获取任务统计失败: {e}")
            return {
                "total_tasks": 0,
                "running_tasks": 0,
                "completed_tasks": 0,
                "failed_tasks": 0,
                "total_comments": 0,
                "new_comments": 0,
                "success_rate": 0,
                "avg_response_time": 0.0,
                "crawler_status": "stopped"
            }
    
    def delete_task(self, task_id: int, user_id: int) -> bool:
        """删除任务"""
        try:
            task = self.db.query(CrawlerTask).filter(
                and_(CrawlerTask.id == task_id, CrawlerTask.user_id == user_id)
            ).first()
            
            if not task:
                return False
            
            # 只能删除非运行状态的任务
            if task.status == TaskStatus.RUNNING:
                return False
            
            self.db.delete(task)
            self.db.commit()
            
            logger.info(f"删除爬虫任务成功: {task_id}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除爬虫任务失败: {e}")
            return False
    
    def clear_completed_tasks(self, user_id: int, days_old: int = 7) -> int:
        """清理已完成的旧任务"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_old)
            
            deleted_count = self.db.query(CrawlerTask).filter(
                and_(
                    CrawlerTask.user_id == user_id,
                    CrawlerTask.status == TaskStatus.COMPLETED,
                    CrawlerTask.end_time < cutoff_date
                )
            ).delete()
            
            self.db.commit()
            
            logger.info(f"清理已完成任务: 用户 {user_id}, 删除 {deleted_count} 个任务")
            return deleted_count
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"清理已完成任务失败: {e}")
            return 0
