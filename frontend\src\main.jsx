import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.jsx'
import './index.css'

console.log('main.jsx 开始执行');

const rootElement = document.getElementById('root');
console.log('Root元素:', rootElement);

if (rootElement) {
  console.log('创建React根并渲染App');
  ReactDOM.createRoot(rootElement).render(
    <React.StrictMode>
      <App />
    </React.StrictMode>,
  );
  console.log('App 渲染完成');
} else {
  console.error('❌ 找不到root元素！');
  document.body.innerHTML = '<h1 style="color: red;">错误：找不到root元素！</h1>';
}
