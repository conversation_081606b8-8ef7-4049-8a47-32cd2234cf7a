<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试页面</title>
</head>
<body>
    <h1>简单测试页面</h1>
    <div id="root">这里应该是React应用</div>
    
    <script>
        console.log('简单测试页面加载成功');
        console.log('Root元素:', document.getElementById('root'));
        
        // 检查是否能访问主页面的HTML
        fetch('/')
            .then(response => response.text())
            .then(html => {
                console.log('主页面HTML长度:', html.length);
                console.log('主页面HTML前100字符:', html.substring(0, 100));
                
                // 检查是否包含root元素
                if (html.includes('id="root"')) {
                    console.log('✅ 主页面HTML包含root元素');
                } else {
                    console.log('❌ 主页面HTML不包含root元素');
                }
                
                // 显示在页面上
                document.body.innerHTML += '<h2>主页面HTML分析:</h2>';
                document.body.innerHTML += '<p>HTML长度: ' + html.length + '</p>';
                document.body.innerHTML += '<p>包含root元素: ' + (html.includes('id="root"') ? '是' : '否') + '</p>';
                document.body.innerHTML += '<pre>' + html.substring(0, 500) + '</pre>';
            })
            .catch(error => {
                console.error('获取主页面失败:', error);
                document.body.innerHTML += '<p style="color: red;">获取主页面失败: ' + error.message + '</p>';
            });
    </script>
</body>
</html>
