# 数据库配置
DATABASE_URL=postgresql://postgres:password123@localhost:5432/xia<PERSON><PERSON><PERSON>_auto_reply

# JWT密钥
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# OpenAI API配置
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo

# 小红书配置
XIAOHONGSHU_LOGIN_URL=https://www.xiaohongshu.com/explore
XIAOHONGSHU_BASE_URL=https://www.xiaohongshu.com

# 爬虫配置
CRAWLER_DELAY_MIN=2
CRAWLER_DELAY_MAX=5
CRAWLER_TIMEOUT=30

# Redis配置
REDIS_PASSWORD=redis123
REDIS_URL=redis://:redis123@localhost:6379/0

# 数据库密码
DB_PASSWORD=postgres123

# CORS配置
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:8000

# OpenAI扩展配置
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_TEMPERATURE=0.7
OPENAI_MAX_TOKENS=150
OPENAI_MAX_REQUESTS_PER_DAY=1000

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_TLS=true
SMTP_SSL=false

# 监控配置
GRAFANA_PASSWORD=admin123

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
LOG_FORMAT=json

# 文件上传配置
MAX_UPLOAD_SIZE=10485760  # 10MB
UPLOAD_PATH=./uploads

# 缓存配置
CACHE_TTL=3600  # 1小时
CACHE_MAX_SIZE=1000

# 安全配置
ENABLE_RATE_LIMITING=true
RATE_LIMIT_PER_MINUTE=60
ENABLE_CSRF_PROTECTION=true

# 爬虫扩展配置
CRAWLER_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
CRAWLER_MAX_RETRIES=3

# 备份配置
BACKUP_RETENTION_DAYS=30
AUTO_BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # 每天凌晨2点

# 性能配置
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# WebSocket配置
WS_MAX_CONNECTIONS=1000
WS_HEARTBEAT_INTERVAL=30

# AI配置扩展
AI_REPLY_CACHE_TTL=3600
AI_BATCH_SIZE=10
AI_CONCURRENT_REQUESTS=5
AI_RETRY_ATTEMPTS=3
AI_RETRY_DELAY=1

# 监控和告警
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_HEALTH_CHECK=true
HEALTH_CHECK_INTERVAL=30

# 开发环境配置
DEBUG=True
ENVIRONMENT=development
ENABLE_DEBUG_TOOLBAR=false
ENABLE_PROFILING=false
ENABLE_SQL_ECHO=false
