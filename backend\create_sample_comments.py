#!/usr/bin/env python3
"""
创建示例留言数据
"""
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.database import SessionLocal
from app.models.user import User
from app.models.note import Note
from sqlalchemy import text

def create_sample_comments():
    """创建示例留言数据"""
    db = SessionLocal()
    
    try:
        # 查找testuser2和相关笔记
        test_user = db.query(User).filter(User.username == "testuser2").first()
        if not test_user:
            print("❌ 找不到testuser2用户")
            return
        
        print(f"✅ 找到testuser2用户，ID: {test_user.id}")
        
        # 查找所有笔记（用于测试）
        notes = db.query(Note).all()
        if not notes:
            print("❌ 数据库中没有笔记")
            return
        
        print(f"📝 找到 {len(notes)} 个笔记")
        
        # 创建示例留言数据
        print("\n💬 创建示例留言...")
        
        comments_data = [
            {
                "note_id": notes[0].id,
                "comment_id": "comment_ai_001",
                "content": "谢谢分享！这个护肤心得太有用了，我也要试试看",
                "user_name": "小美同学",
                "user_id": "user_001",
                "parent_comment_id": None,
                "publish_time": datetime.now() - timedelta(hours=2),
                "status": "NEW",
                "reply_content": None,
                "reply_time": None
            },
            {
                "note_id": notes[0].id,
                "comment_id": "comment_ai_002",
                "content": "请问这个面膜是什么牌子的？哪里可以买到？",
                "user_name": "护肤小白",
                "user_id": "user_002",
                "parent_comment_id": None,
                "publish_time": datetime.now() - timedelta(hours=1),
                "status": "NEW",
                "reply_content": None,
                "reply_time": None
            },
            {
                "note_id": notes[0].id,
                "comment_id": "comment_ai_003",
                "content": "太棒了！我的肌肤也是敏感肌，正好需要这样的护肤建议",
                "user_name": "敏感肌女孩",
                "user_id": "user_003",
                "parent_comment_id": None,
                "publish_time": datetime.now() - timedelta(minutes=30),
                "status": "NEW",
                "reply_content": None,
                "reply_time": None
            },
            {
                "note_id": notes[0].id,
                "comment_id": "comment_ai_004",
                "content": "关于干性肌肤的保养，能推荐一些好用的精华吗？",
                "user_name": "干皮救星",
                "user_id": "user_004",
                "parent_comment_id": None,
                "publish_time": datetime.now() - timedelta(minutes=15),
                "status": "NEW",
                "reply_content": None,
                "reply_time": None
            },
            {
                "note_id": notes[0].id,
                "comment_id": "comment_ai_005",
                "content": "很喜欢你的分享风格，继续加油！期待更多美妆内容",
                "user_name": "美妆爱好者",
                "user_id": "user_005",
                "parent_comment_id": None,
                "publish_time": datetime.now() - timedelta(minutes=5),
                "status": "NEW",
                "reply_content": None,
                "reply_time": None
            }
        ]
        
        # 插入留言数据
        for comment_data in comments_data:
            # 使用原生SQL插入，因为我们没有Comment模型
            insert_sql = text("""
                INSERT INTO comments (
                    note_id, comment_id, content, user_name, user_id, 
                    parent_comment_id, publish_time, status, reply_content, reply_time,
                    created_at, updated_at
                ) VALUES (
                    :note_id, :comment_id, :content, :user_name, :user_id,
                    :parent_comment_id, :publish_time, :status, :reply_content, :reply_time,
                    :created_at, :updated_at
                )
            """)
            
            db.execute(insert_sql, {
                **comment_data,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            })
            
            print(f"   ✅ 创建留言: {comment_data['content'][:20]}...")
        
        # 提交更改
        db.commit()
        
        # 验证创建结果
        print(f"\n🎯 验证创建结果:")
        
        # 查询留言数量
        count_result = db.execute(text("SELECT COUNT(*) FROM comments WHERE note_id = :note_id"), 
                                {"note_id": notes[0].id})
        comment_count = count_result.scalar()
        
        print(f"   💬 笔记 '{notes[0].title}' 的留言数: {comment_count} 条")
        
        # 显示留言列表
        comments_result = db.execute(text("""
            SELECT comment_id, user_name, content, status, publish_time 
            FROM comments 
            WHERE note_id = :note_id 
            ORDER BY publish_time DESC
        """), {"note_id": notes[0].id})
        
        comments = comments_result.fetchall()
        print(f"\n💬 留言列表:")
        for comment in comments:
            status_icon = "⏳" if comment.status == "pending" else "✅"
            print(f"   {status_icon} {comment.user_name}: {comment.content[:30]}...")
            print(f"      时间: {comment.publish_time}")
        
        print(f"\n🎉 示例留言数据创建完成！")
        print(f"现在可以测试AI回复功能了")
        
    except Exception as e:
        print(f"❌ 创建示例留言失败: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("💬 创建示例留言数据")
    print("=" * 50)
    create_sample_comments()
