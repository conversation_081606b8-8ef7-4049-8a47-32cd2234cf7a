"""
性能测试
"""
import pytest
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
from fastapi.testclient import TestClient
from unittest.mock import patch, AsyncMock


class TestPerformance:
    """性能测试类"""
    
    def test_api_response_time(self, client: TestClient, auth_headers):
        """测试API响应时间"""
        start_time = time.time()
        
        response = client.get(
            "/api/v1/auth/me",
            headers=auth_headers
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        assert response.status_code == 200
        assert response_time < 1.0  # 响应时间应小于1秒
    
    def test_concurrent_requests(self, client: TestClient, auth_headers):
        """测试并发请求"""
        def make_request():
            response = client.get(
                "/api/v1/auth/me",
                headers=auth_headers
            )
            return response.status_code == 200
        
        # 并发执行10个请求
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(10)]
            results = [future.result() for future in futures]
        
        # 所有请求都应该成功
        assert all(results)
    
    def test_database_query_performance(self, client: TestClient, auth_headers, test_factory, db_session):
        """测试数据库查询性能"""
        # 创建大量测试数据
        user = test_factory.create_user(db_session, username="perfuser", email="<EMAIL>")
        account = test_factory.create_account(db_session, user.id)
        note = test_factory.create_note(db_session, account.id)
        
        # 创建100条留言
        for i in range(100):
            test_factory.create_comment(
                db_session, 
                note.id, 
                user.id, 
                comment_id=f"comment_{i}",
                content=f"测试留言 {i}"
            )
        
        start_time = time.time()
        
        # 查询留言列表
        response = client.get(
            "/api/v1/comments/",
            headers=auth_headers,
            params={"limit": 50}
        )
        
        end_time = time.time()
        query_time = end_time - start_time
        
        assert response.status_code == 200
        assert query_time < 2.0  # 查询时间应小于2秒
        
        data = response.json()
        assert len(data["data"]["comments"]) <= 50
    
    @patch('app.services.ai_service.openai.ChatCompletion.acreate')
    def test_ai_reply_generation_performance(self, mock_openai, client: TestClient, auth_headers, test_ai_config):
        """测试AI回复生成性能"""
        # Mock OpenAI响应
        mock_response = AsyncMock()
        mock_response.choices = [AsyncMock()]
        mock_response.choices[0].message.content = "测试回复"
        mock_response.usage.total_tokens = 50
        mock_openai.return_value = mock_response
        
        start_time = time.time()
        
        response = client.post(
            "/api/v1/ai/generate-reply",
            headers=auth_headers,
            json={
                "comment_content": "你的笔记很棒！"
            }
        )
        
        end_time = time.time()
        generation_time = end_time - start_time
        
        assert response.status_code == 200
        assert generation_time < 5.0  # AI生成时间应小于5秒
    
    def test_batch_operations_performance(self, client: TestClient, auth_headers, test_factory, db_session):
        """测试批量操作性能"""
        # 创建测试数据
        user = test_factory.create_user(db_session, username="batchuser", email="<EMAIL>")
        account = test_factory.create_account(db_session, user.id)
        note = test_factory.create_note(db_session, account.id)
        
        # 创建50条留言
        comment_ids = []
        for i in range(50):
            comment = test_factory.create_comment(
                db_session, 
                note.id, 
                user.id, 
                comment_id=f"batch_comment_{i}",
                content=f"批量测试留言 {i}"
            )
            comment_ids.append(comment.id)
        
        start_time = time.time()
        
        # 批量标记为已读
        response = client.post(
            "/api/v1/comments/batch-mark-read",
            headers=auth_headers,
            json={"comment_ids": comment_ids}
        )
        
        end_time = time.time()
        batch_time = end_time - start_time
        
        assert response.status_code == 200
        assert batch_time < 3.0  # 批量操作时间应小于3秒
    
    def test_memory_usage(self, client: TestClient, auth_headers):
        """测试内存使用情况"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行多个请求
        for _ in range(100):
            response = client.get(
                "/api/v1/auth/me",
                headers=auth_headers
            )
            assert response.status_code == 200
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # 内存增长应该在合理范围内（小于50MB）
        assert memory_increase < 50
    
    def test_cache_performance(self, client: TestClient, auth_headers, test_ai_config):
        """测试缓存性能"""
        with patch('app.services.ai_service.openai.ChatCompletion.acreate') as mock_openai:
            # Mock OpenAI响应
            mock_response = AsyncMock()
            mock_response.choices = [AsyncMock()]
            mock_response.choices[0].message.content = "缓存测试回复"
            mock_response.usage.total_tokens = 50
            mock_openai.return_value = mock_response
            
            # 第一次请求（应该调用API）
            start_time = time.time()
            response1 = client.post(
                "/api/v1/ai/generate-reply",
                headers=auth_headers,
                json={
                    "comment_content": "缓存测试留言"
                }
            )
            first_request_time = time.time() - start_time
            
            assert response1.status_code == 200
            assert mock_openai.call_count == 1
            
            # 第二次相同请求（应该使用缓存）
            start_time = time.time()
            response2 = client.post(
                "/api/v1/ai/generate-reply",
                headers=auth_headers,
                json={
                    "comment_content": "缓存测试留言"
                }
            )
            second_request_time = time.time() - start_time
            
            assert response2.status_code == 200
            # 第二次请求应该更快（使用缓存）
            assert second_request_time < first_request_time
            # API应该只被调用一次
            assert mock_openai.call_count == 1
    
    def test_websocket_connection_performance(self, client: TestClient, test_user_token):
        """测试WebSocket连接性能"""
        import websocket
        import threading
        import json
        
        connection_times = []
        
        def test_connection():
            start_time = time.time()
            
            ws = websocket.WebSocket()
            try:
                ws.connect(f"ws://localhost:8000/api/v1/ws?token={test_user_token}")
                connection_time = time.time() - start_time
                connection_times.append(connection_time)
                
                # 发送ping消息
                ws.send(json.dumps({"type": "ping", "timestamp": time.time()}))
                response = ws.recv()
                response_data = json.loads(response)
                assert response_data["type"] == "pong"
                
            finally:
                ws.close()
        
        # 测试10个并发连接
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=test_connection)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # 所有连接时间都应该在合理范围内
        assert len(connection_times) == 10
        assert all(t < 2.0 for t in connection_times)  # 连接时间应小于2秒
        
        # 平均连接时间应该很快
        avg_connection_time = sum(connection_times) / len(connection_times)
        assert avg_connection_time < 1.0
    
    def test_large_data_handling(self, client: TestClient, auth_headers):
        """测试大数据处理性能"""
        # 创建大量数据的请求
        large_content = "这是一个很长的留言内容。" * 100  # 约1000字符
        
        start_time = time.time()
        
        response = client.post(
            "/api/v1/ai/generate-reply",
            headers=auth_headers,
            json={
                "comment_content": large_content
            }
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # 即使是大数据，处理时间也应该在合理范围内
        assert processing_time < 10.0  # 处理时间应小于10秒
    
    def test_error_handling_performance(self, client: TestClient, auth_headers):
        """测试错误处理性能"""
        start_time = time.time()
        
        # 发送无效请求
        response = client.post(
            "/api/v1/ai/generate-reply",
            headers=auth_headers,
            json={
                "invalid_field": "invalid_value"
            }
        )
        
        end_time = time.time()
        error_handling_time = end_time - start_time
        
        assert response.status_code == 422
        assert error_handling_time < 1.0  # 错误处理时间应该很快
