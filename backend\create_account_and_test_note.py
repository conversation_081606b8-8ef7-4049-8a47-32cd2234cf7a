#!/usr/bin/env python3
"""
创建小红书账号和测试笔记用于删除测试
"""
import requests
import json

# API配置
BASE_URL = "http://localhost:8000/api/v1"

def get_auth_token():
    """获取认证token"""
    try:
        login_data = {
            'username': 'testuser2',
            'password': 'testpass123'
        }
        
        response = requests.post(f'{BASE_URL}/auth/login', data=login_data)
        if response.status_code == 200:
            data = response.json()
            return data['data']['access_token']
        
        print(f"登录失败: {response.text}")
        return None
        
    except Exception as e:
        print(f"获取token异常: {e}")
        return None

def create_xiaohongshu_account(headers):
    """创建小红书账号"""
    print("🔍 创建小红书账号...")
    
    account_data = {
        "account_name": "测试删除功能账号",
        "account_id": "test_delete_account_123",
        "login_phone": "***********",
        "login_email": "<EMAIL>"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/xiaohongshu/accounts", headers=headers, json=account_data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code in [200, 201]:
            data = response.json()
            print(f"✅ 小红书账号创建成功!")
            account_id = data.get('data', {}).get('id')
            print(f"账号ID: {account_id}")
            return account_id
        else:
            print(f"❌ 小红书账号创建失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def create_test_note(headers, account_id):
    """创建测试笔记"""
    print("\n🔍 创建测试笔记...")

    note_data = {
        "account_id": account_id,  # 使用指定的账号ID
        "note_id": "test_delete_note_999",  # 添加必需的note_id字段
        "note_url": "https://www.xiaohongshu.com/explore/test_delete_note_999",
        "title": "测试删除功能的笔记",
        "crawl_interval": 300,
        "auto_reply_enabled": True
    }
    
    try:
        response = requests.post(f"{BASE_URL}/notes", headers=headers, json=note_data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 测试笔记创建成功!")
            note_id = data.get('data', {}).get('id')
            print(f"笔记ID: {note_id}")
            return note_id
        else:
            print(f"❌ 测试笔记创建失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def get_accounts(headers):
    """获取账号列表"""
    print("\n🔍 获取账号列表...")
    
    try:
        response = requests.get(f"{BASE_URL}/xiaohongshu/accounts", headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 账号列表获取成功!")
            accounts = data.get('data', [])
            print(f"账号数量: {len(accounts)}")
            for account in accounts:
                print(f"  - 账号ID: {account.get('id')}, 名称: {account.get('account_name')}")
            return accounts
        else:
            print(f"❌ 账号列表获取失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return []

def main():
    """主函数"""
    print("🚀 创建账号和测试笔记用于删除功能测试...")
    print("=" * 50)
    
    # 获取认证token
    print("0️⃣ 获取认证token")
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证token，测试终止")
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    print(f"✅ 认证token获取成功")
    
    # 1. 获取现有账号
    print("\n" + "=" * 50)
    print("1️⃣ 获取现有账号")
    accounts = get_accounts(headers)
    
    # 2. 如果没有账号，创建一个
    if not accounts:
        print("\n" + "=" * 50)
        print("2️⃣ 创建小红书账号")
        account_id = create_xiaohongshu_account(headers)
        if not account_id:
            print("❌ 无法创建小红书账号，测试终止")
            return
    else:
        account_id = accounts[0].get('id')
        print(f"使用现有账号ID: {account_id}")
    
    # 3. 创建测试笔记
    print("\n" + "=" * 50)
    print("3️⃣ 创建测试笔记")
    note_id = create_test_note(headers, account_id)
    
    print("\n" + "=" * 50)
    print("✅ 准备工作完成!")
    if note_id:
        print(f"📊 结果:")
        print(f"   小红书账号ID: {account_id}")
        print(f"   测试笔记ID: {note_id}")
        print("现在可以在前端页面测试删除功能了！")
    else:
        print("❌ 测试笔记创建失败，但账号已创建，可以手动在前端创建笔记进行测试")

if __name__ == "__main__":
    main()
