.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  background-size: 100px 100px;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.login-container {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 400px;
  padding: 20px;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px 32px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.logo-icon {
  font-size: 48px;
  color: #1890ff;
  margin-right: 12px;
}

.logo-title {
  margin: 0 !important;
  color: #333;
  font-weight: 600;
}

.subtitle {
  font-size: 14px;
  color: #666;
}

.auth-tabs {
  margin-bottom: 24px;
}

.auth-tabs .ant-tabs-tab {
  font-size: 16px;
  font-weight: 500;
}

.auth-form {
  margin-top: 24px;
}

.auth-form .ant-form-item {
  margin-bottom: 20px;
}

.auth-form .ant-input-affix-wrapper,
.auth-form .ant-input {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
}

.auth-form .ant-input-affix-wrapper:hover,
.auth-form .ant-input:hover {
  border-color: #40a9ff;
}

.auth-form .ant-input-affix-wrapper:focus,
.auth-form .ant-input-affix-wrapper-focused,
.auth-form .ant-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.error-message {
  margin-bottom: 16px;
  padding: 8px 12px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 6px;
  text-align: center;
}

.submit-button {
  height: 44px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  transition: all 0.3s;
}

.submit-button:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
  transform: translateY(-1px);
}

.submit-button:active {
  transform: translateY(0);
}

.login-footer {
  margin-top: 24px;
  text-align: center;
}

.footer-text {
  font-size: 12px;
}

.footer-link {
  font-size: 12px;
  cursor: pointer;
  transition: color 0.3s;
}

.footer-link:hover {
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }
  
  .login-card {
    padding: 24px 20px;
  }
  
  .logo-icon {
    font-size: 36px;
  }
  
  .logo-title {
    font-size: 20px;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .login-card {
    background: rgba(20, 20, 20, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .logo-title {
    color: #fff;
  }
  
  .subtitle {
    color: #ccc;
  }
  
  .auth-form .ant-input-affix-wrapper,
  .auth-form .ant-input {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: #fff;
  }
  
  .auth-form .ant-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }
  
  .footer-text,
  .footer-link {
    color: #ccc;
  }
}
