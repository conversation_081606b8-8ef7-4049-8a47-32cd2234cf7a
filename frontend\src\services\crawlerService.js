import { request } from './api';

export const crawlerService = {
  // 启动爬虫
  startCrawler: async () => {
    const response = await request.post('/crawler/crawl');
    return response;
  },

  // 获取爬虫状态
  getCrawlerStatus: async () => {
    const response = await request.get('/crawler/status');
    return response;
  },

  // 停止爬虫
  stopCrawler: async () => {
    const response = await request.post('/crawler/stop');
    return response;
  },

  // 测试爬虫功能
  testCrawler: async (testData) => {
    const response = await request.post('/crawler/test', testData);
    return response;
  },

  // 获取爬虫日志
  getCrawlerLogs: async (params = {}) => {
    const response = await request.get('/crawler/logs', { params });
    return response;
  },

  // 启动定时任务
  startSchedule: async () => {
    const response = await request.post('/crawler/schedule/start');
    return response;
  },

  // 停止定时任务
  stopSchedule: async () => {
    const response = await request.post('/crawler/schedule/stop');
    return response;
  },

  // 获取定时任务状态
  getScheduleStatus: async () => {
    const response = await request.get('/crawler/schedule/status');
    return response;
  },

  // 手动执行抓取任务
  manualCrawl: async (noteIds = []) => {
    const response = await request.post('/crawler/manual', {
      note_ids: noteIds,
    });
    return response;
  },

  // 获取爬虫配置
  getCrawlerConfig: async () => {
    const response = await request.get('/crawler/config');
    return response;
  },

  // 更新爬虫配置
  updateCrawlerConfig: async (config) => {
    const response = await request.put('/crawler/config', config);
    return response;
  },

  // 获取爬虫统计信息
  getCrawlerStats: async () => {
    const response = await request.get('/crawler/stats');
    return response;
  },

  // 重启爬虫
  restartCrawler: async () => {
    const response = await request.post('/crawler/restart');
    return response;
  },

  // 清理爬虫缓存
  clearCrawlerCache: async () => {
    const response = await request.post('/crawler/clear-cache');
    return response;
  },
};
