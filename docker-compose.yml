version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: xiaohongshu_db
    environment:
      POSTGRES_DB: xiaohongshu_auto_reply
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD:-password123}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - xiaohongshu_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 主应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: xiaohongshu_app
    environment:
      # 数据库配置
      DATABASE_URL: postgresql://postgres:${DB_PASSWORD:-password123}@postgres:5432/xiaohongshu_auto_reply

      # Redis配置
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis123}@redis:6379/0

      # 应用配置
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-change-in-production}
      DEBUG: ${DEBUG:-false}
      ENVIRONMENT: ${ENVIRONMENT:-production}

      # OpenAI配置
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      OPENAI_API_BASE: ${OPENAI_API_BASE:-https://api.openai.com/v1}
      OPENAI_MODEL: ${OPENAI_MODEL:-gpt-3.5-turbo}

      # 安全配置
      ALLOWED_HOSTS: ${ALLOWED_HOSTS:-localhost,127.0.0.1}
      CORS_ORIGINS: ${CORS_ORIGINS:-http://localhost:3000,http://127.0.0.1:3000}

    ports:
      - "8000:8000"

    volumes:
      - app_logs:/app/logs
      - app_uploads:/app/uploads

    networks:
      - xiaohongshu_network

    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy

    restart: unless-stopped

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: xiaohongshu_redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - xiaohongshu_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: xiaohongshu_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - nginx_logs:/var/log/nginx
    networks:
      - xiaohongshu_network
    depends_on:
      - app
    restart: unless-stopped

  # 监控服务 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: xiaohongshu_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - xiaohongshu_network
    restart: unless-stopped
    profiles:
      - monitoring

  # 监控服务 - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: xiaohongshu_grafana
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin123}
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - xiaohongshu_network
    depends_on:
      - prometheus
    restart: unless-stopped
    profiles:
      - monitoring

# 网络配置
networks:
  xiaohongshu_network:
    driver: bridge

# 数据卷配置
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  app_uploads:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
