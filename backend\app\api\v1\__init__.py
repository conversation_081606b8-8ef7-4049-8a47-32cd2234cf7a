"""
API v1版本路由
"""
from fastapi import APIRouter
from .auth import router as auth_router
from .xiaohongshu import router as xiaohongshu_router
from .notes import router as notes_router
from .comments import router as comments_router
from .crawler import router as crawler_router
from .ai import router as ai_router
from .websocket import router as websocket_router
from .analytics import router as analytics_router
from .automation import router as automation_router
from .health import router as health_router

api_router = APIRouter()

# 注册认证路由
api_router.include_router(auth_router, prefix="/auth", tags=["认证"])

# 注册小红书账号管理路由
api_router.include_router(xiaohongshu_router, prefix="/xiaohongshu", tags=["小红书账号管理"])

# 注册笔记管理路由
api_router.include_router(notes_router, prefix="/notes", tags=["笔记管理"])

# 注册留言管理路由
api_router.include_router(comments_router, prefix="/comments", tags=["留言管理"])

# 注册爬虫管理路由
api_router.include_router(crawler_router, prefix="/crawler", tags=["爬虫管理"])

# 注册AI智能回复路由
api_router.include_router(ai_router, prefix="/ai", tags=["AI智能回复"])

# 注册WebSocket路由
api_router.include_router(websocket_router, prefix="", tags=["实时通信"])

# 注册数据分析路由
api_router.include_router(analytics_router, prefix="/analytics", tags=["数据分析"])

# 注册自动化路由
api_router.include_router(automation_router, prefix="/automation", tags=["自动化处理"])

# 注册健康检查路由
api_router.include_router(health_router, prefix="", tags=["系统监控"])
