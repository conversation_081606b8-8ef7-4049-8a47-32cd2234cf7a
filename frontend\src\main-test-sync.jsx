import React from 'react'
import <PERSON>actDOM from 'react-dom/client'
import App from './App-test-sync.jsx'
import './index.css'

console.log('main-test-sync.jsx 开始执行');

const rootElement = document.getElementById('root');
if (rootElement) {
  console.log('创建React根并渲染App');
  ReactDOM.createRoot(rootElement).render(
    <React.StrictMode>
      <App />
    </React.StrictMode>,
  );
  console.log('App 渲染完成');
} else {
  console.error('找不到root元素！');
}
