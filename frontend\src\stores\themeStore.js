import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 主题配置
export const themes = {
  light: {
    name: 'light',
    displayName: '浅色主题',
    colors: {
      // 主要颜色
      primary: '#1890ff',
      primaryHover: '#40a9ff',
      primaryActive: '#096dd9',
      
      // 背景颜色
      bodyBg: '#ffffff',
      componentBg: '#ffffff',
      layoutBg: '#f0f2f5',
      siderBg: '#ffffff',
      headerBg: '#ffffff',
      
      // 文字颜色
      textPrimary: 'rgba(0, 0, 0, 0.85)',
      textSecondary: 'rgba(0, 0, 0, 0.65)',
      textDisabled: 'rgba(0, 0, 0, 0.25)',
      
      // 边框颜色
      borderColor: '#d9d9d9',
      borderColorSplit: '#f0f0f0',
      
      // 状态颜色
      success: '#52c41a',
      warning: '#faad14',
      error: '#ff4d4f',
      info: '#1890ff',
      
      // 阴影
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
      cardShadow: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
      
      // 滚动条
      scrollbarTrack: '#f1f1f1',
      scrollbarThumb: '#c1c1c1',
      scrollbarThumbHover: '#a8a8a8',
    }
  },
  dark: {
    name: 'dark',
    displayName: '深色主题',
    colors: {
      // 主要颜色
      primary: '#1890ff',
      primaryHover: '#40a9ff',
      primaryActive: '#096dd9',
      
      // 背景颜色
      bodyBg: '#141414',
      componentBg: '#1f1f1f',
      layoutBg: '#000000',
      siderBg: '#001529',
      headerBg: '#1f1f1f',
      
      // 文字颜色
      textPrimary: 'rgba(255, 255, 255, 0.85)',
      textSecondary: 'rgba(255, 255, 255, 0.65)',
      textDisabled: 'rgba(255, 255, 255, 0.25)',
      
      // 边框颜色
      borderColor: '#434343',
      borderColorSplit: '#303030',
      
      // 状态颜色
      success: '#52c41a',
      warning: '#faad14',
      error: '#ff4d4f',
      info: '#1890ff',
      
      // 阴影
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.45)',
      cardShadow: '0 1px 3px rgba(0, 0, 0, 0.32), 0 1px 2px rgba(0, 0, 0, 0.44)',
      
      // 滚动条
      scrollbarTrack: '#2f2f2f',
      scrollbarThumb: '#6c6c6c',
      scrollbarThumbHover: '#8c8c8c',
    }
  }
};

// 应用主题到CSS变量
const applyTheme = (theme) => {
  const root = document.documentElement;
  const colors = theme.colors;
  
  // 设置CSS变量
  Object.entries(colors).forEach(([key, value]) => {
    root.style.setProperty(`--theme-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`, value);
  });
  
  // 设置body背景色
  document.body.style.backgroundColor = colors.bodyBg;
  document.body.style.color = colors.textPrimary;
  
  // 更新Ant Design主题
  updateAntdTheme(theme);
};

// 更新Ant Design主题
const updateAntdTheme = (theme) => {
  const isDark = theme.name === 'dark';
  
  // 动态添加或移除dark主题类
  if (isDark) {
    document.body.classList.add('dark-theme');
    document.body.classList.remove('light-theme');
  } else {
    document.body.classList.add('light-theme');
    document.body.classList.remove('dark-theme');
  }
  
  // 设置Ant Design的ConfigProvider主题
  window.antdTheme = {
    algorithm: isDark ? 'darkAlgorithm' : 'defaultAlgorithm',
    token: {
      colorPrimary: theme.colors.primary,
      colorBgBase: theme.colors.componentBg,
      colorTextBase: theme.colors.textPrimary,
      colorBorder: theme.colors.borderColor,
      colorBgContainer: theme.colors.componentBg,
      colorBgLayout: theme.colors.layoutBg,
    }
  };
  
  // 触发主题更新事件
  window.dispatchEvent(new CustomEvent('themeChange', { detail: { theme, isDark } }));
};

// 主题store
export const useThemeStore = create(
  persist(
    (set, get) => ({
      // 当前主题
      currentTheme: 'light',
      
      // 是否为深色主题
      isDark: false,
      
      // 主题配置
      themes,
      
      // 切换主题
      setTheme: (themeName) => {
        const theme = themes[themeName];
        if (!theme) {
          console.warn(`Theme "${themeName}" not found`);
          return;
        }
        
        const isDark = themeName === 'dark';
        
        set({
          currentTheme: themeName,
          isDark
        });
        
        // 应用主题
        applyTheme(theme);
      },
      
      // 切换深色/浅色主题
      toggleTheme: () => {
        const { currentTheme } = get();
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        get().setTheme(newTheme);
      },
      
      // 获取当前主题配置
      getCurrentTheme: () => {
        const { currentTheme } = get();
        return themes[currentTheme];
      },
      
      // 获取主题颜色
      getThemeColor: (colorKey) => {
        const theme = get().getCurrentTheme();
        return theme.colors[colorKey];
      },
      
      // 初始化主题
      initTheme: () => {
        const { currentTheme } = get();
        const theme = themes[currentTheme];
        if (theme) {
          applyTheme(theme);
        }
      },
      
      // 检测系统主题偏好
      detectSystemTheme: () => {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
          return 'dark';
        }
        return 'light';
      },
      
      // 跟随系统主题
      followSystemTheme: () => {
        const systemTheme = get().detectSystemTheme();
        get().setTheme(systemTheme);
        
        // 监听系统主题变化
        if (window.matchMedia) {
          const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
          const handleChange = (e) => {
            const newTheme = e.matches ? 'dark' : 'light';
            get().setTheme(newTheme);
          };
          
          mediaQuery.addEventListener('change', handleChange);
          
          // 返回清理函数
          return () => {
            mediaQuery.removeEventListener('change', handleChange);
          };
        }
      }
    }),
    {
      name: 'theme-storage',
      partialize: (state) => ({
        currentTheme: state.currentTheme,
        isDark: state.isDark,
      }),
    }
  )
);

// 主题工具函数
export const themeUtils = {
  // 获取CSS变量值
  getCSSVariable: (name) => {
    return getComputedStyle(document.documentElement).getPropertyValue(`--theme-${name}`);
  },
  
  // 设置CSS变量值
  setCSSVariable: (name, value) => {
    document.documentElement.style.setProperty(`--theme-${name}`, value);
  },
  
  // 生成主题样式对象
  getThemeStyles: (theme) => {
    const colors = theme.colors;
    return {
      backgroundColor: colors.componentBg,
      color: colors.textPrimary,
      borderColor: colors.borderColor,
    };
  },
  
  // 生成卡片样式
  getCardStyles: (theme) => {
    const colors = theme.colors;
    return {
      backgroundColor: colors.componentBg,
      color: colors.textPrimary,
      borderColor: colors.borderColor,
      boxShadow: colors.cardShadow,
    };
  }
};
