# 🤖 AI配置页面使用指南

## 📋 概述

前端AI配置页面已成功集成后端AI功能，提供完整的AI回复系统管理界面。

## 🚀 快速开始

### 1. 启动服务

```bash
# 启动后端服务
cd backend
python -c "from app.main import app; import uvicorn; uvicorn.run(app, host='0.0.0.0', port=8000)"

# 启动前端服务
cd frontend
npm run dev
```

### 2. 访问页面

- 前端地址: http://localhost:3000
- 后端地址: http://localhost:8000

### 3. 登录系统

使用测试账户登录：
- 用户名: `testuser2`
- 密码: `testpass123`

## 🎛️ 功能模块

### 1. AI配置标签页

#### Ollama配置 (推荐)
- **服务地址**: 配置Ollama服务地址 (默认: http://************:11434)
- **连接测试**: 点击"测试连接"验证Ollama服务状态
- **模型管理**: 点击"获取模型列表"查看可用AI模型
- **模型选择**: 从下拉列表中选择要使用的AI模型

#### OpenAI配置
- **API密钥**: 输入OpenAI API密钥
- **模型选择**: 选择GPT模型 (gpt-3.5-turbo, gpt-4等)

#### 回复参数
- **温度参数**: 控制回复的创造性 (0.0-2.0)
- **最大Token数**: 限制回复长度 (1-4000)
- **回复语调**: 选择回复风格 (友好/专业/随意/热情)
- **回复语言**: 选择回复语言 (中文/英文)
- **包含表情**: 是否在回复中包含表情符号
- **内容过滤**: 是否启用敏感内容过滤

### 2. 使用统计标签页

#### 今日统计
- 请求数量
- 成功率
- Token使用量
- 成本统计

#### 本月统计
- 总请求数
- 平均成功率
- 累计Token使用
- 累计成本

#### 使用限制
- 每日请求限制
- 已使用量
- 使用进度条

#### 模型性能对比
- 不同AI模型的性能对比
- 成功率统计
- 请求数量统计

### 3. 回复历史标签页

#### 历史记录查看
- 按时间倒序显示AI回复历史
- 显示原评论、AI回复、使用模型、Token消耗
- 支持分页浏览

#### 筛选功能
- 按时间筛选 (今天/本周/本月)
- 按模型筛选 (Ollama/OpenAI)

### 4. 功能测试标签页

#### AI回复测试
- 输入测试评论内容
- 一键生成AI回复
- 查看生成结果和详细信息
- 显示使用的模型、Token消耗、置信度等

## 🔧 操作流程

### 首次配置

1. **选择AI提供商**
   - 推荐选择Ollama (本地部署，免费)
   - 或选择OpenAI (云端服务，需要API密钥)

2. **配置Ollama** (推荐)
   ```
   1. 确保Ollama服务正在运行
   2. 输入Ollama服务地址
   3. 点击"测试连接"验证连接
   4. 点击"获取模型列表"
   5. 选择要使用的AI模型
   6. 调整回复参数
   7. 点击"保存配置"
   ```

3. **配置OpenAI**
   ```
   1. 输入OpenAI API密钥
   2. 选择GPT模型
   3. 调整回复参数
   4. 点击"保存配置"
   ```

### 日常使用

1. **查看统计数据**
   - 切换到"使用统计"标签页
   - 点击"刷新统计数据"查看最新数据

2. **查看历史记录**
   - 切换到"回复历史"标签页
   - 点击"刷新历史记录"查看最新记录

3. **测试AI功能**
   - 切换到"功能测试"标签页
   - 输入测试评论
   - 点击"生成AI回复"测试功能

## 🎯 最佳实践

### Ollama配置建议
- 使用本地IP地址而非localhost (如: ************:11434)
- 选择适合的模型大小 (推荐qwen3:30b-a3b)
- 温度参数设置为0.7-1.0之间

### 回复参数优化
- **友好语调**: 适合日常互动回复
- **专业语调**: 适合产品咨询回复
- **温度0.7**: 平衡创造性和准确性
- **Token限制150**: 适合小红书评论长度

### 性能监控
- 定期查看使用统计
- 监控成功率和成本
- 根据历史记录优化配置

## 🔍 故障排除

### 常见问题

1. **Ollama连接失败**
   - 检查Ollama服务是否启动
   - 确认服务地址是否正确
   - 检查网络连接

2. **模型列表为空**
   - 确保Ollama连接成功
   - 检查是否已下载AI模型
   - 重新获取模型列表

3. **AI回复生成失败**
   - 检查AI配置是否正确
   - 确认模型是否可用
   - 查看错误信息

4. **统计数据不显示**
   - 确保已有AI回复记录
   - 点击刷新按钮重新加载
   - 检查后端API连接

## 📞 技术支持

如遇到问题，请检查：
1. 后端服务是否正常运行 (http://localhost:8000)
2. 前端服务是否正常运行 (http://localhost:3000)
3. Ollama服务是否正常运行
4. 网络连接是否正常

## 🎉 功能特色

- ✅ **完整的AI配置管理** - 支持Ollama和OpenAI
- ✅ **实时连接测试** - 验证AI服务可用性
- ✅ **模型管理** - 动态获取和选择AI模型
- ✅ **使用统计** - 详细的数据分析和监控
- ✅ **回复历史** - 完整的AI回复记录
- ✅ **功能测试** - 便捷的AI功能验证
- ✅ **现代化界面** - 美观易用的管理界面
- ✅ **实时反馈** - 操作结果即时显示

现在您可以享受完整的AI回复系统管理体验！🚀
