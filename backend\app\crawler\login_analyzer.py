"""
小红书登录机制分析工具
研究小红书的登录流程、<PERSON>ie管理和身份验证机制
"""
import asyncio
import json
import re
from typing import Dict, List, Optional
from playwright.async_api import async_playwright, <PERSON>, BrowserContext
from loguru import logger
import time


class LoginAnalyzer:
    """登录机制分析器"""
    
    def __init__(self):
        self.base_url = "https://www.xiaohongshu.com"
        self.login_url = "https://www.xiaohongshu.com/explore"
        self.login_requests = []
        self.cookies_info = {}
        
    async def analyze_login_flow(self) -> Dict:
        """分析登录流程"""
        logger.info("开始分析小红书登录流程...")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            context = await browser.new_context()
            page = await context.new_page()
            
            # 监听网络请求
            page.on("response", self._handle_login_response)
            
            try:
                # 访问主页
                await page.goto(self.login_url, wait_until='networkidle')
                await page.wait_for_timeout(3000)
                
                # 查找登录按钮
                login_selectors = [
                    'text="登录"',
                    '[data-testid="login"]',
                    '.login-btn',
                    '[class*="login"]',
                    'button:has-text("登录")'
                ]
                
                login_button = None
                for selector in login_selectors:
                    try:
                        login_button = await page.wait_for_selector(selector, timeout=2000)
                        if login_button:
                            logger.info(f"找到登录按钮: {selector}")
                            break
                    except:
                        continue
                
                if login_button:
                    # 点击登录按钮
                    await login_button.click()
                    await page.wait_for_timeout(2000)
                    
                    # 分析登录弹窗或页面
                    await self._analyze_login_modal(page)
                else:
                    logger.warning("未找到登录按钮，可能已经登录或页面结构变化")
                
                # 等待更多网络请求
                await page.wait_for_timeout(5000)
                
                # 获取当前cookies
                cookies = await context.cookies()
                self.cookies_info = {
                    'cookies': cookies,
                    'cookie_count': len(cookies),
                    'important_cookies': self._analyze_cookies(cookies)
                }
                
            except Exception as e:
                logger.error(f"分析登录流程时出错: {e}")
            finally:
                await browser.close()
                
        return self._generate_login_report()
        
    async def _handle_login_response(self, response) -> None:
        """处理登录相关的网络响应"""
        url = response.url
        method = response.request.method
        
        # 记录登录相关的请求
        login_keywords = ['login', 'auth', 'signin', 'passport', 'sso', 'oauth']
        if any(keyword in url.lower() for keyword in login_keywords):
            request_info = {
                'timestamp': time.time(),
                'method': method,
                'url': url,
                'status': response.status,
                'headers': dict(response.headers),
                'request_headers': dict(response.request.headers)
            }
            
            # 尝试获取响应内容
            try:
                if 'application/json' in response.headers.get('content-type', ''):
                    content = await response.json()
                    request_info['response'] = content
            except:
                pass
                
            self.login_requests.append(request_info)
            logger.info(f"发现登录相关请求: {url}")
            
    async def _analyze_login_modal(self, page: Page) -> None:
        """分析登录弹窗"""
        logger.info("分析登录弹窗...")
        
        # 查找登录方式
        login_methods = {
            'phone': ['手机号', 'phone', '手机登录'],
            'qr': ['二维码', 'qr', 'QR', '扫码'],
            'wechat': ['微信', 'wechat', 'WeChat'],
            'email': ['邮箱', 'email', '邮件']
        }
        
        found_methods = []
        for method, keywords in login_methods.items():
            for keyword in keywords:
                try:
                    element = await page.wait_for_selector(f'text="{keyword}"', timeout=1000)
                    if element:
                        found_methods.append(method)
                        logger.info(f"发现登录方式: {method}")
                        break
                except:
                    continue
                    
        # 查找输入框
        input_selectors = [
            'input[type="text"]',
            'input[type="tel"]',
            'input[type="email"]',
            'input[placeholder*="手机"]',
            'input[placeholder*="邮箱"]'
        ]
        
        for selector in input_selectors:
            try:
                inputs = await page.query_selector_all(selector)
                if inputs:
                    logger.info(f"找到输入框: {selector}, 数量: {len(inputs)}")
            except:
                continue
                
        # 查找验证码相关元素
        captcha_selectors = [
            '[class*="captcha"]',
            '[class*="verify"]',
            'canvas',
            'img[src*="captcha"]'
        ]
        
        for selector in captcha_selectors:
            try:
                elements = await page.query_selector_all(selector)
                if elements:
                    logger.info(f"发现验证码元素: {selector}")
            except:
                continue
                
    def _analyze_cookies(self, cookies: List[Dict]) -> Dict:
        """分析重要的cookies"""
        important_cookies = {}
        
        for cookie in cookies:
            name = cookie['name']
            value = cookie['value']
            
            # 识别重要的cookie
            if any(keyword in name.lower() for keyword in ['session', 'token', 'auth', 'login', 'user']):
                important_cookies[name] = {
                    'value_length': len(value),
                    'domain': cookie.get('domain', ''),
                    'path': cookie.get('path', ''),
                    'secure': cookie.get('secure', False),
                    'httpOnly': cookie.get('httpOnly', False)
                }
                
        return important_cookies
        
    def _generate_login_report(self) -> Dict:
        """生成登录分析报告"""
        report = {
            'login_requests': self.login_requests,
            'cookies_info': self.cookies_info,
            'analysis_summary': {
                'login_request_count': len(self.login_requests),
                'cookie_count': self.cookies_info.get('cookie_count', 0),
                'important_cookies_count': len(self.cookies_info.get('important_cookies', {}))
            },
            'security_features': self._analyze_security_features(),
            'recommendations': self._generate_recommendations()
        }
        
        return report
        
    def _analyze_security_features(self) -> Dict:
        """分析安全特性"""
        features = {
            'has_captcha': False,
            'has_sms_verification': False,
            'has_rate_limiting': False,
            'uses_https': True,
            'has_csrf_protection': False
        }
        
        # 基于请求分析安全特性
        for req in self.login_requests:
            url = req['url']
            headers = req.get('request_headers', {})
            
            if 'captcha' in url.lower():
                features['has_captcha'] = True
            if 'sms' in url.lower() or 'verify' in url.lower():
                features['has_sms_verification'] = True
            if 'x-csrf-token' in headers or 'csrf' in str(headers).lower():
                features['has_csrf_protection'] = True
                
        return features
        
    def _generate_recommendations(self) -> List[str]:
        """生成建议"""
        recommendations = [
            "使用低频率的登录尝试，避免触发反爬虫机制",
            "保存和管理cookies，避免频繁登录",
            "模拟真实用户行为，包括鼠标移动和随机延时",
            "使用代理IP轮换，分散请求来源",
            "监控登录状态，及时处理登录失效"
        ]
        
        return recommendations
        
    def save_login_report(self, report: Dict, filename: str = "login_analysis.json") -> None:
        """保存登录分析报告"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        logger.info(f"登录分析报告已保存到: {filename}")


async def main():
    """主函数"""
    analyzer = LoginAnalyzer()
    
    logger.info("开始小红书登录机制分析...")
    report = await analyzer.analyze_login_flow()
    
    logger.info("分析完成，生成报告...")
    analyzer.save_login_report(report)
    
    # 打印关键发现
    summary = report['analysis_summary']
    logger.info(f"登录相关请求数: {summary['login_request_count']}")
    logger.info(f"Cookie数量: {summary['cookie_count']}")
    logger.info(f"重要Cookie数量: {summary['important_cookies_count']}")
    
    security = report['security_features']
    logger.info("安全特性:")
    for feature, enabled in security.items():
        logger.info(f"  {feature}: {enabled}")


if __name__ == "__main__":
    asyncio.run(main())
