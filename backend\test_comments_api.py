#!/usr/bin/env python3
"""
测试留言管理API
"""
import requests
import json

# API配置
BASE_URL = "http://localhost:8000/api/v1"

def get_auth_token():
    """获取认证token"""
    try:
        # 创建测试用户
        register_data = {
            'username': 'testuser2',
            'password': 'testpass123',
            'email': '<EMAIL>',
            'full_name': 'Test User 2'
        }
        
        # 尝试注册
        response = requests.post(f'{BASE_URL}/auth/register', json=register_data)
        if response.status_code == 200:
            data = response.json()
            return data['data']['access_token']
        
        # 如果注册失败，尝试登录
        login_data = {
            'username': 'testuser2',
            'password': 'testpass123'
        }
        
        response = requests.post(f'{BASE_URL}/auth/login', data=login_data)
        if response.status_code == 200:
            data = response.json()
            return data['data']['access_token']
        
        print(f"认证失败: {response.text}")
        return None
        
    except Exception as e:
        print(f"获取token异常: {e}")
        return None

def test_get_comments(headers):
    """测试获取留言列表"""
    print("🔍 测试获取留言列表...")
    
    try:
        response = requests.get(f"{BASE_URL}/comments", headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 留言列表获取成功!")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return data.get('data', [])
        else:
            print(f"❌ 留言列表获取失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return []

def test_get_comments_stats(headers):
    """测试获取留言统计"""
    print("\n🔍 测试获取留言统计...")
    
    try:
        response = requests.get(f"{BASE_URL}/comments/stats", headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 留言统计获取成功!")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 留言统计获取失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_get_pending_comments(headers):
    """测试获取待处理留言"""
    print("\n🔍 测试获取待处理留言...")
    
    try:
        response = requests.get(f"{BASE_URL}/comments/pending", headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 待处理留言获取成功!")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return data.get('data', [])
        else:
            print(f"❌ 待处理留言获取失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return []

def test_reply_comment(headers, comment_id):
    """测试回复留言"""
    if not comment_id:
        print("\n⚠️ 跳过回复测试 - 没有有效的留言ID")
        return False
        
    print(f"\n🔍 测试回复留言 (ID: {comment_id})...")
    
    reply_data = {
        "reply_content": "感谢您的留言，我们会认真考虑您的建议！"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/comments/{comment_id}/reply", headers=headers, json=reply_data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 留言回复成功!")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 留言回复失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_batch_reply(headers, comment_ids):
    """测试批量回复"""
    if not comment_ids:
        print("\n⚠️ 跳过批量回复测试 - 没有有效的留言ID")
        return False
        
    print(f"\n🔍 测试批量回复留言...")
    
    batch_data = {
        "comment_ids": comment_ids[:2],  # 只测试前两个
        "reply_content": "感谢大家的关注和支持！"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/comments/batch-reply", headers=headers, json=batch_data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 批量回复成功!")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 批量回复失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_batch_ignore(headers, comment_ids):
    """测试批量忽略"""
    if not comment_ids:
        print("\n⚠️ 跳过批量忽略测试 - 没有有效的留言ID")
        return False
        
    print(f"\n🔍 测试批量忽略留言...")
    
    batch_data = {
        "comment_ids": comment_ids[-2:]  # 测试最后两个
    }
    
    try:
        response = requests.post(f"{BASE_URL}/comments/batch-ignore", headers=headers, json=batch_data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 批量忽略成功!")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 批量忽略失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始留言管理API测试...")
    print("=" * 50)
    
    # 获取认证token
    print("0️⃣ 获取认证token")
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证token，测试终止")
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    print(f"✅ 认证token获取成功")
    
    # 1. 测试获取留言列表
    print("\n" + "=" * 50)
    print("1️⃣ 获取留言列表")
    comments = test_get_comments(headers)
    comment_ids = [comment.get('id') for comment in comments if comment.get('id')]
    
    # 2. 测试获取留言统计
    print("\n" + "=" * 50)
    print("2️⃣ 获取留言统计")
    stats_success = test_get_comments_stats(headers)
    
    # 3. 测试获取待处理留言
    print("\n" + "=" * 50)
    print("3️⃣ 获取待处理留言")
    pending_comments = test_get_pending_comments(headers)
    
    # 4. 测试回复留言
    print("\n" + "=" * 50)
    print("4️⃣ 回复留言")
    reply_success = test_reply_comment(headers, comment_ids[0] if comment_ids else None)
    
    # 5. 测试批量回复
    print("\n" + "=" * 50)
    print("5️⃣ 批量回复")
    batch_reply_success = test_batch_reply(headers, comment_ids)
    
    # 6. 测试批量忽略
    print("\n" + "=" * 50)
    print("6️⃣ 批量忽略")
    batch_ignore_success = test_batch_ignore(headers, comment_ids)
    
    # 7. 最终验证
    print("\n" + "=" * 50)
    print("7️⃣ 最终验证")
    final_comments = test_get_comments(headers)
    
    print("\n" + "=" * 50)
    print("✅ 留言管理API测试完成!")
    print(f"📊 测试结果总结:")
    print(f"   初始留言数: {len(comments)}")
    print(f"   最终留言数: {len(final_comments)}")
    print(f"   统计操作: {'✅ 成功' if stats_success else '❌ 失败'}")
    print(f"   回复操作: {'✅ 成功' if reply_success else '❌ 失败'}")
    print(f"   批量回复: {'✅ 成功' if batch_reply_success else '❌ 失败'}")
    print(f"   批量忽略: {'✅ 成功' if batch_ignore_success else '❌ 失败'}")

if __name__ == "__main__":
    main()
