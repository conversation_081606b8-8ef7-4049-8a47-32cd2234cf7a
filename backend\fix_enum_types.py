#!/usr/bin/env python3
"""
修复数据库枚举类型
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.database import engine
from sqlalchemy import text

def fix_enum_types():
    """修复数据库枚举类型"""
    try:
        with engine.connect() as conn:
            # 删除现有的枚举类型
            print("删除现有的枚举类型...")
            
            # 先删除使用枚举的表
            conn.execute(text("DROP TABLE IF EXISTS crawler_task_logs CASCADE"))
            conn.execute(text("DROP TABLE IF EXISTS crawler_tasks CASCADE"))
            conn.execute(text("DROP TABLE IF EXISTS crawler_configs CASCADE"))
            
            # 删除枚举类型
            conn.execute(text("DROP TYPE IF EXISTS taskstatus CASCADE"))
            conn.execute(text("DROP TYPE IF EXISTS tasktype CASCADE"))
            
            # 重新创建枚举类型（使用大写值）
            print("重新创建枚举类型...")
            conn.execute(text("""
                CREATE TYPE taskstatus AS ENUM (
                    'PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', 'PAUSED'
                )
            """))
            
            conn.execute(text("""
                CREATE TYPE tasktype AS ENUM (
                    'MANUAL', 'SCHEDULED', 'AUTO'
                )
            """))
            
            conn.commit()
            print("✅ 枚举类型修复完成")
            
    except Exception as e:
        print(f"❌ 修复枚举类型失败: {e}")

if __name__ == "__main__":
    fix_enum_types()
