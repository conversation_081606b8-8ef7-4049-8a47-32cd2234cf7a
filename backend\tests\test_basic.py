"""
基础功能测试
"""
import pytest
from fastapi.testclient import TestClient
from fastapi import FastAPI

# 创建一个简单的测试应用
app = FastAPI()

@app.get("/")
def read_root():
    return {"message": "Hello World"}

@app.get("/health")
def health_check():
    return {"status": "healthy"}

client = TestClient(app)

def test_read_root():
    """测试根路径"""
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"message": "Hello World"}

def test_health_check():
    """测试健康检查"""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}

def test_not_found():
    """测试404错误"""
    response = client.get("/nonexistent")
    assert response.status_code == 404

# 测试基础Python功能
def test_basic_python():
    """测试基础Python功能"""
    assert 1 + 1 == 2
    assert "hello".upper() == "HELLO"
    assert [1, 2, 3][1] == 2

# 测试字符串处理
def test_string_processing():
    """测试字符串处理"""
    text = "这是一个测试留言"
    assert len(text) > 0
    assert "测试" in text
    assert text.startswith("这是")

# 测试列表操作
def test_list_operations():
    """测试列表操作"""
    items = [1, 2, 3, 4, 5]
    assert len(items) == 5
    assert max(items) == 5
    assert min(items) == 1
    assert sum(items) == 15

# 测试字典操作
def test_dict_operations():
    """测试字典操作"""
    data = {
        "name": "测试用户",
        "email": "<EMAIL>",
        "active": True
    }
    assert data["name"] == "测试用户"
    assert data.get("email") == "<EMAIL>"
    assert data.get("nonexistent", "default") == "default"

# 测试异常处理
def test_exception_handling():
    """测试异常处理"""
    with pytest.raises(ZeroDivisionError):
        1 / 0
    
    with pytest.raises(KeyError):
        data = {"key": "value"}
        _ = data["nonexistent"]

# 测试JSON处理
def test_json_processing():
    """测试JSON处理"""
    import json
    
    data = {"message": "Hello", "count": 42}
    json_str = json.dumps(data)
    parsed_data = json.loads(json_str)
    
    assert parsed_data["message"] == "Hello"
    assert parsed_data["count"] == 42

# 测试日期时间
def test_datetime():
    """测试日期时间"""
    from datetime import datetime, timedelta
    
    now = datetime.now()
    future = now + timedelta(days=1)
    
    assert future > now
    assert (future - now).days == 1

# 测试正则表达式
def test_regex():
    """测试正则表达式"""
    import re
    
    text = "用户123在笔记中留言"
    pattern = r"用户(\d+)"
    match = re.search(pattern, text)
    
    assert match is not None
    assert match.group(1) == "123"

# 测试HTTP状态码
def test_http_status_codes():
    """测试HTTP状态码"""
    # 测试成功响应
    response = client.get("/")
    assert 200 <= response.status_code < 300
    
    # 测试404响应
    response = client.get("/nonexistent")
    assert response.status_code == 404

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
