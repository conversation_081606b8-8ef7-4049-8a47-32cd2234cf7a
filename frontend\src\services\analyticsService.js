import { request } from './api';

export const analyticsService = {
  // 获取分析概览
  getOverview: async (days = 30) => {
    const response = await request.get('/analytics/overview', {
      params: { days }
    });
    return response;
  },

  // 获取留言分析
  getCommentAnalytics: async (days = 30) => {
    const response = await request.get('/analytics/comments', {
      params: { days }
    });
    return response;
  },

  // 获取AI分析
  getAIAnalytics: async (days = 30) => {
    const response = await request.get('/analytics/ai', {
      params: { days }
    });
    return response;
  },

  // 获取模板分析
  getTemplateAnalytics: async () => {
    const response = await request.get('/analytics/templates');
    return response;
  },

  // 获取账号分析
  getAccountAnalytics: async (days = 30) => {
    const response = await request.get('/analytics/accounts', {
      params: { days }
    });
    return response;
  },

  // 获取性能指标
  getPerformanceMetrics: async (days = 30) => {
    const response = await request.get('/analytics/performance', {
      params: { days }
    });
    return response;
  },

  // 生成报告
  generateReport: async (days = 30, format = 'json') => {
    const response = await request.get('/analytics/report', {
      params: { days, format }
    });
    return response;
  },

  // 获取趋势数据
  getTrends: async (days = 30, granularity = 'daily') => {
    const response = await request.get('/analytics/trends/comments', {
      params: { days, granularity }
    });
    return response;
  },

  // 获取对比数据
  getComparison: async (currentDays = 30, previousDays = 30) => {
    const response = await request.get('/analytics/comparison', {
      params: { current_days: currentDays, previous_days: previousDays }
    });
    return response;
  },

  // 导出数据
  exportData: async (days = 30, dataType = 'all', format = 'json') => {
    const response = await request.get('/analytics/export', {
      params: { days, data_type: dataType, format }
    });
    return response;
  },
};

export const automationService = {
  // 处理单个留言
  processComment: async (commentId) => {
    const response = await request.post('/automation/process-comment', {
      comment_id: commentId
    });
    return response;
  },

  // 批量处理留言
  batchProcess: async (commentIds) => {
    const response = await request.post('/automation/batch-process', {
      comment_ids: commentIds
    });
    return response;
  },

  // 测试规则
  testRule: async (ruleId, testContent) => {
    const response = await request.post('/automation/test-rule', {
      rule_id: ruleId,
      test_content: testContent
    });
    return response;
  },

  // 获取规则统计
  getRuleStats: async (ruleId, days = 30) => {
    const response = await request.get(`/automation/rule-stats/${ruleId}`, {
      params: { days }
    });
    return response;
  },

  // 自动处理新留言
  autoProcessNewComments: async (limit = 50) => {
    const response = await request.post('/automation/auto-process-new-comments', {
      limit
    });
    return response;
  },

  // 获取处理状态
  getProcessingStatus: async () => {
    const response = await request.get('/automation/processing-status');
    return response;
  },
};

export const websocketService = {
  // 获取WebSocket统计
  getStats: async () => {
    const response = await request.get('/ws/stats');
    return response;
  },

  // 广播消息
  broadcast: async (message, priority = 'normal', userIds = null) => {
    const response = await request.post('/ws/broadcast', {
      message,
      priority,
      user_ids: userIds
    });
    return response;
  },

  // 发送通知
  sendNotification: async (userId, type, title, message, data = null, priority = 'normal') => {
    const response = await request.post(`/ws/notify/${userId}`, {
      notification_type: type,
      title,
      message,
      data,
      priority
    });
    return response;
  },
};
