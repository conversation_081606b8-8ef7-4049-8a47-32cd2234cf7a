import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { useAuthStore } from './stores/authStore';

console.log('App-test-sync.jsx 开始加载');

// 同步导入所有组件（和原始App.jsx一样）
console.log('开始同步导入组件...');
import MainLayout from './components/Layout/MainLayout';
console.log('MainLayout 导入完成');
import ProtectedRoute from './components/Auth/ProtectedRoute';
console.log('ProtectedRoute 导入完成');
import LoginPage from './pages/Auth/LoginPage';
console.log('LoginPage 导入完成');
import DashboardPage from './pages/Dashboard/DashboardPage';
console.log('DashboardPage 导入完成');

// 样式导入
import './App.css';
console.log('App.css 导入完成');

function App() {
  console.log('App 组件开始渲染');
  const { initAuth } = useAuthStore();

  useEffect(() => {
    console.log('App useEffect: 开始初始化认证');
    initAuth();
    console.log('App useEffect: 认证初始化完成');
  }, [initAuth]);

  console.log('App 组件返回JSX');
  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 6,
        },
      }}
    >
      <AntdApp>
        <Router>
          <div className="app">
            <Routes>
              {/* 登录页面 */}
              <Route path="/login" element={<LoginPage />} />

              {/* 受保护的路由 */}
              <Route path="/*" element={
                <ProtectedRoute>
                  <MainLayout>
                    <Routes>
                      {/* 仪表盘 */}
                      <Route path="/" element={<DashboardPage />} />
                      
                      {/* 简单测试路由 */}
                      <Route path="/test" element={<div>测试页面</div>} />

                      {/* 404页面 */}
                      <Route path="*" element={<Navigate to="/" replace />} />
                    </Routes>
                  </MainLayout>
                </ProtectedRoute>
              } />
            </Routes>
          </div>
        </Router>
      </AntdApp>
    </ConfigProvider>
  );
}

console.log('App-test-sync.jsx 加载完成');

export default App;
