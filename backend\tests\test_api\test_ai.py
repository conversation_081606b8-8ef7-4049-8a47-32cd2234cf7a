"""
AI功能API测试
"""
import pytest
from unittest.mock import patch, AsyncMock
from fastapi.testclient import TestClient


class TestAI:
    """AI功能测试"""
    
    @patch('app.services.ai_service.openai.ChatCompletion.acreate')
    def test_generate_reply_success(self, mock_openai, client: TestClient, auth_headers, test_ai_config):
        """测试AI回复生成成功"""
        # Mock OpenAI响应
        mock_response = AsyncMock()
        mock_response.choices = [AsyncMock()]
        mock_response.choices[0].message.content = "谢谢你的关注！"
        mock_response.usage.total_tokens = 50
        mock_openai.return_value = mock_response
        
        response = client.post(
            "/api/v1/ai/generate-reply",
            headers=auth_headers,
            json={
                "comment_content": "你的笔记很棒！",
                "context": {"note_title": "测试笔记"},
                "template_id": None
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "reply" in data["data"]
        assert data["data"]["reply"] == "谢谢你的关注！"
    
    def test_generate_reply_no_config(self, client: TestClient, auth_headers):
        """测试没有AI配置时生成回复"""
        response = client.post(
            "/api/v1/ai/generate-reply",
            headers=auth_headers,
            json={
                "comment_content": "你的笔记很棒！"
            }
        )
        
        # 应该自动创建默认配置
        assert response.status_code == 200
    
    def test_generate_suggestions_success(self, client: TestClient, auth_headers, test_template):
        """测试获取回复建议成功"""
        with patch('app.services.ai_service.AIService.get_reply_suggestions') as mock_suggestions:
            mock_suggestions.return_value = [
                {
                    "template_id": test_template.id,
                    "template_name": test_template.name,
                    "reply": "测试回复",
                    "confidence": 85
                }
            ]
            
            response = client.post(
                "/api/v1/ai/generate-suggestions",
                headers=auth_headers,
                json={
                    "comment_content": "你的笔记很棒！"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "suggestions" in data["data"]
            assert len(data["data"]["suggestions"]) > 0
    
    def test_create_template_success(self, client: TestClient, auth_headers):
        """测试创建模板成功"""
        response = client.post(
            "/api/v1/ai/templates",
            headers=auth_headers,
            json={
                "name": "新模板",
                "category": "感谢类",
                "content": "谢谢你的支持！{emoji}",
                "tags": ["感谢", "支持"],
                "is_active": True
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["name"] == "新模板"
        assert data["data"]["category"] == "感谢类"
    
    def test_get_templates_success(self, client: TestClient, auth_headers, test_template):
        """测试获取模板列表成功"""
        response = client.get(
            "/api/v1/ai/templates",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "templates" in data["data"]
        assert data["data"]["total"] >= 1
    
    def test_get_template_by_id_success(self, client: TestClient, auth_headers, test_template):
        """测试根据ID获取模板成功"""
        response = client.get(
            f"/api/v1/ai/templates/{test_template.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["id"] == test_template.id
        assert data["data"]["name"] == test_template.name
    
    def test_get_template_not_found(self, client: TestClient, auth_headers):
        """测试获取不存在的模板"""
        response = client.get(
            "/api/v1/ai/templates/99999",
            headers=auth_headers
        )
        
        assert response.status_code == 404
    
    def test_update_template_success(self, client: TestClient, auth_headers, test_template):
        """测试更新模板成功"""
        response = client.put(
            f"/api/v1/ai/templates/{test_template.id}",
            headers=auth_headers,
            json={
                "name": "更新后的模板",
                "content": "更新后的内容"
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["name"] == "更新后的模板"
    
    def test_delete_template_success(self, client: TestClient, auth_headers, test_template):
        """测试删除模板成功"""
        response = client.delete(
            f"/api/v1/ai/templates/{test_template.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    def test_get_ai_config_success(self, client: TestClient, auth_headers, test_ai_config):
        """测试获取AI配置成功"""
        response = client.get(
            "/api/v1/ai/config",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "default_model" in data["data"]
        assert "temperature" in data["data"]
    
    def test_update_ai_config_success(self, client: TestClient, auth_headers, test_ai_config):
        """测试更新AI配置成功"""
        response = client.put(
            "/api/v1/ai/config",
            headers=auth_headers,
            json={
                "default_model": "gpt-4",
                "temperature": "0.8",
                "max_tokens": 200
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["default_model"] == "gpt-4"
    
    def test_get_ai_replies_success(self, client: TestClient, auth_headers):
        """测试获取AI回复历史成功"""
        response = client.get(
            "/api/v1/ai/replies",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "replies" in data["data"]
        assert "total" in data["data"]
    
    def test_batch_generate_success(self, client: TestClient, auth_headers):
        """测试批量生成回复成功"""
        with patch('app.services.ai_service.AIService.batch_generate_replies') as mock_batch:
            mock_batch.return_value = [
                {
                    "comment_id": 1,
                    "result": {
                        "success": True,
                        "reply": "测试回复1"
                    }
                },
                {
                    "comment_id": 2,
                    "result": {
                        "success": True,
                        "reply": "测试回复2"
                    }
                }
            ]
            
            response = client.post(
                "/api/v1/ai/batch-generate",
                headers=auth_headers,
                json={
                    "comments": [
                        {"id": 1, "content": "留言1"},
                        {"id": 2, "content": "留言2"}
                    ]
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "results" in data["data"]
    
    def test_unauthorized_access(self, client: TestClient):
        """测试未授权访问AI功能"""
        response = client.get("/api/v1/ai/templates")
        assert response.status_code == 401
        
        response = client.post(
            "/api/v1/ai/generate-reply",
            json={"comment_content": "测试"}
        )
        assert response.status_code == 401
    
    def test_invalid_template_data(self, client: TestClient, auth_headers):
        """测试无效的模板数据"""
        response = client.post(
            "/api/v1/ai/templates",
            headers=auth_headers,
            json={
                "name": "",  # 空名称
                "category": "测试类",
                "content": "测试内容"
            }
        )
        
        assert response.status_code == 422
    
    def test_template_access_control(self, client: TestClient, auth_headers, test_factory, db_session):
        """测试模板访问控制"""
        # 创建另一个用户的模板
        other_user = test_factory.create_user(db_session, username="otheruser", email="<EMAIL>")
        other_template = ReplyTemplate(
            user_id=other_user.id,
            name="其他用户模板",
            category="测试类",
            content="其他用户的内容",
            is_active=True
        )
        db_session.add(other_template)
        db_session.commit()
        
        # 尝试访问其他用户的模板
        response = client.get(
            f"/api/v1/ai/templates/{other_template.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 404  # 应该返回404而不是403，保护隐私
