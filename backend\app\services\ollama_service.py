import asyncio
import json
import aiohttp
from typing import Dict, List, Optional, Any
from datetime import datetime
from loguru import logger

from ..core.config import settings


class OllamaService:
    """Ollama服务类，处理本地大语言模型调用"""
    
    def __init__(self, base_url: str = None, timeout: int = 60):
        self.base_url = base_url or settings.OLLAMA_BASE_URL or "http://localhost:11434"
        self.timeout = timeout
        self.session = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def list_models(self) -> Dict[str, Any]:
        """获取可用模型列表"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                )
            
            async with self.session.get(f"{self.base_url}/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        "success": True,
                        "models": data.get("models", []),
                        "count": len(data.get("models", []))
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status}: {await response.text()}",
                        "models": []
                    }
        except aiohttp.ClientError as e:
            logger.error(f"Ollama连接错误: {str(e)}")
            return {
                "success": False,
                "error": f"连接失败: {str(e)}",
                "models": []
            }
        except Exception as e:
            logger.error(f"获取Ollama模型列表失败: {str(e)}")
            return {
                "success": False,
                "error": f"获取模型列表失败: {str(e)}",
                "models": []
            }
    
    async def generate_completion(
        self,
        model: str,
        prompt: str,
        system_prompt: str = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        stream: bool = False
    ) -> Dict[str, Any]:
        """生成文本补全"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                )
            
            # 构建请求数据
            data = {
                "model": model,
                "prompt": prompt,
                "stream": stream,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens,
                }
            }
            
            if system_prompt:
                data["system"] = system_prompt
            
            async with self.session.post(
                f"{self.base_url}/api/generate",
                json=data
            ) as response:
                if response.status == 200:
                    if stream:
                        return await self._handle_stream_response(response)
                    else:
                        result = await response.json()
                        return {
                            "success": True,
                            "response": result.get("response", ""),
                            "model": result.get("model", model),
                            "created_at": result.get("created_at"),
                            "done": result.get("done", True),
                            "total_duration": result.get("total_duration"),
                            "load_duration": result.get("load_duration"),
                            "prompt_eval_count": result.get("prompt_eval_count"),
                            "prompt_eval_duration": result.get("prompt_eval_duration"),
                            "eval_count": result.get("eval_count"),
                            "eval_duration": result.get("eval_duration")
                        }
                else:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "error": f"HTTP {response.status}: {error_text}",
                        "response": ""
                    }
        
        except aiohttp.ClientError as e:
            logger.error(f"Ollama请求错误: {str(e)}")
            return {
                "success": False,
                "error": f"请求失败: {str(e)}",
                "response": ""
            }
        except Exception as e:
            logger.error(f"Ollama生成失败: {str(e)}")
            return {
                "success": False,
                "error": f"生成失败: {str(e)}",
                "response": ""
            }
    
    async def chat_completion(
        self,
        model: str,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 1000,
        stream: bool = False
    ) -> Dict[str, Any]:
        """聊天补全（类似OpenAI的chat completion）"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                )
            
            # 构建请求数据
            data = {
                "model": model,
                "messages": messages,
                "stream": stream,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens,
                }
            }
            
            async with self.session.post(
                f"{self.base_url}/api/chat",
                json=data
            ) as response:
                if response.status == 200:
                    if stream:
                        return await self._handle_stream_response(response)
                    else:
                        result = await response.json()
                        return {
                            "success": True,
                            "message": result.get("message", {}),
                            "model": result.get("model", model),
                            "created_at": result.get("created_at"),
                            "done": result.get("done", True),
                            "total_duration": result.get("total_duration"),
                            "load_duration": result.get("load_duration"),
                            "prompt_eval_count": result.get("prompt_eval_count"),
                            "prompt_eval_duration": result.get("prompt_eval_duration"),
                            "eval_count": result.get("eval_count"),
                            "eval_duration": result.get("eval_duration")
                        }
                else:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "error": f"HTTP {response.status}: {error_text}",
                        "message": {}
                    }
        
        except aiohttp.ClientError as e:
            logger.error(f"Ollama聊天请求错误: {str(e)}")
            return {
                "success": False,
                "error": f"请求失败: {str(e)}",
                "message": {}
            }
        except Exception as e:
            logger.error(f"Ollama聊天失败: {str(e)}")
            return {
                "success": False,
                "error": f"聊天失败: {str(e)}",
                "message": {}
            }
    
    async def _handle_stream_response(self, response) -> Dict[str, Any]:
        """处理流式响应"""
        full_response = ""
        async for line in response.content:
            if line:
                try:
                    data = json.loads(line.decode('utf-8'))
                    if 'response' in data:
                        full_response += data['response']
                    elif 'message' in data and 'content' in data['message']:
                        full_response += data['message']['content']
                except json.JSONDecodeError:
                    continue
        
        return {
            "success": True,
            "response": full_response,
            "stream": True
        }
    
    async def check_health(self) -> Dict[str, Any]:
        """检查Ollama服务健康状态"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=10)  # 健康检查使用较短超时
                )
            
            async with self.session.get(f"{self.base_url}/api/tags") as response:
                if response.status == 200:
                    return {
                        "success": True,
                        "status": "healthy",
                        "url": self.base_url,
                        "response_time": response.headers.get("X-Response-Time")
                    }
                else:
                    return {
                        "success": False,
                        "status": "unhealthy",
                        "url": self.base_url,
                        "error": f"HTTP {response.status}"
                    }
        except Exception as e:
            return {
                "success": False,
                "status": "unreachable",
                "url": self.base_url,
                "error": str(e)
            }
    
    async def pull_model(self, model_name: str) -> Dict[str, Any]:
        """拉取模型"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=300)  # 拉取模型需要更长时间
                )
            
            data = {"name": model_name}
            
            async with self.session.post(
                f"{self.base_url}/api/pull",
                json=data
            ) as response:
                if response.status == 200:
                    return {
                        "success": True,
                        "message": f"模型 {model_name} 拉取成功"
                    }
                else:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "error": f"拉取失败: {error_text}"
                    }
        except Exception as e:
            return {
                "success": False,
                "error": f"拉取模型失败: {str(e)}"
            }


# 全局Ollama服务实例
ollama_service = None

async def get_ollama_service() -> OllamaService:
    """获取Ollama服务实例"""
    global ollama_service
    if ollama_service is None:
        ollama_service = OllamaService()
    return ollama_service
