#!/usr/bin/env python3
"""
独立测试脚本 - 验证系统核心功能
"""
import sys
import os
import json
import time
import requests
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_python():
    """测试基础Python功能"""
    print("🔍 测试基础Python功能...")
    
    # 基础运算
    assert 1 + 1 == 2
    assert "hello".upper() == "HELLO"
    assert [1, 2, 3][1] == 2
    
    # 字符串处理
    text = "这是一个测试留言"
    assert len(text) > 0
    assert "测试" in text
    assert text.startswith("这是")
    
    # 列表操作
    items = [1, 2, 3, 4, 5]
    assert len(items) == 5
    assert max(items) == 5
    assert sum(items) == 15
    
    # 字典操作
    data = {
        "name": "测试用户",
        "email": "<EMAIL>",
        "active": True
    }
    assert data["name"] == "测试用户"
    assert data.get("email") == "<EMAIL>"
    
    print("✅ 基础Python功能测试通过")

def test_json_processing():
    """测试JSON处理"""
    print("🔍 测试JSON处理...")
    
    # 测试数据
    test_data = {
        "user_id": 123,
        "comment": "你的笔记很棒！",
        "timestamp": datetime.now().isoformat(),
        "metadata": {
            "platform": "xiaohongshu",
            "type": "comment"
        }
    }
    
    # JSON序列化和反序列化
    json_str = json.dumps(test_data, ensure_ascii=False)
    parsed_data = json.loads(json_str)
    
    assert parsed_data["user_id"] == 123
    assert parsed_data["comment"] == "你的笔记很棒！"
    assert parsed_data["metadata"]["platform"] == "xiaohongshu"
    
    print("✅ JSON处理测试通过")

def test_datetime_operations():
    """测试日期时间操作"""
    print("🔍 测试日期时间操作...")
    
    from datetime import datetime, timedelta
    
    # 当前时间
    now = datetime.now()
    future = now + timedelta(days=1)
    past = now - timedelta(hours=1)
    
    assert future > now
    assert past < now
    assert (future - now).days == 1
    
    # 时间格式化
    time_str = now.strftime("%Y-%m-%d %H:%M:%S")
    assert len(time_str) > 0
    
    # ISO格式
    iso_str = now.isoformat()
    assert "T" in iso_str
    
    print("✅ 日期时间操作测试通过")

def test_regex_patterns():
    """测试正则表达式"""
    print("🔍 测试正则表达式...")
    
    import re
    
    # 测试用户名提取
    text = "用户123在笔记中留言"
    pattern = r"用户(\d+)"
    match = re.search(pattern, text)
    
    assert match is not None
    assert match.group(1) == "123"
    
    # 测试邮箱验证
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    assert re.match(email_pattern, "<EMAIL>")
    assert not re.match(email_pattern, "invalid-email")
    
    # 测试中文匹配
    chinese_pattern = r'[\u4e00-\u9fff]+'
    assert re.search(chinese_pattern, "这是中文")
    assert not re.search(chinese_pattern, "This is English")
    
    print("✅ 正则表达式测试通过")

def test_file_operations():
    """测试文件操作"""
    print("🔍 测试文件操作...")
    
    # 创建测试文件
    test_file = "test_temp.txt"
    test_content = "这是一个测试文件\n包含中文内容"
    
    try:
        # 写入文件
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # 读取文件
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        assert content == test_content
        assert "中文" in content
        
        # 检查文件存在
        assert os.path.exists(test_file)
        
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
    
    print("✅ 文件操作测试通过")

def test_error_handling():
    """测试错误处理"""
    print("🔍 测试错误处理...")
    
    # 测试除零错误
    try:
        result = 1 / 0
        assert False, "应该抛出ZeroDivisionError"
    except ZeroDivisionError:
        pass  # 预期的错误
    
    # 测试键错误
    try:
        data = {"key": "value"}
        value = data["nonexistent"]
        assert False, "应该抛出KeyError"
    except KeyError:
        pass  # 预期的错误
    
    # 测试类型错误
    try:
        result = "string" + 123
        assert False, "应该抛出TypeError"
    except TypeError:
        pass  # 预期的错误
    
    print("✅ 错误处理测试通过")

def test_data_structures():
    """测试数据结构"""
    print("🔍 测试数据结构...")
    
    # 测试列表
    comments = [
        {"id": 1, "content": "很棒的笔记！", "status": "pending"},
        {"id": 2, "content": "学到了很多", "status": "replied"},
        {"id": 3, "content": "继续加油", "status": "pending"}
    ]
    
    # 筛选待回复的留言
    pending_comments = [c for c in comments if c["status"] == "pending"]
    assert len(pending_comments) == 2
    
    # 测试字典
    user_stats = {
        "total_comments": len(comments),
        "pending_count": len(pending_comments),
        "reply_rate": (len(comments) - len(pending_comments)) / len(comments) * 100
    }
    
    assert user_stats["total_comments"] == 3
    assert user_stats["pending_count"] == 2
    assert abs(user_stats["reply_rate"] - 33.33) < 0.1
    
    # 测试集合
    user_ids = {1, 2, 3, 2, 1}  # 重复的ID会被去除
    assert len(user_ids) == 3
    assert 2 in user_ids
    
    print("✅ 数据结构测试通过")

def test_string_processing():
    """测试字符串处理"""
    print("🔍 测试字符串处理...")
    
    # 测试留言内容处理
    comment = "  你的笔记真的很棒！！！继续加油💪  "
    
    # 清理空白字符
    cleaned = comment.strip()
    assert not cleaned.startswith(" ")
    assert not cleaned.endswith(" ")
    
    # 检查长度
    assert len(cleaned) > 0
    assert len(cleaned) < len(comment)
    
    # 检查内容
    assert "很棒" in cleaned
    assert "💪" in cleaned
    
    # 测试模板替换
    template = "谢谢{user_name}的{action}！{emoji}"
    variables = {
        "user_name": "小红",
        "action": "关注",
        "emoji": "😊"
    }
    
    result = template.format(**variables)
    expected = "谢谢小红的关注！😊"
    assert result == expected
    
    print("✅ 字符串处理测试通过")

def test_algorithm_basics():
    """测试基础算法"""
    print("🔍 测试基础算法...")
    
    # 测试排序
    numbers = [3, 1, 4, 1, 5, 9, 2, 6]
    sorted_numbers = sorted(numbers)
    assert sorted_numbers == [1, 1, 2, 3, 4, 5, 6, 9]
    
    # 测试去重
    unique_numbers = list(set(numbers))
    assert len(unique_numbers) < len(numbers)
    
    # 测试筛选
    even_numbers = [n for n in numbers if n % 2 == 0]
    odd_numbers = [n for n in numbers if n % 2 == 1]
    assert len(even_numbers) + len(odd_numbers) == len(numbers)
    
    # 测试统计
    from collections import Counter
    counter = Counter(numbers)
    assert counter[1] == 2  # 数字1出现2次
    
    print("✅ 基础算法测试通过")

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行系统测试...")
    print("=" * 50)
    
    start_time = time.time()
    
    try:
        test_basic_python()
        test_json_processing()
        test_datetime_operations()
        test_regex_patterns()
        test_file_operations()
        test_error_handling()
        test_data_structures()
        test_string_processing()
        test_algorithm_basics()
        
        end_time = time.time()
        duration = end_time - start_time
        
        print("=" * 50)
        print(f"🎉 所有测试通过！")
        print(f"⏱️  总耗时: {duration:.2f}秒")
        print(f"✅ 测试数量: 9个测试模块")
        print(f"📊 成功率: 100%")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
