"""
留言相关的Pydantic模型
"""
from typing import Optional, List
from pydantic import BaseModel, validator
from datetime import datetime
from enum import Enum


class CommentStatusEnum(str, Enum):
    """留言状态枚举"""
    NEW = "new"
    REPLIED = "replied"
    IGNORED = "ignored"
    ERROR = "error"


class CommentBase(BaseModel):
    """留言基础模型"""
    comment_id: str
    content: str
    author_name: str
    author_id: Optional[str] = None
    
    @validator('comment_id')
    def validate_comment_id(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('留言ID不能为空')
        return v.strip()
    
    @validator('content')
    def validate_content(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('留言内容不能为空')
        return v.strip()
    
    @validator('author_name')
    def validate_author_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('作者名称不能为空')
        return v.strip()


class CommentCreate(CommentBase):
    """创建留言模型"""
    note_id: int
    publish_time: Optional[datetime] = None
    parent_comment_id: Optional[str] = None  # 父留言ID（用于回复）
    
    @validator('note_id')
    def validate_note_id(cls, v):
        if v <= 0:
            raise ValueError('笔记ID必须大于0')
        return v


class CommentUpdate(BaseModel):
    """更新留言模型"""
    status: Optional[CommentStatusEnum] = None
    reply_content: Optional[str] = None
    replied_at: Optional[datetime] = None


class CommentResponse(CommentBase):
    """留言响应模型"""
    id: int
    note_id: int
    parent_comment_id: Optional[str] = None
    publish_time: Optional[datetime] = None
    status: CommentStatusEnum
    reply_content: Optional[str] = None
    replied_at: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class CommentReply(BaseModel):
    """留言回复模型"""
    comment_id: int
    reply_content: str
    
    @validator('reply_content')
    def validate_reply_content(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('回复内容不能为空')
        if len(v) > 1000:
            raise ValueError('回复内容不能超过1000个字符')
        return v.strip()


class CommentBatchReply(BaseModel):
    """批量回复留言模型"""
    comment_ids: List[int]
    reply_content: str
    
    @validator('comment_ids')
    def validate_comment_ids(cls, v):
        if not v or len(v) == 0:
            raise ValueError('留言ID列表不能为空')
        if len(v) > 20:  # 限制批量回复数量
            raise ValueError('批量回复最多支持20条留言')
        return v
    
    @validator('reply_content')
    def validate_reply_content(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('回复内容不能为空')
        if len(v) > 1000:
            raise ValueError('回复内容不能超过1000个字符')
        return v.strip()


class CommentSearchFilter(BaseModel):
    """留言搜索过滤器"""
    note_id: Optional[int] = None
    status: Optional[CommentStatusEnum] = None
    author_name: Optional[str] = None
    keyword: Optional[str] = None  # 搜索留言内容
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    
    @validator('author_name')
    def validate_author_name(cls, v):
        if v is not None and len(v.strip()) == 0:
            return None
        return v.strip() if v else None
    
    @validator('keyword')
    def validate_keyword(cls, v):
        if v is not None and len(v.strip()) == 0:
            return None
        return v.strip() if v else None


class CommentStats(BaseModel):
    """留言统计模型"""
    note_id: int
    total_comments: int = 0
    new_comments: int = 0
    replied_comments: int = 0
    ignored_comments: int = 0
    error_comments: int = 0
    last_activity: Optional[datetime] = None


class CrawlRequest(BaseModel):
    """抓取请求模型"""
    note_ids: Optional[List[int]] = None  # 指定笔记ID，为空则抓取所有活跃笔记
    force_crawl: bool = False  # 是否强制抓取（忽略间隔限制）
    
    @validator('note_ids')
    def validate_note_ids(cls, v):
        if v is not None and len(v) > 50:
            raise ValueError('单次抓取最多支持50个笔记')
        return v


class CrawlResult(BaseModel):
    """抓取结果模型"""
    note_id: int
    success: bool
    new_comments_count: int = 0
    error_message: Optional[str] = None
    crawl_time: datetime
