#!/usr/bin/env python3
"""
创建测试留言数据
"""
from app.models import get_db, User, Note, Comment, XiaohongshuAccount
from app.models.note import CommentStatus
from datetime import datetime, timedelta
import random

def create_test_data():
    """创建测试数据"""
    db = next(get_db())
    
    try:
        # 获取测试用户
        user = db.query(User).filter(User.username == 'testuser2').first()
        if not user:
            print("❌ 测试用户不存在")
            return
        
        print(f"✅ 找到测试用户: {user.username}")
        
        # 创建测试小红书账号
        account = db.query(XiaohongshuAccount).filter(
            XiaohongshuAccount.user_id == user.id
        ).first()
        
        if not account:
            account = XiaohongshuAccount(
                user_id=user.id,
                account_name="测试账号",
                account_id="test_account_123",
                login_phone="***********",
                is_active=True
            )
            db.add(account)
            db.commit()
            db.refresh(account)
            print(f"✅ 创建测试账号: {account.account_name}")
        else:
            print(f"✅ 找到测试账号: {account.account_name}")
        
        # 创建测试笔记
        note = db.query(Note).filter(Note.account_id == account.id).first()
        
        if not note:
            note = Note(
                account_id=account.id,
                note_id="test_note_123",
                note_url="https://www.xiaohongshu.com/explore/test_note_123",
                title="测试笔记 - 美妆分享",
                content="这是一篇测试笔记，分享一些美妆心得...",
                author_name=account.account_name,
                author_id=account.account_id,
                publish_time=datetime.now() - timedelta(days=1),
                likes_count=100,
                comments_count=0,
                shares_count=10,
                status="ACTIVE",
                auto_reply_enabled=True,
                crawl_interval=300
            )
            db.add(note)
            db.commit()
            db.refresh(note)
            print(f"✅ 创建测试笔记: {note.title}")
        else:
            print(f"✅ 找到测试笔记: {note.title}")
        
        # 创建测试留言
        test_comments = [
            {
                "comment_id": "comment_001",
                "content": "这个产品真的很好用，推荐给大家！",
                "user_name": "小红薯用户1",
                "user_id": "user_001",
                "status": CommentStatus.NEW
            },
            {
                "comment_id": "comment_002",
                "content": "请问这个色号适合黄皮吗？",
                "user_name": "美妆达人小李",
                "user_id": "user_002",
                "status": CommentStatus.NEW
            },
            {
                "comment_id": "comment_003",
                "content": "哪里可以买到同款？",
                "user_name": "时尚小达人",
                "user_id": "user_003",
                "status": CommentStatus.NEW
            },
            {
                "comment_id": "comment_004",
                "content": "效果怎么样？值得购买吗？",
                "user_name": "购物小能手",
                "user_id": "user_004",
                "status": CommentStatus.NEW
            },
            {
                "comment_id": "comment_005",
                "content": "已经回复过的留言测试",
                "user_name": "老用户",
                "user_id": "user_005",
                "status": CommentStatus.REPLIED,
                "reply_content": "感谢您的关注！",
                "reply_time": datetime.now() - timedelta(hours=2)
            }
        ]
        
        created_count = 0
        for comment_data in test_comments:
            # 检查是否已存在
            existing = db.query(Comment).filter(
                Comment.comment_id == comment_data["comment_id"]
            ).first()
            
            if not existing:
                comment = Comment(
                    note_id=note.id,
                    comment_id=comment_data["comment_id"],
                    content=comment_data["content"],
                    user_name=comment_data["user_name"],
                    user_id=comment_data["user_id"],
                    publish_time=datetime.now() - timedelta(
                        hours=random.randint(1, 24),
                        minutes=random.randint(0, 59)
                    ),
                    status=comment_data["status"],
                    reply_content=comment_data.get("reply_content"),
                    reply_time=comment_data.get("reply_time")
                )
                db.add(comment)
                created_count += 1
        
        db.commit()
        print(f"✅ 创建了 {created_count} 条测试留言")
        
        # 更新笔记的留言数量
        total_comments = db.query(Comment).filter(Comment.note_id == note.id).count()
        note.comments_count = total_comments
        db.commit()
        
        print(f"✅ 更新笔记留言数量: {total_comments}")
        print("🎉 测试数据创建完成！")
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_test_data()
