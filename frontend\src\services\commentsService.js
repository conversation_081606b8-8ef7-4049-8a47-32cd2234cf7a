import { request } from './api';

export const commentsService = {
  // 获取留言列表
  getComments: async (params = {}) => {
    const response = await request.get('/comments', { params });
    return response;
  },

  // 获取留言详情
  getCommentById: async (id) => {
    const response = await request.get(`/comments/${id}`);
    return response;
  },

  // 更新留言信息
  updateComment: async (id, commentData) => {
    const response = await request.put(`/comments/${id}`, commentData);
    return response;
  },

  // 回复留言
  replyComment: async (id, replyContent) => {
    const response = await request.post(`/comments/${id}/reply?reply_content=${encodeURIComponent(replyContent)}`);
    return response;
  },

  // 批量回复留言
  batchReply: async (commentIds, replyContent) => {
    const response = await request.post('/comments/batch-reply', {
      comment_ids: commentIds,
      reply_content: replyContent,
    });
    return response;
  },

  // 批量忽略留言
  batchIgnore: async (commentIds) => {
    const response = await request.post('/comments/batch-ignore', {
      comment_ids: commentIds,
    });
    return response;
  },

  // 获取留言统计
  getCommentsStats: async () => {
    const response = await request.get('/comments/stats');
    return response;
  },

  // 获取待处理留言
  getPendingComments: async (params = {}) => {
    const response = await request.get('/comments/pending', { params });
    return response;
  },

  // 标记留言为已读
  markAsRead: async (id) => {
    const response = await request.put(`/comments/${id}`, {
      is_read: true,
    });
    return response;
  },

  // 设置留言情感标签
  setSentiment: async (id, sentiment) => {
    const response = await request.put(`/comments/${id}`, {
      sentiment: sentiment,
    });
    return response;
  },

  // 搜索留言
  searchComments: async (query, filters = {}) => {
    const response = await request.get('/comments', {
      params: {
        search: query,
        ...filters,
      },
    });
    return response;
  },
};
