{"name": "xia<PERSON><PERSON><PERSON>-auto-reply-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.2.6", "antd": "^5.12.0", "axios": "^1.6.0", "dayjs": "^1.11.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.18.0", "recharts": "^3.1.0", "zustand": "^4.4.6"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "vite": "^4.5.0"}}