#!/usr/bin/env python3
"""
测试爬虫状态API
"""
import requests
import json

# API配置
BASE_URL = "http://localhost:8000/api/v1"

def test_login():
    """测试登录功能"""
    print("🔍 测试登录功能...")
    
    try:
        login_data = {
            'username': 'testuser2',
            'password': 'testpass123'
        }
        
        response = requests.post(f'{BASE_URL}/auth/login', data=login_data, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 登录成功!")
            return data['data']['access_token']
        else:
            print(f"❌ 登录失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_crawler_status(token):
    """测试爬虫状态API"""
    print("\n🔍 测试爬虫状态API...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{BASE_URL}/crawler/status", headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 爬虫状态API调用成功!")
            
            # 打印完整响应
            print(f"📄 完整响应:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 打印统计信息
            stats = data.get('data', {})
            print(f"\n📊 统计信息:")
            print(f"   总任务数: {stats.get('total_tasks', 0)}")
            print(f"   运行中任务: {stats.get('running_tasks', 0)}")
            print(f"   已完成任务: {stats.get('completed_tasks', 0)}")
            print(f"   失败任务: {stats.get('failed_tasks', 0)}")
            print(f"   总评论数: {stats.get('total_comments', 0)}")
            print(f"   新评论数: {stats.get('new_comments', 0)}")
            print(f"   成功率: {stats.get('success_rate', 0)}%")
            print(f"   平均响应时间: {stats.get('avg_response_time', 0)}秒")
            print(f"   爬虫状态: {stats.get('crawler_status', 'unknown')}")
            
            # 打印任务列表
            tasks = stats.get('tasks', [])
            print(f"\n📋 任务列表 ({len(tasks)} 个任务):")
            for i, task in enumerate(tasks, 1):
                print(f"   {i}. {task.get('task_name', 'Unknown')}")
                print(f"      ID: {task.get('id', 'N/A')}")
                print(f"      状态: {task.get('status', 'unknown')}")
                print(f"      类型: {task.get('task_type', 'unknown')}")
                print(f"      进度: {task.get('progress', 0)}%")
                print(f"      评论数: {task.get('comments_found', 0)}")
                print(f"      新评论: {task.get('new_comments', 0)}")
                print(f"      错误数: {task.get('error_count', 0)}")
                print(f"      创建时间: {task.get('created_at', 'N/A')}")
                print()
            
            return True
        else:
            print(f"❌ 爬虫状态API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 爬虫状态API测试")
    print("=" * 50)
    
    # 1. 登录测试
    token = test_login()
    
    if not token:
        print("❌ 登录失败，测试终止")
        return
    
    # 2. 爬虫状态测试
    status_ok = test_crawler_status(token)
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 测试结果总结:")
    
    if status_ok:
        print("✅ 爬虫状态API测试通过")
    else:
        print("❌ 爬虫状态API测试失败")

if __name__ == "__main__":
    main()
