#!/usr/bin/env python3
"""
修复示例数据的用户关联
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.database import SessionLocal
from app.models.user import User, XiaohongshuAccount
from app.models.crawler_task import CrawlerTask, CrawlerTaskLog
from app.models.note import Note

def fix_sample_data_user():
    """修复示例数据的用户关联"""
    db = SessionLocal()
    
    try:
        # 查找testuser2的用户ID
        test_user = db.query(User).filter(User.username == "testuser2").first()
        if not test_user:
            print("❌ 找不到testuser2用户")
            return
        
        print(f"✅ 找到testuser2用户，ID: {test_user.id}")
        
        # 查找当前示例数据的用户ID
        sample_tasks = db.query(CrawlerTask).all()
        if not sample_tasks:
            print("❌ 没有找到示例任务数据")
            return
        
        print(f"📋 找到 {len(sample_tasks)} 个示例任务")
        
        # 检查示例数据当前关联的用户ID
        current_user_ids = set(task.user_id for task in sample_tasks)
        print(f"📊 当前示例数据关联的用户ID: {current_user_ids}")
        
        # 如果testuser2已经是关联用户，则不需要修改
        if test_user.id in current_user_ids:
            print("✅ testuser2已经关联了示例数据，无需修改")
            return
        
        # 查找示例数据关联的账户和笔记
        sample_accounts = db.query(XiaohongshuAccount).filter(
            XiaohongshuAccount.user_id.in_(current_user_ids)
        ).all()
        
        sample_notes = db.query(Note).join(XiaohongshuAccount).filter(
            XiaohongshuAccount.user_id.in_(current_user_ids)
        ).all()
        
        print(f"📱 找到 {len(sample_accounts)} 个示例账户")
        print(f"📝 找到 {len(sample_notes)} 个示例笔记")
        
        # 更新所有相关数据的用户ID
        print("🔄 开始更新用户关联...")
        
        # 更新账户
        for account in sample_accounts:
            account.user_id = test_user.id
            print(f"   更新账户 {account.account_name} 的用户ID")
        
        # 更新任务
        for task in sample_tasks:
            task.user_id = test_user.id
            print(f"   更新任务 {task.task_name} 的用户ID")
        
        # 提交更改
        db.commit()
        print("✅ 用户关联更新完成！")
        
        # 验证更新结果
        updated_tasks = db.query(CrawlerTask).filter(
            CrawlerTask.user_id == test_user.id
        ).all()
        
        updated_accounts = db.query(XiaohongshuAccount).filter(
            XiaohongshuAccount.user_id == test_user.id
        ).all()
        
        print(f"🎯 验证结果:")
        print(f"   testuser2现在关联了 {len(updated_tasks)} 个任务")
        print(f"   testuser2现在关联了 {len(updated_accounts)} 个账户")
        
        # 显示任务详情
        print(f"\n📋 任务列表:")
        for task in updated_tasks:
            print(f"   - {task.task_name} (状态: {task.status}, 类型: {task.task_type})")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("🔧 修复示例数据用户关联")
    print("=" * 50)
    fix_sample_data_user()
