"""
认证API测试
"""
import pytest
from fastapi.testclient import TestClient


class TestAuth:
    """认证相关测试"""
    
    def test_register_success(self, client: TestClient):
        """测试用户注册成功"""
        response = client.post(
            "/api/v1/auth/register",
            json={
                "username": "newuser",
                "email": "<EMAIL>",
                "password": "password123"
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "access_token" in data["data"]
        assert data["data"]["token_type"] == "bearer"
    
    def test_register_duplicate_username(self, client: TestClient, test_user):
        """测试重复用户名注册"""
        response = client.post(
            "/api/v1/auth/register",
            json={
                "username": test_user.username,
                "email": "<EMAIL>",
                "password": "password123"
            }
        )
        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "已存在" in data["message"]
    
    def test_register_duplicate_email(self, client: TestClient, test_user):
        """测试重复邮箱注册"""
        response = client.post(
            "/api/v1/auth/register",
            json={
                "username": "anotheruser",
                "email": test_user.email,
                "password": "password123"
            }
        )
        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "已存在" in data["message"]
    
    def test_register_invalid_email(self, client: TestClient):
        """测试无效邮箱格式"""
        response = client.post(
            "/api/v1/auth/register",
            json={
                "username": "testuser",
                "email": "invalid-email",
                "password": "password123"
            }
        )
        assert response.status_code == 422
    
    def test_register_weak_password(self, client: TestClient):
        """测试弱密码"""
        response = client.post(
            "/api/v1/auth/register",
            json={
                "username": "testuser",
                "email": "<EMAIL>",
                "password": "123"
            }
        )
        assert response.status_code == 422
    
    def test_login_success(self, client: TestClient, test_user):
        """测试登录成功"""
        response = client.post(
            "/api/v1/auth/login",
            data={
                "username": test_user.username,
                "password": "secret"
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "access_token" in data["data"]
        assert data["data"]["token_type"] == "bearer"
    
    def test_login_wrong_username(self, client: TestClient):
        """测试错误用户名登录"""
        response = client.post(
            "/api/v1/auth/login",
            data={
                "username": "wronguser",
                "password": "secret"
            }
        )
        assert response.status_code == 401
        data = response.json()
        assert data["success"] is False
        assert "用户名或密码错误" in data["message"]
    
    def test_login_wrong_password(self, client: TestClient, test_user):
        """测试错误密码登录"""
        response = client.post(
            "/api/v1/auth/login",
            data={
                "username": test_user.username,
                "password": "wrongpassword"
            }
        )
        assert response.status_code == 401
        data = response.json()
        assert data["success"] is False
        assert "用户名或密码错误" in data["message"]
    
    def test_get_current_user_success(self, client: TestClient, auth_headers):
        """测试获取当前用户信息成功"""
        response = client.get(
            "/api/v1/auth/me",
            headers=auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "username" in data["data"]
        assert "email" in data["data"]
    
    def test_get_current_user_unauthorized(self, client: TestClient):
        """测试未授权获取用户信息"""
        response = client.get("/api/v1/auth/me")
        assert response.status_code == 401
    
    def test_get_current_user_invalid_token(self, client: TestClient):
        """测试无效token获取用户信息"""
        response = client.get(
            "/api/v1/auth/me",
            headers={"Authorization": "Bearer invalid_token"}
        )
        assert response.status_code == 401
    
    def test_refresh_token_success(self, client: TestClient, auth_headers):
        """测试刷新token成功"""
        response = client.post(
            "/api/v1/auth/refresh",
            headers=auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "access_token" in data["data"]
    
    def test_logout_success(self, client: TestClient, auth_headers):
        """测试登出成功"""
        response = client.post(
            "/api/v1/auth/logout",
            headers=auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    def test_change_password_success(self, client: TestClient, auth_headers):
        """测试修改密码成功"""
        response = client.post(
            "/api/v1/auth/change-password",
            headers=auth_headers,
            json={
                "old_password": "secret",
                "new_password": "newsecret123"
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    def test_change_password_wrong_old_password(self, client: TestClient, auth_headers):
        """测试修改密码时旧密码错误"""
        response = client.post(
            "/api/v1/auth/change-password",
            headers=auth_headers,
            json={
                "old_password": "wrongpassword",
                "new_password": "newsecret123"
            }
        )
        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "旧密码错误" in data["message"]
    
    def test_reset_password_request(self, client: TestClient, test_user):
        """测试请求重置密码"""
        response = client.post(
            "/api/v1/auth/reset-password-request",
            json={"email": test_user.email}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    def test_reset_password_request_nonexistent_email(self, client: TestClient):
        """测试不存在的邮箱请求重置密码"""
        response = client.post(
            "/api/v1/auth/reset-password-request",
            json={"email": "<EMAIL>"}
        )
        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False
