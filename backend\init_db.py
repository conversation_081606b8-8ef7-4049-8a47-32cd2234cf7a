#!/usr/bin/env python3
"""
数据库初始化脚本
创建所有数据库表并插入初始数据
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.models import create_tables, drop_tables, SessionLocal
from app.models.user import User, XiaohongshuAccount
from app.models.note import Note, Comment, NoteStatus, CommentStatus
from app.models.reply_rule import ReplyRule, ReplyLog, SystemLog, RuleType
from sqlalchemy.exc import IntegrityError
from datetime import datetime, timedelta
import hashlib

def create_sample_data():
    """创建示例数据"""
    db = SessionLocal()
    try:
        # 创建示例用户
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password=hashlib.sha256("password123".encode()).hexdigest(),
            is_active=True
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        
        # 创建小红书账号
        account = XiaohongshuAccount(
            user_id=user.id,
            account_name="主账号",
            account_id="test_xiaohongshu",
            cookies="test_cookies",
            is_active=True
        )
        db.add(account)
        db.commit()
        db.refresh(account)
        
        # 创建示例笔记
        notes_data = [
            {
                "title": "美妆分享 - 夏日护肤心得",
                "url": "https://www.xiaohongshu.com/explore/123456",
                "note_id": "123456",
                "is_monitoring": True,
                "crawl_interval": 30
            },
            {
                "title": "时尚穿搭 - 春季搭配指南", 
                "url": "https://www.xiaohongshu.com/explore/789012",
                "note_id": "789012",
                "is_monitoring": False,
                "crawl_interval": 60
            },
            {
                "title": "美食探店 - 网红咖啡厅推荐",
                "url": "https://www.xiaohongshu.com/explore/345678",
                "note_id": "345678",
                "is_monitoring": True,
                "crawl_interval": 45
            }
        ]
        
        for note_data in notes_data:
            note = Note(
                account_id=account.id,
                title=note_data["title"],
                note_url=note_data["url"],
                note_id=note_data["note_id"],
                crawl_interval=note_data["crawl_interval"],
                status=NoteStatus.ACTIVE,
                auto_reply_enabled=note_data["is_monitoring"]
            )
            db.add(note)
        
        db.commit()
        
        # 获取创建的笔记
        notes = db.query(Note).all()
        
        # 创建示例评论
        comments_data = [
            {
                "content": "这个护肤心得太实用了！请问用的是什么品牌的产品呀？",
                "author": "小红",
                "note": notes[0]
            },
            {
                "content": "姐姐皮肤好好啊，能分享一下具体的护肤步骤吗？",
                "author": "美妆爱好者",
                "note": notes[0]
            },
            {
                "content": "这套搭配真的很好看！请问外套是在哪里买的？",
                "author": "时尚达人",
                "note": notes[1]
            },
            {
                "content": "这家咖啡厅环境真不错，下次一定要去试试！",
                "author": "咖啡控",
                "note": notes[2]
            }
        ]
        
        for i, comment_data in enumerate(comments_data):
            comment = Comment(
                note_id=comment_data["note"].id,
                comment_id=f"comment_{int(datetime.now().timestamp())}_{i}",
                content=comment_data["content"],
                user_name=comment_data["author"],
                status=CommentStatus.NEW,
                publish_time=datetime.now() - timedelta(minutes=30)
            )
            db.add(comment)
        
        # 创建回复规则
        rules_data = [
            {
                "name": "产品咨询回复",
                "rule_type": RuleType.KEYWORD,
                "trigger_condition": "产品,品牌,哪里买,链接",
                "reply_templates": ["感谢您的关注！具体产品信息我会私信发给您哦～"],
                "is_active": True
            },
            {
                "name": "护肤咨询回复",
                "rule_type": RuleType.KEYWORD,
                "trigger_condition": "护肤,步骤,方法,怎么用",
                "reply_templates": ["谢谢亲的喜欢！护肤方法因人而异，建议根据自己的肌肤状态调整哦～"],
                "is_active": True
            },
            {
                "name": "通用感谢回复",
                "rule_type": RuleType.TEMPLATE,
                "trigger_condition": "*",
                "reply_templates": ["谢谢亲的支持和关注！❤️"],
                "is_active": True
            }
        ]

        for rule_data in rules_data:
            rule = ReplyRule(
                user_id=user.id,
                name=rule_data["name"],
                rule_type=rule_data["rule_type"],
                trigger_condition=rule_data["trigger_condition"],
                reply_templates=rule_data["reply_templates"],
                is_active=rule_data["is_active"]
            )
            db.add(rule)
        
        db.commit()
        
        print("✅ 示例数据创建成功！")
        print(f"   - 用户: {user.username}")
        print(f"   - 小红书账号: {account.account_name}")
        print(f"   - 笔记数量: {len(notes)}")
        print(f"   - 评论数量: {len(comments_data)}")
        print(f"   - 回复规则数量: {len(rules_data)}")
        
    except IntegrityError as e:
        print(f"❌ 数据已存在或创建失败: {e}")
        db.rollback()
    except Exception as e:
        print(f"❌ 创建示例数据失败: {e}")
        db.rollback()
    finally:
        db.close()

def main():
    """主函数"""
    print("🚀 开始初始化数据库...")
    
    try:
        # 创建所有表
        print("📋 创建数据库表...")
        create_tables()
        print("✅ 数据库表创建成功！")
        
        # 创建示例数据
        print("📝 创建示例数据...")
        create_sample_data()
        
        print("🎉 数据库初始化完成！")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
