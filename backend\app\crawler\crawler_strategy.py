"""
数据抓取策略优化
实现低频、模拟人工的数据抓取策略
"""
import asyncio
import random
import time
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from loguru import logger
import json


@dataclass
class CrawlerConfig:
    """爬虫配置"""
    min_delay: float = 2.0  # 最小延时（秒）
    max_delay: float = 5.0  # 最大延时（秒）
    max_requests_per_hour: int = 100  # 每小时最大请求数
    max_concurrent_requests: int = 1  # 最大并发请求数
    retry_attempts: int = 3  # 重试次数
    retry_delay: float = 10.0  # 重试延时
    user_agent_rotation: bool = True  # 是否轮换User-Agent
    proxy_rotation: bool = False  # 是否使用代理轮换
    behavior_simulation: bool = True  # 是否模拟人类行为


class RequestRateLimiter:
    """请求频率限制器"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.request_history = []
        
    async def wait_if_needed(self) -> None:
        """如果需要，等待以符合频率限制"""
        current_time = time.time()
        
        # 清理1小时前的请求记录
        self.request_history = [
            req_time for req_time in self.request_history 
            if current_time - req_time < 3600
        ]
        
        # 检查是否超过每小时限制
        if len(self.request_history) >= self.config.max_requests_per_hour:
            oldest_request = min(self.request_history)
            wait_time = 3600 - (current_time - oldest_request)
            if wait_time > 0:
                logger.info(f"达到每小时请求限制，等待 {wait_time:.1f} 秒")
                await asyncio.sleep(wait_time)
                
        # 添加随机延时
        delay = random.uniform(self.config.min_delay, self.config.max_delay)
        logger.debug(f"随机延时: {delay:.1f} 秒")
        await asyncio.sleep(delay)
        
        # 记录请求时间
        self.request_history.append(current_time)


class BehaviorSimulator:
    """人类行为模拟器"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        
    async def simulate_reading_time(self, content_length: int = 0) -> None:
        """模拟阅读时间"""
        if not self.config.behavior_simulation:
            return
            
        # 基于内容长度计算阅读时间
        base_time = 2.0  # 基础时间
        reading_time = base_time + (content_length / 100) * 0.5  # 每100字符0.5秒
        reading_time = min(reading_time, 30.0)  # 最大30秒
        
        # 添加随机变化
        actual_time = random.uniform(reading_time * 0.7, reading_time * 1.3)
        
        logger.debug(f"模拟阅读时间: {actual_time:.1f} 秒")
        await asyncio.sleep(actual_time)
        
    async def simulate_mouse_movement(self, page) -> None:
        """模拟鼠标移动"""
        if not self.config.behavior_simulation:
            return
            
        try:
            # 随机鼠标移动
            for _ in range(random.randint(2, 5)):
                x = random.randint(100, 1200)
                y = random.randint(100, 800)
                await page.mouse.move(x, y)
                await asyncio.sleep(random.uniform(0.1, 0.5))
        except Exception as e:
            logger.warning(f"模拟鼠标移动失败: {e}")
            
    async def simulate_scrolling(self, page) -> None:
        """模拟滚动行为"""
        if not self.config.behavior_simulation:
            return
            
        try:
            # 随机滚动
            scroll_count = random.randint(2, 5)
            for _ in range(scroll_count):
                scroll_distance = random.randint(200, 800)
                direction = random.choice([1, -1])
                await page.evaluate(f"window.scrollBy(0, {scroll_distance * direction})")
                await asyncio.sleep(random.uniform(0.5, 2.0))
        except Exception as e:
            logger.warning(f"模拟滚动失败: {e}")


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        
    async def handle_with_retry(self, func: Callable, *args, **kwargs):
        """带重试的错误处理"""
        last_exception = None
        
        for attempt in range(self.config.retry_attempts):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                logger.warning(f"尝试 {attempt + 1}/{self.config.retry_attempts} 失败: {e}")
                
                if attempt < self.config.retry_attempts - 1:
                    # 指数退避
                    wait_time = self.config.retry_delay * (2 ** attempt)
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                    
        logger.error(f"所有重试都失败了，最后的错误: {last_exception}")
        raise last_exception


class CrawlerStrategy:
    """爬虫策略管理器"""
    
    def __init__(self, config: CrawlerConfig = None):
        self.config = config or CrawlerConfig()
        self.rate_limiter = RequestRateLimiter(self.config)
        self.behavior_simulator = BehaviorSimulator(self.config)
        self.error_handler = ErrorHandler(self.config)
        self.session_stats = {
            'requests_made': 0,
            'errors_encountered': 0,
            'start_time': time.time()
        }
        
    async def execute_request(self, func: Callable, *args, **kwargs):
        """执行请求（带策略控制）"""
        # 频率限制
        await self.rate_limiter.wait_if_needed()
        
        # 执行请求（带重试）
        try:
            result = await self.error_handler.handle_with_retry(func, *args, **kwargs)
            self.session_stats['requests_made'] += 1
            return result
        except Exception as e:
            self.session_stats['errors_encountered'] += 1
            raise e
            
    async def simulate_human_interaction(self, page) -> None:
        """模拟人类交互"""
        await self.behavior_simulator.simulate_mouse_movement(page)
        await self.behavior_simulator.simulate_scrolling(page)
        
    async def simulate_content_consumption(self, content_length: int = 0) -> None:
        """模拟内容消费"""
        await self.behavior_simulator.simulate_reading_time(content_length)
        
    def get_session_stats(self) -> Dict:
        """获取会话统计"""
        current_time = time.time()
        duration = current_time - self.session_stats['start_time']
        
        return {
            **self.session_stats,
            'session_duration': duration,
            'requests_per_minute': self.session_stats['requests_made'] / (duration / 60) if duration > 0 else 0,
            'error_rate': self.session_stats['errors_encountered'] / max(self.session_stats['requests_made'], 1)
        }
        
    def save_stats(self, filename: str = "crawler_stats.json") -> None:
        """保存统计信息"""
        stats = self.get_session_stats()
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        logger.info(f"统计信息已保存到: {filename}")


class AdaptiveCrawlerStrategy(CrawlerStrategy):
    """自适应爬虫策略"""
    
    def __init__(self, config: CrawlerConfig = None):
        super().__init__(config)
        self.success_rate_window = []
        self.adaptive_delay_multiplier = 1.0
        
    async def execute_request(self, func: Callable, *args, **kwargs):
        """执行请求（带自适应调整）"""
        start_time = time.time()
        
        try:
            result = await super().execute_request(func, *args, **kwargs)
            self._record_success()
            return result
        except Exception as e:
            self._record_failure()
            self._adjust_strategy()
            raise e
        finally:
            # 记录请求耗时
            duration = time.time() - start_time
            logger.debug(f"请求耗时: {duration:.2f} 秒")
            
    def _record_success(self) -> None:
        """记录成功"""
        self.success_rate_window.append(True)
        self._trim_window()
        
    def _record_failure(self) -> None:
        """记录失败"""
        self.success_rate_window.append(False)
        self._trim_window()
        
    def _trim_window(self, window_size: int = 20) -> None:
        """修剪窗口大小"""
        if len(self.success_rate_window) > window_size:
            self.success_rate_window = self.success_rate_window[-window_size:]
            
    def _adjust_strategy(self) -> None:
        """调整策略"""
        if len(self.success_rate_window) < 5:
            return
            
        success_rate = sum(self.success_rate_window) / len(self.success_rate_window)
        
        if success_rate < 0.7:  # 成功率低于70%
            # 增加延时
            self.adaptive_delay_multiplier *= 1.5
            self.config.min_delay *= self.adaptive_delay_multiplier
            self.config.max_delay *= self.adaptive_delay_multiplier
            logger.warning(f"成功率较低({success_rate:.2f})，增加延时倍数到 {self.adaptive_delay_multiplier:.2f}")
        elif success_rate > 0.9:  # 成功率高于90%
            # 适当减少延时
            self.adaptive_delay_multiplier *= 0.9
            self.config.min_delay *= self.adaptive_delay_multiplier
            self.config.max_delay *= self.adaptive_delay_multiplier
            logger.info(f"成功率较高({success_rate:.2f})，减少延时倍数到 {self.adaptive_delay_multiplier:.2f}")


# 预定义的策略配置
CONSERVATIVE_CONFIG = CrawlerConfig(
    min_delay=5.0,
    max_delay=10.0,
    max_requests_per_hour=50,
    retry_attempts=5,
    behavior_simulation=True
)

BALANCED_CONFIG = CrawlerConfig(
    min_delay=2.0,
    max_delay=5.0,
    max_requests_per_hour=100,
    retry_attempts=3,
    behavior_simulation=True
)

AGGRESSIVE_CONFIG = CrawlerConfig(
    min_delay=1.0,
    max_delay=3.0,
    max_requests_per_hour=200,
    retry_attempts=2,
    behavior_simulation=False
)


async def demo_strategy_usage():
    """演示策略使用"""
    logger.info("演示爬虫策略使用...")
    
    # 使用保守策略
    strategy = AdaptiveCrawlerStrategy(CONSERVATIVE_CONFIG)
    
    # 模拟一些请求
    async def mock_request():
        await asyncio.sleep(0.1)  # 模拟网络请求
        if random.random() < 0.1:  # 10%失败率
            raise Exception("模拟请求失败")
        return "成功"
        
    for i in range(10):
        try:
            result = await strategy.execute_request(mock_request)
            logger.info(f"请求 {i+1}: {result}")
        except Exception as e:
            logger.error(f"请求 {i+1} 失败: {e}")
            
    # 打印统计信息
    stats = strategy.get_session_stats()
    logger.info(f"会话统计: {stats}")


if __name__ == "__main__":
    asyncio.run(demo_strategy_usage())
