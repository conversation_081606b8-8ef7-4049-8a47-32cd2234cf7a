#!/usr/bin/env python3
"""
检查AI功能相关的数据库表
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.database import engine
from sqlalchemy import text, inspect

def check_ai_tables():
    """检查AI功能相关的数据库表"""
    try:
        with engine.connect() as conn:
            inspector = inspect(engine)
            
            # 检查需要的表
            required_tables = [
                'reply_templates',
                'ai_replies', 
                'ai_rules',
                'ai_configs'
            ]
            
            existing_tables = inspector.get_table_names()
            
            print("🔍 检查AI功能相关数据库表")
            print("=" * 50)
            
            print(f"📊 数据库中现有的表 ({len(existing_tables)} 个):")
            for table in sorted(existing_tables):
                print(f"   ✅ {table}")
            
            print(f"\n🎯 AI功能需要的表:")
            missing_tables = []
            for table in required_tables:
                if table in existing_tables:
                    print(f"   ✅ {table} - 存在")
                else:
                    print(f"   ❌ {table} - 缺失")
                    missing_tables.append(table)
            
            if missing_tables:
                print(f"\n⚠️ 缺失的表: {missing_tables}")
                print("需要创建这些表才能使用AI功能")
                return False
            else:
                print(f"\n🎉 所有AI功能相关的表都存在！")
                
                # 检查表结构
                print(f"\n📋 表结构详情:")
                for table in required_tables:
                    columns = inspector.get_columns(table)
                    print(f"\n   📄 {table} ({len(columns)} 列):")
                    for col in columns:
                        col_type = str(col['type'])
                        nullable = "NULL" if col['nullable'] else "NOT NULL"
                        default = f" DEFAULT {col['default']}" if col['default'] else ""
                        print(f"      - {col['name']}: {col_type} {nullable}{default}")
                
                return True
                
    except Exception as e:
        print(f"❌ 检查数据库表失败: {e}")
        return False

if __name__ == "__main__":
    check_ai_tables()
