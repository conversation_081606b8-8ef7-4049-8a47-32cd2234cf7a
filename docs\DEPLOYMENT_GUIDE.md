# 小红书自动回复系统 - 部署指南

## 📖 目录

1. [系统要求](#系统要求)
2. [快速部署](#快速部署)
3. [生产环境部署](#生产环境部署)
4. [配置说明](#配置说明)
5. [监控和维护](#监控和维护)
6. [故障排除](#故障排除)
7. [安全建议](#安全建议)

## 💻 系统要求

### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **网络**: 稳定的互联网连接

### 推荐配置
- **CPU**: 4核心或更多
- **内存**: 8GB RAM或更多
- **存储**: 50GB SSD
- **网络**: 100Mbps带宽

### 软件依赖
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Git**: 2.30+
- **操作系统**: Ubuntu 20.04+, CentOS 8+, 或 Windows 10+

## 🚀 快速部署

### 1. 克隆项目
```bash
git clone https://github.com/your-repo/xiaohongshu-auto-reply.git
cd xiaohongshu-auto-reply
```

### 2. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，设置必要的配置
nano .env
```

### 3. 启动服务
```bash
# 给部署脚本执行权限
chmod +x scripts/deploy.sh

# 部署开发环境
./scripts/deploy.sh deploy development

# 或者使用Docker Compose
docker-compose up -d
```

### 4. 访问系统
- **前端界面**: http://localhost
- **API文档**: http://localhost:8000/docs
- **管理后台**: http://localhost:8000/admin

## 🏭 生产环境部署

### 1. 服务器准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 创建应用目录
sudo mkdir -p /opt/xiaohongshu-auto-reply
cd /opt/xiaohongshu-auto-reply
```

### 2. 配置生产环境
```bash
# 克隆代码
git clone https://github.com/your-repo/xiaohongshu-auto-reply.git .

# 配置生产环境变量
cp .env.example .env.production
nano .env.production
```

### 3. 生产环境配置示例
```bash
# .env.production
ENVIRONMENT=production
DEBUG=false

# 数据库配置
DB_PASSWORD=your_secure_db_password
DATABASE_URL=***********************************************************/xiaohongshu_auto_reply

# Redis配置
REDIS_PASSWORD=your_secure_redis_password
REDIS_URL=redis://:your_secure_redis_password@redis:6379/0

# 应用安全配置
SECRET_KEY=your_very_long_and_random_secret_key_here
ALLOWED_HOSTS=your-domain.com,www.your-domain.com
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com

# OpenAI配置
OPENAI_API_KEY=your_openai_api_key

# 邮件配置
SMTP_HOST=smtp.your-email-provider.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password

# 监控配置
GRAFANA_PASSWORD=your_secure_grafana_password
```

### 4. SSL证书配置
```bash
# 创建SSL目录
mkdir -p ssl

# 使用Let's Encrypt获取免费SSL证书
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com

# 复制证书到项目目录
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem ssl/key.pem
sudo chown $USER:$USER ssl/*.pem
```

### 5. 启动生产服务
```bash
# 部署生产环境（包含监控）
./scripts/deploy.sh deploy production

# 或者手动启动
docker-compose --profile monitoring up -d
```

### 6. 配置反向代理（可选）
如果使用外部Nginx：

```nginx
# /etc/nginx/sites-available/xiaohongshu-auto-reply
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/key.pem;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/v1/ws {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## ⚙️ 配置说明

### 环境变量详解

#### 数据库配置
```bash
DB_PASSWORD=postgres123                    # 数据库密码
DATABASE_URL=postgresql://...             # 数据库连接URL
DB_POOL_SIZE=20                           # 连接池大小
DB_MAX_OVERFLOW=30                        # 最大溢出连接数
```

#### Redis配置
```bash
REDIS_PASSWORD=redis123                   # Redis密码
REDIS_URL=redis://...                     # Redis连接URL
CACHE_TTL=3600                           # 缓存过期时间（秒）
```

#### 应用配置
```bash
SECRET_KEY=your-secret-key               # JWT密钥
DEBUG=false                              # 调试模式
ENVIRONMENT=production                   # 环境标识
LOG_LEVEL=INFO                          # 日志级别
```

#### AI配置
```bash
OPENAI_API_KEY=sk-...                   # OpenAI API密钥
OPENAI_MODEL=gpt-3.5-turbo             # 默认模型
OPENAI_TEMPERATURE=0.7                  # 温度参数
OPENAI_MAX_TOKENS=150                   # 最大Token数
```

### Docker Compose配置

#### 服务配置
- **postgres**: PostgreSQL数据库
- **redis**: Redis缓存
- **app**: 主应用服务
- **nginx**: 反向代理
- **prometheus**: 监控数据收集
- **grafana**: 监控面板

#### 数据卷
- **postgres_data**: 数据库数据
- **redis_data**: Redis数据
- **app_logs**: 应用日志
- **nginx_logs**: Nginx日志

## 📊 监控和维护

### 监控面板
- **Grafana**: http://localhost:3001
  - 用户名: admin
  - 密码: 在环境变量中设置

- **Prometheus**: http://localhost:9090
  - 监控指标收集

### 健康检查
```bash
# 检查服务状态
curl http://localhost:8000/health

# 详细健康检查
curl http://localhost:8000/health/detailed

# 系统指标
curl -H "Authorization: Bearer your_token" http://localhost:8000/metrics
```

### 日志管理
```bash
# 查看应用日志
docker-compose logs -f app

# 查看数据库日志
docker-compose logs -f postgres

# 查看所有服务日志
docker-compose logs -f
```

### 备份和恢复
```bash
# 备份数据
./scripts/deploy.sh backup

# 恢复数据
./scripts/deploy.sh restore backup_directory

# 手动备份数据库
docker-compose exec postgres pg_dump -U postgres xiaohongshu_auto_reply > backup.sql
```

### 更新部署
```bash
# 拉取最新代码
git pull origin main

# 重新构建和部署
./scripts/deploy.sh deploy production

# 或者手动更新
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

## 🔧 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查Docker状态
sudo systemctl status docker

# 检查端口占用
sudo netstat -tlnp | grep :8000

# 查看错误日志
docker-compose logs app
```

#### 2. 数据库连接失败
```bash
# 检查数据库状态
docker-compose exec postgres pg_isready -U postgres

# 测试连接
docker-compose exec app python -c "
from app.models.database import engine
print('Database connection:', engine.connect())
"
```

#### 3. Redis连接失败
```bash
# 检查Redis状态
docker-compose exec redis redis-cli ping

# 测试连接
docker-compose exec app python -c "
from app.services.cache_service import cache_service
print('Cache test:', cache_service.set('test', 'ok'))
"
```

#### 4. AI服务错误
```bash
# 检查API密钥配置
echo $OPENAI_API_KEY

# 测试API连接
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models
```

### 性能优化

#### 1. 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_comments_created_at ON comments(created_at);
CREATE INDEX idx_comments_reply_status ON comments(reply_status);
CREATE INDEX idx_ai_replies_user_id ON ai_replies(user_id);
```

#### 2. 缓存优化
```bash
# 增加Redis内存
# 在docker-compose.yml中添加：
# command: redis-server --maxmemory 512mb --maxmemory-policy allkeys-lru
```

#### 3. 应用优化
```bash
# 增加工作进程数
# 在Dockerfile中修改：
# CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
```

## 🔒 安全建议

### 1. 密码安全
- 使用强密码（至少16位，包含大小写字母、数字、特殊字符）
- 定期更换密码
- 不要在代码中硬编码密码

### 2. 网络安全
- 使用HTTPS加密传输
- 配置防火墙规则
- 限制不必要的端口访问

### 3. 数据安全
- 定期备份数据
- 加密敏感数据
- 限制数据库访问权限

### 4. 应用安全
- 及时更新依赖包
- 启用请求限流
- 配置CORS策略

### 5. 监控安全
- 启用访问日志
- 监控异常访问
- 设置告警机制

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看日志文件
2. 检查配置文件
3. 参考故障排除章节
4. 联系技术支持团队

**联系方式**:
- 邮箱: <EMAIL>
- 文档: https://docs.xiaohongshu-auto-reply.com
- 社区: https://community.xiaohongshu-auto-reply.com

---

**祝您部署顺利！**
