"""
小红书账号相关的Pydantic模型
"""
from typing import Optional
from pydantic import BaseModel, validator
from datetime import datetime
import re


class XiaohongshuAccountBase(BaseModel):
    """小红书账号基础模型"""
    account_name: str
    account_id: Optional[str] = None
    login_phone: Optional[str] = None
    login_email: Optional[str] = None
    
    @validator('account_name')
    def validate_account_name(cls, v):
        if len(v) < 1 or len(v) > 100:
            raise ValueError('账号昵称长度必须在1-100个字符之间')
        return v.strip()
    
    @validator('login_phone')
    def validate_phone(cls, v):
        if v is not None:
            # 简单的手机号验证
            if not re.match(r'^1[3-9]\d{9}$', v):
                raise ValueError('请输入有效的手机号')
        return v
    
    @validator('account_id')
    def validate_account_id(cls, v):
        if v is not None and len(v.strip()) == 0:
            return None
        return v


class XiaohongshuAccountCreate(XiaohongshuAccountBase):
    """创建小红书账号模型"""
    pass


class XiaohongshuAccountUpdate(BaseModel):
    """更新小红书账号模型"""
    account_name: Optional[str] = None
    account_id: Optional[str] = None
    login_phone: Optional[str] = None
    login_email: Optional[str] = None
    is_active: Optional[bool] = None
    
    @validator('account_name')
    def validate_account_name(cls, v):
        if v is not None:
            if len(v) < 1 or len(v) > 100:
                raise ValueError('账号昵称长度必须在1-100个字符之间')
            return v.strip()
        return v
    
    @validator('login_phone')
    def validate_phone(cls, v):
        if v is not None and v.strip():
            if not re.match(r'^1[3-9]\d{9}$', v):
                raise ValueError('请输入有效的手机号')
        return v


class XiaohongshuAccountResponse(XiaohongshuAccountBase):
    """小红书账号响应模型"""
    id: int
    user_id: int
    is_active: bool
    last_login: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class XiaohongshuAccountLogin(BaseModel):
    """小红书账号登录模型"""
    account_id: int
    login_type: str = "manual"  # manual, auto
    
    @validator('login_type')
    def validate_login_type(cls, v):
        if v not in ['manual', 'auto']:
            raise ValueError('登录类型必须是 manual 或 auto')
        return v


class XiaohongshuAccountStatus(BaseModel):
    """小红书账号状态模型"""
    account_id: int
    is_logged_in: bool
    last_check: datetime
    error_message: Optional[str] = None


class CookieData(BaseModel):
    """Cookie数据模型"""
    cookies: str
    session_data: Optional[str] = None
    
    @validator('cookies')
    def validate_cookies(cls, v):
        if len(v.strip()) == 0:
            raise ValueError('Cookies不能为空')
        return v


class XiaohongshuAccountStats(BaseModel):
    """小红书账号统计模型"""
    account_id: int
    notes_count: int = 0
    comments_count: int = 0
    replies_count: int = 0
    last_activity: Optional[datetime] = None
