# 🤝 小红书自动回复系统 - 贡献指南

感谢您对小红书自动回复系统的关注！我们欢迎所有形式的贡献，包括但不限于代码、文档、测试、反馈和建议。

## 📖 目录

1. [贡献方式](#贡献方式)
2. [开发环境设置](#开发环境设置)
3. [代码规范](#代码规范)
4. [提交流程](#提交流程)
5. [问题报告](#问题报告)
6. [功能请求](#功能请求)
7. [代码审查](#代码审查)
8. [社区准则](#社区准则)

## 🌟 贡献方式

### 代码贡献
- **🐛 Bug修复** - 修复已知问题和缺陷
- **✨ 新功能** - 开发新的功能特性
- **⚡ 性能优化** - 提升系统性能和效率
- **🔧 重构** - 改进代码结构和可维护性
- **🧪 测试** - 编写和改进测试用例

### 文档贡献
- **📚 技术文档** - 改进技术文档和API文档
- **📖 用户手册** - 完善用户使用指南
- **🎥 教程** - 创建视频教程和示例
- **🌐 翻译** - 翻译文档到其他语言

### 其他贡献
- **🐛 问题报告** - 报告Bug和问题
- **💡 功能建议** - 提出新功能想法
- **🎨 UI/UX设计** - 改进用户界面和体验
- **📊 测试** - 参与功能测试和反馈

## 🛠️ 开发环境设置

### 前置要求

#### 必需软件
- **Git** 2.30+
- **Docker** 20.10+ 和 **Docker Compose** 2.0+
- **Node.js** 18+ 和 **npm** 8+
- **Python** 3.11+ 和 **pip** 23+

#### 推荐工具
- **VS Code** - 主要开发IDE
- **Git GUI工具** - SourceTree、GitKraken等
- **API测试工具** - Postman、Insomnia等

### 环境搭建

#### 1. 克隆项目
```bash
# 克隆主仓库
git clone https://github.com/your-username/xiaohongshu-auto-reply.git
cd xiaohongshu-auto-reply

# 添加上游仓库
git remote add upstream https://github.com/original-owner/xiaohongshu-auto-reply.git
```

#### 2. 后端环境
```bash
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置必要的配置
```

#### 3. 前端环境
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

#### 4. 数据库设置
```bash
# 使用Docker启动数据库
docker-compose up -d postgres redis

# 运行数据库迁移
cd backend
alembic upgrade head
```

#### 5. 验证环境
```bash
# 启动后端服务
cd backend
uvicorn app.main:app --reload

# 访问API文档
# http://localhost:8000/docs

# 访问前端应用
# http://localhost:3000
```

## 📝 代码规范

### Python代码规范

#### 代码风格
- 遵循 **PEP 8** 代码风格指南
- 使用 **Black** 进行代码格式化
- 使用 **isort** 进行导入排序
- 使用 **flake8** 进行代码检查

#### 命名规范
```python
# 变量和函数：snake_case
user_name = "example"
def get_user_info():
    pass

# 类名：PascalCase
class UserService:
    pass

# 常量：UPPER_SNAKE_CASE
MAX_RETRY_COUNT = 3

# 私有成员：前缀下划线
class Example:
    def __init__(self):
        self._private_var = None
        self.__very_private = None
```

#### 类型注解
```python
from typing import List, Optional, Dict, Any

def process_comments(
    comments: List[Dict[str, Any]], 
    user_id: int
) -> Optional[str]:
    """处理留言数据"""
    pass
```

#### 文档字符串
```python
def generate_ai_reply(comment: str, context: Dict[str, Any]) -> str:
    """
    生成AI回复内容
    
    Args:
        comment: 用户留言内容
        context: 上下文信息，包含笔记标题等
        
    Returns:
        生成的回复内容
        
    Raises:
        ValueError: 当输入参数无效时
        APIError: 当AI服务调用失败时
    """
    pass
```

### JavaScript/TypeScript代码规范

#### 代码风格
- 遵循 **ESLint** 配置规则
- 使用 **Prettier** 进行代码格式化
- 优先使用 **TypeScript** 进行类型安全

#### 命名规范
```typescript
// 变量和函数：camelCase
const userName = 'example';
function getUserInfo() {}

// 类名和接口：PascalCase
class UserService {}
interface UserData {}

// 常量：UPPER_SNAKE_CASE
const MAX_RETRY_COUNT = 3;

// 组件：PascalCase
const CommentList: React.FC = () => {};
```

#### 类型定义
```typescript
// 接口定义
interface Comment {
  id: number;
  content: string;
  author: string;
  createdAt: Date;
  replyStatus: 'pending' | 'replied' | 'ignored';
}

// 函数类型
type ProcessCommentFn = (comment: Comment) => Promise<string>;
```

### Git提交规范

#### 提交消息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 提交类型
- **feat**: 新功能
- **fix**: Bug修复
- **docs**: 文档更新
- **style**: 代码格式化（不影响功能）
- **refactor**: 代码重构
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动

#### 示例
```
feat(ai): 添加GPT-4模型支持

- 集成OpenAI GPT-4 API
- 添加模型选择配置
- 优化回复质量评估

Closes #123
```

## 🔄 提交流程

### 1. 创建分支
```bash
# 同步主分支
git checkout main
git pull upstream main

# 创建功能分支
git checkout -b feature/your-feature-name
```

### 2. 开发和测试
```bash
# 进行开发工作
# ...

# 运行测试
cd backend
python -m pytest tests/ -v

cd frontend
npm run test

# 代码质量检查
cd backend
black .
isort .
flake8 .

cd frontend
npm run lint
```

### 3. 提交代码
```bash
# 添加文件
git add .

# 提交代码
git commit -m "feat(ai): 添加GPT-4模型支持"

# 推送分支
git push origin feature/your-feature-name
```

### 4. 创建Pull Request
1. 在GitHub上创建Pull Request
2. 填写详细的PR描述
3. 关联相关的Issue
4. 等待代码审查

### 5. 代码审查和合并
1. 响应审查意见
2. 修改代码并推送更新
3. 等待维护者合并

## 🐛 问题报告

### 报告Bug

#### 使用Issue模板
请使用GitHub Issue模板报告Bug，包含以下信息：

1. **问题描述** - 清晰描述遇到的问题
2. **复现步骤** - 详细的复现步骤
3. **期望行为** - 期望的正确行为
4. **实际行为** - 实际发生的错误行为
5. **环境信息** - 操作系统、浏览器、版本等
6. **截图/日志** - 相关的截图或错误日志

#### Bug报告示例
```markdown
## 问题描述
AI回复生成时出现超时错误

## 复现步骤
1. 登录系统
2. 进入留言管理页面
3. 选择一条留言
4. 点击"AI回复"按钮
5. 等待30秒后出现超时错误

## 期望行为
应该在5秒内生成AI回复

## 实际行为
30秒后显示"请求超时"错误

## 环境信息
- OS: Windows 11
- Browser: Chrome 120.0
- Version: 1.0.0

## 错误日志
```
ERROR: OpenAI API timeout after 30s
```

### 安全问题报告

如果发现安全漏洞，请不要在公开Issue中报告，而是：

1. 发送邮件到 <EMAIL>
2. 详细描述安全问题
3. 提供复现步骤（如果安全）
4. 等待我们的回复和修复

## 💡 功能请求

### 提出新功能

#### 使用功能请求模板
1. **功能描述** - 清晰描述想要的功能
2. **使用场景** - 说明功能的使用场景
3. **解决问题** - 功能要解决的具体问题
4. **替代方案** - 考虑过的其他解决方案
5. **附加信息** - 其他相关信息

#### 功能请求示例
```markdown
## 功能描述
希望添加批量导入小红书账号的功能

## 使用场景
作为运营人员，我需要管理多个小红书账号，
希望能够通过Excel文件批量导入账号信息，
而不是一个一个手动添加。

## 解决问题
- 提高账号管理效率
- 减少重复操作
- 支持大规模账号管理

## 替代方案
目前只能手动逐个添加账号

## 附加信息
- 支持Excel和CSV格式
- 包含数据验证功能
- 提供导入结果报告
```

## 👀 代码审查

### 审查清单

#### 功能性
- [ ] 功能是否按预期工作
- [ ] 是否有边界情况处理
- [ ] 错误处理是否完善
- [ ] 性能是否可接受

#### 代码质量
- [ ] 代码是否清晰易读
- [ ] 命名是否恰当
- [ ] 是否遵循项目规范
- [ ] 是否有适当的注释

#### 测试
- [ ] 是否有足够的测试覆盖
- [ ] 测试是否有意义
- [ ] 是否测试了边界情况
- [ ] 测试是否能通过

#### 文档
- [ ] 是否更新了相关文档
- [ ] API文档是否准确
- [ ] 变更日志是否更新
- [ ] 用户手册是否需要更新

### 审查礼仪

#### 给出反馈时
- 保持友善和建设性
- 解释为什么需要修改
- 提供具体的改进建议
- 认可好的代码和想法

#### 接收反馈时
- 保持开放的心态
- 认真考虑审查意见
- 及时响应和修改
- 感谢审查者的时间

## 🤝 社区准则

### 行为准则

我们致力于为所有人提供友好、安全和欢迎的环境，无论：
- 性别、性别认同和表达
- 性取向
- 残疾
- 外貌
- 身体大小
- 种族
- 年龄
- 宗教

### 期望行为

- **友善和耐心** - 对新贡献者友善和耐心
- **欢迎多样性** - 欢迎不同背景和观点
- **尊重不同意见** - 尊重不同的观点和经验
- **建设性反馈** - 给出和接受建设性批评
- **关注社区利益** - 关注对社区最有利的事情
- **同理心** - 对其他社区成员表现同理心

### 不可接受行为

- 使用性化的语言或图像
- 人身攻击或政治攻击
- 公开或私下骚扰
- 发布他人的私人信息
- 其他在专业环境中不当的行为

### 执行

如果遇到不当行为，请联系项目维护者：
- 邮箱：<EMAIL>
- 我们会认真对待所有投诉
- 我们会保护举报者的隐私

## 📞 获取帮助

### 联系方式

- **GitHub Issues** - 技术问题和Bug报告
- **GitHub Discussions** - 一般讨论和问题
- **邮箱** - <EMAIL>
- **QQ群** - 123456789
- **微信群** - 扫描二维码加入

### 文档资源

- **用户手册** - [USER_MANUAL.md](docs/USER_MANUAL.md)
- **API文档** - [API_DOCUMENTATION.md](docs/API_DOCUMENTATION.md)
- **部署指南** - [DEPLOYMENT_GUIDE.md](docs/DEPLOYMENT_GUIDE.md)
- **开发指南** - 本文档

---

**感谢您为小红书自动回复系统做出贡献！您的参与让这个项目变得更好。** 🙏
