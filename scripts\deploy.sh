#!/bin/bash

# 小红书自动回复系统部署脚本
# 使用方法: ./scripts/deploy.sh [environment]
# 环境: development, staging, production

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 设置环境
setup_environment() {
    local env=${1:-development}
    log_info "设置环境: $env"
    
    # 复制环境配置文件
    if [ -f ".env.$env" ]; then
        cp ".env.$env" ".env"
        log_success "环境配置文件已复制"
    else
        log_warning "环境配置文件 .env.$env 不存在，使用默认配置"
        if [ ! -f ".env" ]; then
            cp ".env.example" ".env"
        fi
    fi
    
    # 设置环境变量
    export ENVIRONMENT=$env
    export COMPOSE_PROJECT_NAME="xiaohongshu_${env}"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建应用镜像
    docker-compose build --no-cache
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    local env=${1:-development}
    log_info "启动服务..."
    
    if [ "$env" = "production" ]; then
        # 生产环境启动所有服务包括监控
        docker-compose --profile monitoring up -d
    else
        # 开发环境只启动基础服务
        docker-compose up -d postgres redis app nginx
    fi
    
    log_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待数据库
    log_info "等待数据库启动..."
    until docker-compose exec postgres pg_isready -U postgres; do
        sleep 2
    done
    
    # 等待Redis
    log_info "等待Redis启动..."
    until docker-compose exec redis redis-cli ping; do
        sleep 2
    done
    
    # 等待应用
    log_info "等待应用启动..."
    until curl -f http://localhost:8000/health; do
        sleep 5
    done
    
    log_success "所有服务已就绪"
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    docker-compose exec app alembic upgrade head
    
    log_success "数据库迁移完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查应用健康状态
    if curl -f http://localhost:8000/health/detailed; then
        log_success "应用健康检查通过"
    else
        log_error "应用健康检查失败"
        return 1
    fi
    
    # 检查数据库连接
    if docker-compose exec app python -c "
from app.models.database import engine
from sqlalchemy import text
with engine.connect() as conn:
    conn.execute(text('SELECT 1'))
print('Database connection OK')
"; then
        log_success "数据库连接检查通过"
    else
        log_error "数据库连接检查失败"
        return 1
    fi
    
    log_success "健康检查通过"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose down
    log_success "服务已停止"
}

# 清理资源
cleanup() {
    log_info "清理资源..."
    
    # 清理未使用的镜像
    docker image prune -f
    
    # 清理未使用的卷（谨慎使用）
    # docker volume prune -f
    
    log_success "资源清理完成"
}

# 备份数据
backup_data() {
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    log_info "备份数据到 $backup_dir..."
    
    mkdir -p "$backup_dir"
    
    # 备份数据库
    docker-compose exec postgres pg_dump -U postgres xiaohongshu_auto_reply > "$backup_dir/database.sql"
    
    # 备份上传文件（如果有）
    if [ -d "uploads" ]; then
        cp -r uploads "$backup_dir/"
    fi
    
    log_success "数据备份完成: $backup_dir"
}

# 恢复数据
restore_data() {
    local backup_dir=$1
    if [ -z "$backup_dir" ]; then
        log_error "请指定备份目录"
        exit 1
    fi
    
    log_info "从 $backup_dir 恢复数据..."
    
    # 恢复数据库
    if [ -f "$backup_dir/database.sql" ]; then
        docker-compose exec -T postgres psql -U postgres xiaohongshu_auto_reply < "$backup_dir/database.sql"
        log_success "数据库恢复完成"
    fi
    
    # 恢复上传文件
    if [ -d "$backup_dir/uploads" ]; then
        cp -r "$backup_dir/uploads" .
        log_success "文件恢复完成"
    fi
}

# 查看日志
view_logs() {
    local service=${1:-app}
    log_info "查看 $service 服务日志..."
    docker-compose logs -f "$service"
}

# 显示帮助信息
show_help() {
    echo "小红书自动回复系统部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [命令] [参数]"
    echo ""
    echo "命令:"
    echo "  deploy [env]     部署系统 (env: development|staging|production)"
    echo "  start [env]      启动服务"
    echo "  stop             停止服务"
    echo "  restart [env]    重启服务"
    echo "  status           查看服务状态"
    echo "  logs [service]   查看日志"
    echo "  health           健康检查"
    echo "  backup           备份数据"
    echo "  restore [dir]    恢复数据"
    echo "  cleanup          清理资源"
    echo "  help             显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 deploy production"
    echo "  $0 logs app"
    echo "  $0 backup"
}

# 主函数
main() {
    local command=${1:-deploy}
    local env=${2:-development}
    
    case $command in
        deploy)
            check_dependencies
            setup_environment "$env"
            build_images
            start_services "$env"
            wait_for_services
            run_migrations
            health_check
            log_success "部署完成！"
            log_info "应用访问地址: http://localhost"
            log_info "API文档地址: http://localhost:8000/docs"
            if [ "$env" = "production" ]; then
                log_info "监控面板: http://localhost:3001 (Grafana)"
                log_info "指标收集: http://localhost:9090 (Prometheus)"
            fi
            ;;
        start)
            setup_environment "$env"
            start_services "$env"
            ;;
        stop)
            stop_services
            ;;
        restart)
            stop_services
            setup_environment "$env"
            start_services "$env"
            ;;
        status)
            docker-compose ps
            ;;
        logs)
            view_logs "$env"
            ;;
        health)
            health_check
            ;;
        backup)
            backup_data
            ;;
        restore)
            restore_data "$env"
            ;;
        cleanup)
            cleanup
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
