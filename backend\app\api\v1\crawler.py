"""
爬虫管理API路由
"""
from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import json

from ...models import get_db, User, CrawlerTask, TaskStatus, TaskType
from ...services.crawler_service import CrawlerService
from ...services.crawler_task_service import CrawlerTaskService
from ...api.schemas.comment import CrawlRequest
from ...api.responses import success_response
from ...api.deps import get_current_active_user
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/crawl", response_model=dict)
async def start_crawl(
    crawl_request: CrawlRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """启动爬虫抓取"""
    try:
        crawler_service = CrawlerService(db)
        
        # 如果指定了笔记ID，验证权限
        if crawl_request.note_ids:
            from ...services.note_service import NoteService
            note_service = NoteService(db)
            
            for note_id in crawl_request.note_ids:
                note = note_service.get_note_by_id(note_id, current_user.id)
                if not note:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"笔记 {note_id} 不存在或无权限访问"
                    )
        
        # 启动后台抓取任务
        background_tasks.add_task(
            _background_crawl_task,
            crawler_service,
            crawl_request.note_ids,
            crawl_request.force_crawl
        )
        
        return success_response(
            data={
                "message": "抓取任务已启动",
                "note_ids": crawl_request.note_ids,
                "force_crawl": crawl_request.force_crawl
            },
            message="爬虫抓取任务启动成功"
        )
        
    except HTTPException as e:
        logger.warning(f"启动爬虫抓取失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"启动爬虫抓取时发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="启动爬虫抓取失败，请稍后重试"
        )


@router.get("/test", response_model=dict)
def test_endpoint(
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """简单测试端点"""
    return {
        "success": True,
        "message": "测试端点工作正常",
        "data": {
            "user_id": current_user.id,
            "username": current_user.username,
            "timestamp": "2025-07-18 19:30:00"
        }
    }

@router.get("/test-db", response_model=dict)
def test_db_endpoint(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """测试数据库连接端点"""
    try:
        # 简单的数据库查询
        user_count = db.query(User).count()
        return {
            "success": True,
            "message": "数据库连接测试成功",
            "data": {
                "user_id": current_user.id,
                "username": current_user.username,
                "user_count": user_count,
                "timestamp": "2025-07-18 19:30:00"
            }
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"数据库连接测试失败: {str(e)}",
            "data": None
        }

@router.get("/test-response", response_model=dict)
def test_response_endpoint(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """测试success_response函数"""
    try:
        user_count = db.query(User).count()
        test_data = {
            "user_id": current_user.id,
            "username": current_user.username,
            "user_count": user_count,
            "timestamp": "2025-07-18 19:30:00"
        }

        return success_response(
            data=test_data,
            message="success_response函数测试成功"
        ).model_dump()

    except Exception as e:
        return {
            "success": False,
            "message": f"success_response函数测试失败: {str(e)}",
            "data": None
        }

@router.get("/status", response_model=dict)
def get_crawl_status(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取爬虫状态"""
    try:
        # 使用真实数据库数据
        task_service = CrawlerTaskService(db)

        # 获取统计信息
        stats = task_service.get_task_statistics(current_user.id)

        # 获取最近的任务列表
        recent_tasks = task_service.get_tasks_by_user(
            user_id=current_user.id,
            limit=10,
            offset=0
        )

        # 格式化任务数据
        tasks_data = []
        for task in recent_tasks:
            # 获取关联的笔记和账号信息
            note_title = "批量任务"
            account_name = "未知账号"

            if task.note:
                note_title = task.note.title or f"笔记 {task.note.id}"

            if task.account:
                account_name = task.account.account_name or f"账号 {task.account.id}"

            # 格式化时间
            start_time = task.start_time.strftime('%Y-%m-%d %H:%M:%S') if task.start_time else None
            last_update = task.last_update.strftime('%Y-%m-%d %H:%M:%S') if task.last_update else None

            tasks_data.append({
                'id': task.id,
                'task_name': task.task_name,
                'note_title': note_title,
                'account_name': account_name,
                'status': task.status.value,
                'progress': task.progress or 0,
                'start_time': start_time,
                'last_update': last_update,
                'comments_found': task.comments_found or 0,
                'new_comments': task.new_comments or 0,
                'errors': task.error_count or 0,
                'success_rate': task.success_rate,
                'task_type': task.task_type.value if task.task_type else 'manual'
            })

        # 组合返回数据
        status_data = {
            **stats,
            'tasks': tasks_data
        }

        return success_response(
            data=status_data,
            message="获取爬虫状态成功"
        ).model_dump()

    except Exception as e:
        logger.error(f"获取爬虫状态时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取爬虫状态失败"
        )


@router.post("/tasks", response_model=dict)
def create_crawler_task(
    task_name: str,
    account_id: int,
    note_id: Optional[int] = None,
    task_type: str = "manual",
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """创建爬虫任务"""
    try:
        task_service = CrawlerTaskService(db)

        # 验证任务类型
        try:
            task_type_enum = TaskType(task_type)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的任务类型: {task_type}"
            )

        # 创建任务
        task = task_service.create_task(
            user_id=current_user.id,
            account_id=account_id,
            task_name=task_name,
            task_type=task_type_enum,
            note_id=note_id
        )

        return success_response(
            data={
                "task_id": task.id,
                "task_name": task.task_name,
                "status": task.status.value,
                "created_at": task.created_at.strftime('%Y-%m-%d %H:%M:%S')
            },
            message="创建爬虫任务成功"
        ).model_dump()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建爬虫任务时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建爬虫任务失败"
        )


@router.delete("/tasks/{task_id}", response_model=dict)
def delete_crawler_task(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """删除爬虫任务"""
    try:
        task_service = CrawlerTaskService(db)

        success = task_service.delete_task(task_id, current_user.id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在或无法删除"
            )

        return success_response(
            data={"task_id": task_id},
            message="删除爬虫任务成功"
        ).model_dump()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除爬虫任务时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除爬虫任务失败"
        )


@router.post("/tasks/clear", response_model=dict)
def clear_completed_tasks(
    days_old: int = 7,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """清理已完成的旧任务"""
    try:
        task_service = CrawlerTaskService(db)

        deleted_count = task_service.clear_completed_tasks(current_user.id, days_old)

        return success_response(
            data={"deleted_count": deleted_count},
            message=f"清理完成，删除了 {deleted_count} 个旧任务"
        ).model_dump()

    except Exception as e:
        logger.error(f"清理任务时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="清理任务失败"
        )


@router.get("/tasks/{task_id}/logs", response_model=dict)
def get_task_logs(
    task_id: int,
    limit: int = 50,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取任务日志"""
    try:
        task_service = CrawlerTaskService(db)

        # 验证任务是否属于当前用户
        task = task_service.get_task_by_id(task_id, current_user.id)
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )

        logs = task_service.get_task_logs(task_id=task_id, limit=limit)

        logs_data = []
        for log in logs:
            logs_data.append({
                "id": log.id,
                "level": log.level,
                "message": log.message,
                "details": json.loads(log.details) if log.details else None,
                "url": log.url,
                "response_time": log.response_time,
                "created_at": log.created_at.strftime('%Y-%m-%d %H:%M:%S')
            })

        return success_response(
            data={"logs": logs_data},
            message="获取任务日志成功"
        ).model_dump()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务日志时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务日志失败"
        )


@router.post("/test", response_model=dict)
async def test_crawl(
    note_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """测试单个笔记的爬虫抓取"""
    try:
        from ...services.note_service import NoteService
        
        # 验证笔记权限
        note_service = NoteService(db)
        note = note_service.get_note_by_id(note_id, current_user.id)
        
        if not note:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="笔记不存在或无权限访问"
            )
        
        # 执行测试抓取
        crawler_service = CrawlerService(db)
        results = await crawler_service.crawl_notes([note_id], force_crawl=True)
        
        if results:
            result = results[0]
            return success_response(
                data={
                    "note_id": result.note_id,
                    "success": result.success,
                    "new_comments_count": result.new_comments_count,
                    "error_message": result.error_message,
                    "crawl_time": result.crawl_time.isoformat()
                },
                message="测试抓取完成"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="测试抓取失败"
            )
        
    except HTTPException as e:
        logger.warning(f"测试爬虫抓取失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"测试爬虫抓取时发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="测试爬虫抓取失败，请稍后重试"
        )


@router.get("/logs", response_model=dict)
def get_crawl_logs(
    limit: int = 50,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取爬虫日志"""
    try:
        # 这里可以实现获取爬虫日志的逻辑
        # 目前返回模拟数据
        
        logs = [
            {
                "timestamp": "2024-01-01T10:00:00",
                "level": "INFO",
                "message": "开始抓取笔记: 测试笔记1",
                "note_id": 1
            },
            {
                "timestamp": "2024-01-01T10:01:00",
                "level": "SUCCESS",
                "message": "抓取完成，新增留言 5 条",
                "note_id": 1
            }
        ]
        
        return success_response(
            data=logs,
            message="获取爬虫日志成功"
        )
        
    except Exception as e:
        logger.error(f"获取爬虫日志时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取爬虫日志失败"
        )


@router.post("/schedule/start", response_model=dict)
def start_scheduled_crawling(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """启动定时爬虫任务"""
    try:
        # 这里可以实现启动定时任务的逻辑
        # 例如使用APScheduler或Celery
        
        return success_response(
            data={"status": "started"},
            message="定时爬虫任务启动成功"
        )
        
    except Exception as e:
        logger.error(f"启动定时爬虫任务时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="启动定时爬虫任务失败"
        )


@router.post("/schedule/stop", response_model=dict)
def stop_scheduled_crawling(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """停止定时爬虫任务"""
    try:
        # 这里可以实现停止定时任务的逻辑
        
        return success_response(
            data={"status": "stopped"},
            message="定时爬虫任务停止成功"
        )
        
    except Exception as e:
        logger.error(f"停止定时爬虫任务时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="停止定时爬虫任务失败"
        )


@router.get("/schedule/status", response_model=dict)
def get_schedule_status(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取定时任务状态"""
    try:
        # 这里可以实现获取定时任务状态的逻辑
        
        status_data = {
            "is_running": False,
            "next_run_time": None,
            "last_run_time": None,
            "total_runs": 0,
            "success_runs": 0,
            "failed_runs": 0
        }
        
        return success_response(
            data=status_data,
            message="获取定时任务状态成功"
        )
        
    except Exception as e:
        logger.error(f"获取定时任务状态时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取定时任务状态失败"
        )


async def _background_crawl_task(
    crawler_service: CrawlerService,
    note_ids: List[int] = None,
    force_crawl: bool = False
):
    """后台爬虫抓取任务"""
    try:
        logger.info(f"开始后台抓取任务: note_ids={note_ids}, force_crawl={force_crawl}")
        
        results = await crawler_service.crawl_notes(note_ids, force_crawl)
        
        success_count = sum(1 for r in results if r.success)
        total_count = len(results)
        
        logger.info(f"后台抓取任务完成: 成功 {success_count}/{total_count}")
        
    except Exception as e:
        logger.error(f"后台抓取任务失败: {e}")
