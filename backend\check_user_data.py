#!/usr/bin/env python3
"""
检查用户数据情况
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.database import SessionLocal
from app.models.user import User, <PERSON><PERSON>shuAccount
from app.models.note import Note
from sqlalchemy import text

def check_user_data():
    """检查用户数据情况"""
    db = SessionLocal()
    
    try:
        # 查找testuser2
        test_user = db.query(User).filter(User.username == "testuser2").first()
        if not test_user:
            print("❌ 找不到testuser2用户")
            return
        
        print(f"✅ 找到testuser2用户，ID: {test_user.id}")
        
        # 查找用户的账号
        accounts = db.query(XiaohongshuAccount).filter(XiaohongshuAccount.user_id == test_user.id).all()
        print(f"📱 用户的小红书账号数量: {len(accounts)}")
        
        for account in accounts:
            print(f"   - 账号ID: {account.id}, 名称: {account.account_name}")
            
            # 查找该账号的笔记
            notes = db.query(Note).filter(Note.account_id == account.id).all()
            print(f"     笔记数量: {len(notes)}")
            
            for note in notes:
                print(f"       - 笔记ID: {note.id}, 标题: {note.title}")
        
        # 查找所有笔记
        all_notes = db.query(Note).all()
        print(f"\n📝 数据库中所有笔记数量: {len(all_notes)}")
        
        for note in all_notes:
            print(f"   - 笔记ID: {note.id}, 账号ID: {note.account_id}, 标题: {note.title}")
        
        # 查找所有留言
        comments_result = db.execute(text("SELECT COUNT(*) FROM comments"))
        comment_count = comments_result.scalar()
        print(f"\n💬 数据库中所有留言数量: {comment_count}")
        
        if comment_count > 0:
            comments_result = db.execute(text("""
                SELECT c.id, c.note_id, c.user_name, c.content, c.status, n.title as note_title
                FROM comments c
                LEFT JOIN notes n ON c.note_id = n.id
                ORDER BY c.created_at DESC
                LIMIT 10
            """))
            
            comments = comments_result.fetchall()
            print(f"\n💬 最近的留言:")
            for comment in comments:
                print(f"   - ID: {comment.id}, 笔记: {comment.note_title}, 用户: {comment.user_name}")
                print(f"     内容: {comment.content[:50]}...")
        
    except Exception as e:
        print(f"❌ 检查数据失败: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("🔍 检查用户数据情况")
    print("=" * 50)
    check_user_data()
