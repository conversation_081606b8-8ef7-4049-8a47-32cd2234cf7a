#!/usr/bin/env python3
"""
创建爬虫任务示例数据
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.database import SessionLocal
from app.models.user import User, XiaohongshuAccount
from app.models.note import Note
from app.models.crawler_task import CrawlerTask, CrawlerTaskLog, TaskStatus, TaskType
from datetime import datetime, timedelta
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_sample_data():
    """创建示例数据"""
    try:
        logger.info("开始创建爬虫任务示例数据...")
        
        db = SessionLocal()
        try:
            # 获取第一个用户和账号
            user = db.query(User).first()
            if not user:
                logger.warning("没有找到用户，跳过创建示例数据")
                return True
            
            account = db.query(XiaohongshuAccount).filter(
                XiaohongshuAccount.user_id == user.id
            ).first()
            if not account:
                logger.warning("没有找到小红书账号，跳过创建示例数据")
                return True
            
            # 获取第一个笔记
            note = db.query(Note).filter(Note.account_id == account.id).first()
            
            # 创建示例任务
            now = datetime.utcnow()
            
            # 任务1：运行中的任务
            task1 = CrawlerTask(
                user_id=user.id,
                account_id=account.id,
                note_id=note.id if note else None,
                task_name="美妆分享笔记爬虫",
                task_type="MANUAL",
                status="RUNNING",
                progress=75,
                start_time=now - timedelta(minutes=30),
                last_update=now - timedelta(minutes=5),
                total_items=25,
                processed_items=20,
                success_items=18,
                failed_items=2,
                comments_found=45,
                new_comments=12,
                error_count=1,
                avg_response_time=1.2,
                total_requests=20,
                created_at=now - timedelta(hours=1)
            )

            # 任务2：已完成的任务
            task2 = CrawlerTask(
                user_id=user.id,
                account_id=account.id,
                note_id=note.id if note else None,
                task_name="时尚穿搭笔记爬虫",
                task_type="SCHEDULED",
                status="COMPLETED",
                progress=100,
                start_time=now - timedelta(hours=2),
                end_time=now - timedelta(hours=1, minutes=30),
                last_update=now - timedelta(hours=1, minutes=30),
                total_items=30,
                processed_items=30,
                success_items=28,
                failed_items=2,
                comments_found=67,
                new_comments=23,
                error_count=2,
                avg_response_time=0.8,
                total_requests=30,
                created_at=now - timedelta(hours=3)
            )

            # 任务3：失败的任务
            task3 = CrawlerTask(
                user_id=user.id,
                account_id=account.id,
                note_id=None,
                task_name="批量笔记爬虫任务",
                task_type="AUTO",
                status="FAILED",
                progress=35,
                start_time=now - timedelta(hours=4),
                end_time=now - timedelta(hours=3, minutes=45),
                last_update=now - timedelta(hours=3, minutes=45),
                total_items=50,
                processed_items=15,
                success_items=10,
                failed_items=5,
                comments_found=20,
                new_comments=8,
                error_count=5,
                error_message="网络连接超时",
                avg_response_time=2.5,
                total_requests=15,
                created_at=now - timedelta(hours=5)
            )

            # 任务4：等待中的任务
            task4 = CrawlerTask(
                user_id=user.id,
                account_id=account.id,
                note_id=note.id if note else None,
                task_name="生活分享笔记爬虫",
                task_type="MANUAL",
                status="PENDING",
                progress=0,
                total_items=0,
                processed_items=0,
                success_items=0,
                failed_items=0,
                comments_found=0,
                new_comments=0,
                error_count=0,
                avg_response_time=0.0,
                total_requests=0,
                created_at=now - timedelta(minutes=10)
            )
            
            # 添加任务到数据库
            tasks = [task1, task2, task3, task4]
            for task in tasks:
                db.add(task)
            
            db.commit()
            
            # 刷新任务以获取ID
            for task in tasks:
                db.refresh(task)
            
            # 为每个任务创建一些日志
            logs_data = [
                # 任务1的日志
                (task1.id, "info", "任务开始执行", {"target": "美妆分享笔记"}),
                (task1.id, "info", "开始爬取评论数据", {"url": "https://xiaohongshu.com/note/123"}),
                (task1.id, "warning", "遇到验证码，正在处理", {"retry_count": 1}),
                (task1.id, "info", "成功获取18条评论", {"new_comments": 12}),
                
                # 任务2的日志
                (task2.id, "info", "任务开始执行", {"target": "时尚穿搭笔记"}),
                (task2.id, "info", "爬取完成", {"total_comments": 67, "new_comments": 23}),
                (task2.id, "info", "任务执行成功", {"duration": "30分钟"}),
                
                # 任务3的日志
                (task3.id, "info", "任务开始执行", {"target": "批量笔记"}),
                (task3.id, "error", "网络连接超时", {"url": "https://xiaohongshu.com/note/456"}),
                (task3.id, "error", "任务执行失败", {"error": "连续超时3次"}),
                
                # 任务4的日志
                (task4.id, "info", "任务创建成功", {"task_type": "manual"}),
            ]
            
            for task_id, level, message, details in logs_data:
                log = CrawlerTaskLog(
                    task_id=task_id,
                    level=level,
                    message=message,
                    details=str(details),
                    created_at=now - timedelta(minutes=30)
                )
                db.add(log)
            
            db.commit()
            
            logger.info(f"✅ 创建了 {len(tasks)} 个示例任务和 {len(logs_data)} 条日志")
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ 创建示例数据失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 创建爬虫任务示例数据")
    print("=" * 50)
    
    if create_sample_data():
        print("✅ 示例数据创建成功！")
        print("现在可以测试爬虫管理页面的真实数据对接")
    else:
        print("❌ 示例数据创建失败")


if __name__ == "__main__":
    main()
