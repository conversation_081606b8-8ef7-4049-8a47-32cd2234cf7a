/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  font-size: 14px;
  line-height: 1.5715;
  color: var(--theme-text-primary, rgba(0, 0, 0, 0.85));
  background-color: var(--theme-body-bg, #f0f2f5);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
}

.app {
  height: 100%;
  min-height: 100vh;
}

/* Ant Design 组件样式覆盖 */
.ant-layout {
  background: var(--theme-layout-bg, #f0f2f5);
}

.ant-card {
  border-radius: 8px;
  box-shadow: var(--theme-card-shadow, 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24));
  background: var(--theme-component-bg);
  border-color: var(--theme-border-color);
}

.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background: #40a9ff;
  border-color: #40a9ff;
}

.ant-input,
.ant-input-affix-wrapper {
  border-radius: 6px;
}

.ant-select-selector {
  border-radius: 6px !important;
}

.ant-table {
  border-radius: 8px;
}

.ant-modal {
  border-radius: 8px;
}

.ant-drawer .ant-drawer-content {
  border-radius: 8px 0 0 8px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式工具类 */
.mobile-hidden {
  display: block;
}

.mobile-only {
  display: none;
}

@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }

  .mobile-only {
    display: block;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
