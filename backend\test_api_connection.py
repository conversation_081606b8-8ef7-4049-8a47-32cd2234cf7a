#!/usr/bin/env python3
"""
测试API连接状态
"""
import requests
import json

# API配置
BASE_URL = "http://localhost:8000/api/v1"

def test_health_check():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ 健康检查成功!")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误：无法连接到后端服务器")
        return False
    except requests.exceptions.Timeout:
        print("❌ 超时错误：请求超时")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_login():
    """测试登录功能"""
    print("\n🔍 测试登录功能...")
    
    try:
        login_data = {
            'username': 'testuser2',
            'password': 'testpass123'
        }
        
        response = requests.post(f'{BASE_URL}/auth/login', data=login_data, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 登录成功!")
            return data['data']['access_token']
        else:
            print(f"❌ 登录失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误：无法连接到后端服务器")
        return None
    except requests.exceptions.Timeout:
        print("❌ 超时错误：请求超时")
        return None
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_notes_api(token):
    """测试笔记API"""
    print("\n🔍 测试笔记API...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{BASE_URL}/notes", headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 笔记API调用成功!")
            print(f"笔记数量: {len(data.get('data', []))}")
            return True
        else:
            print(f"❌ 笔记API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误：无法连接到后端服务器")
        return False
    except requests.exceptions.Timeout:
        print("❌ 超时错误：请求超时")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_cors():
    """测试CORS设置"""
    print("\n🔍 测试CORS设置...")
    
    try:
        # 发送OPTIONS请求测试CORS
        response = requests.options(f"{BASE_URL}/notes", timeout=5)
        print(f"OPTIONS状态码: {response.status_code}")
        
        # 检查CORS头
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
        }
        
        print("CORS头信息:")
        for header, value in cors_headers.items():
            print(f"  {header}: {value}")
        
        if response.status_code == 200:
            print(f"✅ CORS设置正常!")
            return True
        else:
            print(f"❌ CORS设置可能有问题")
            return False
            
    except Exception as e:
        print(f"❌ CORS测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 API连接测试...")
    print("=" * 50)
    
    # 1. 健康检查
    print("1️⃣ 健康检查")
    health_ok = test_health_check()
    
    # 2. 登录测试
    print("\n" + "=" * 50)
    print("2️⃣ 登录测试")
    token = test_login()
    
    # 3. 笔记API测试
    if token:
        print("\n" + "=" * 50)
        print("3️⃣ 笔记API测试")
        notes_ok = test_notes_api(token)
    else:
        notes_ok = False
    
    # 4. CORS测试
    print("\n" + "=" * 50)
    print("4️⃣ CORS测试")
    cors_ok = test_cors()
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 测试结果总结:")
    
    results = {
        "健康检查": health_ok,
        "登录功能": token is not None,
        "笔记API": notes_ok,
        "CORS设置": cors_ok
    }
    
    all_passed = all(results.values())
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    if all_passed:
        print("\n🎉 所有测试通过！API连接正常")
        print("前端应该能够正常连接到后端")
    else:
        print("\n⚠️ 部分测试失败，可能存在连接问题")
        print("建议检查：")
        print("1. 后端服务器是否正常运行")
        print("2. 端口8000是否被占用")
        print("3. 防火墙设置")
        print("4. CORS配置")

if __name__ == "__main__":
    main()
