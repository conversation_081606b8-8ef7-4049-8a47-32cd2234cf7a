﻿import React, { useState, useEffect } from 'react';
import {
  Card, Typography, Form, Input, Button, Select, message, Space, Alert, Row, Col,
  Divider, Tag, Tabs, Spin, Table, Modal, InputNumber, Switch, Statistic, Progress,
  Badge, Tooltip, List, Timeline, Empty
} from 'antd';
import {
  RobotOutlined, SaveOutlined, HomeOutlined, CloudOutlined, SettingOutlined,
  BarChartOutlined, HistoryOutlined, TestTubeOutlined, EditOutlined, PlusOutlined,
  CheckCircleOutlined, ExclamationCircleOutlined, ReloadOutlined, ThunderboltOutlined
} from '@ant-design/icons';
import { aiService } from '../../services/aiService';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

const ConfigPage = () => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('config');

  // 配置状态
  const [config, setConfig] = useState({
    provider: 'ollama',
    ollama_base_url: 'http://************:11434',
    ollama_model: '',
    api_key: '',
    default_model: 'gpt-3.5-turbo',
    temperature: 0.7,
    max_tokens: 150,
    reply_language: 'zh',
    reply_tone: 'friendly',
    include_emoji: true,
    content_filter: true,
  });

  // Ollama相关状态
  const [connectionStatus, setConnectionStatus] = useState(null);
  const [ollamaModels, setOllamaModels] = useState([]);
  const [selectedModel, setSelectedModel] = useState('');
  const [testingConnection, setTestingConnection] = useState(false);
  const [loadingModels, setLoadingModels] = useState(false);

  // 统计数据状态
  const [statistics, setStatistics] = useState(null);
  const [loadingStats, setLoadingStats] = useState(false);

  // 历史记录状态
  const [history, setHistory] = useState([]);
  const [loadingHistory, setLoadingHistory] = useState(false);
  const [historyPagination, setHistoryPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 测试状态
  const [testComment, setTestComment] = useState('这个产品看起来不错，请问有什么优惠吗？');
  const [testResult, setTestResult] = useState(null);
  const [testingReply, setTestingReply] = useState(false);

  // 页面加载时获取配置
  useEffect(() => {
    loadConfig();
  }, []);

  // 加载AI配置
  const loadConfig = async () => {
    try {
      const response = await aiService.getConfig();
      if (response.success && response.data) {
        const configData = response.data;
        setConfig(configData);
        form.setFieldsValue(configData);
        if (configData.ollama_model) {
          setSelectedModel(configData.ollama_model);
        }
      }
    } catch (error) {
      console.error('加载配置失败:', error);
    }
  };

  // 测试Ollama连接
  const testOllamaConnection = async () => {
    const baseUrl = form.getFieldValue('ollama_base_url') || config.ollama_base_url;
    if (!baseUrl) {
      message.error('请先输入Ollama服务地址');
      return;
    }

    setTestingConnection(true);
    try {
      const response = await aiService.testOllamaConnection(baseUrl);
      if (response.success) {
        setConnectionStatus({
          status: 'success',
          message: `连接成功！版本: ${response.data.version}`,
          data: response.data,
        });
        message.success('Ollama连接测试成功');
      } else {
        setConnectionStatus({
          status: 'error',
          message: response.message || '连接失败',
        });
      }
    } catch (error) {
      setConnectionStatus({
        status: 'error',
        message: error.message || '连接测试失败',
      });
      message.error('连接测试失败');
    } finally {
      setTestingConnection(false);
    }
  };

  // 获取Ollama模型列表
  const loadOllamaModels = async () => {
    const baseUrl = form.getFieldValue('ollama_base_url') || config.ollama_base_url;
    if (!baseUrl) {
      message.error('请先输入Ollama服务地址');
      return;
    }

    setLoadingModels(true);
    try {
      const response = await aiService.getOllamaModels(baseUrl);
      if (response.success && response.data.models) {
        setOllamaModels(response.data.models);
        message.success(`获取到 ${response.data.models.length} 个模型`);
      } else {
        message.error('获取模型列表失败');
      }
    } catch (error) {
      message.error('获取模型列表失败');
    } finally {
      setLoadingModels(false);
    }
  };

  // 保存配置
  const handleSave = async (values) => {
    setLoading(true);
    try {
      // 如果是Ollama配置，先保存Ollama特定配置
      if (values.provider === 'ollama' && selectedModel) {
        await aiService.saveOllamaConfig(values.ollama_base_url, selectedModel);
      }

      // 保存通用配置
      const response = await aiService.updateConfig(values);
      if (response.success) {
        setConfig({ ...config, ...values });
        message.success('配置保存成功');
        // 重新加载配置
        await loadConfig();
      } else {
        message.error('保存失败');
      }
    } catch (error) {
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载使用统计
  const loadStatistics = async () => {
    setLoadingStats(true);
    try {
      const response = await aiService.getUsageStats();
      if (response.success && response.data) {
        setStatistics(response.data);
      }
    } catch (error) {
      message.error('加载统计数据失败');
    } finally {
      setLoadingStats(false);
    }
  };

  // 加载回复历史
  const loadHistory = async (page = 1, pageSize = 10, filters = {}) => {
    setLoadingHistory(true);
    try {
      const params = {
        page,
        size: pageSize,
        ...filters,
      };
      const response = await aiService.getAIHistory(params);
      if (response.success && response.data) {
        setHistory(response.data);
        setHistoryPagination({
          current: page,
          pageSize,
          total: response.pagination?.total || 0,
        });
      }
    } catch (error) {
      message.error('加载历史记录失败');
    } finally {
      setLoadingHistory(false);
    }
  };

  // 测试AI回复
  const testAIReply = async () => {
    if (!testComment.trim()) {
      message.error('请输入测试评论内容');
      return;
    }

    setTestingReply(true);
    try {
      const response = await aiService.generateReply(testComment, {
        note_category: '美妆',
        note_title: '测试笔记',
      });

      if (response.success && response.data) {
        setTestResult({
          success: true,
          reply: response.data.reply,
          model: response.data.model,
          confidence_score: response.data.confidence_score,
          tokens_used: response.data.tokens_used,
        });
        message.success('AI回复生成成功');
      } else {
        setTestResult({
          success: false,
          error: response.message || '生成失败',
        });
        message.error('AI回复生成失败');
      }
    } catch (error) {
      setTestResult({
        success: false,
        error: error.message || '生成失败',
      });
      message.error('AI回复生成失败');
    } finally {
      setTestingReply(false);
    }
  };

  // 标签页切换时加载对应数据
  const handleTabChange = (key) => {
    setActiveTab(key);
    switch (key) {
      case 'statistics':
        loadStatistics();
        break;
      case 'history':
        loadHistory();
        break;
      default:
        break;
    }
  };

  return (
    <div>
      <Title level={2}>
        <RobotOutlined style={{ marginRight: 8 }} />
        AI配置管理
      </Title>
      <Paragraph type="secondary">
        配置和管理您的AI回复助手，支持Ollama本地模型和OpenAI云端服务
      </Paragraph>

      {/* 当前配置概览 */}
      {config.provider && (
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="AI提供商"
                value={config.provider === 'ollama' ? 'Ollama' : 'OpenAI'}
                prefix={config.provider === 'ollama' ? <HomeOutlined /> : <CloudOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="当前模型"
                value={config.ollama_model || config.default_model || 'N/A'}
                prefix={<RobotOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="温度参数"
                value={config.temperature || 0.7}
                prefix={<SettingOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="最大Token"
                value={config.max_tokens || 150}
                prefix={<ThunderboltOutlined />}
              />
            </Col>
          </Row>
        </Card>
      )}

      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        {/* AI配置标签页 */}
        <TabPane
          tab={
            <span>
              <SettingOutlined />
              AI配置
            </span>
          }
          key="config"
        >
          {renderConfigTab()}
        </TabPane>

        {/* 使用统计标签页 */}
        <TabPane
          tab={
            <span>
              <BarChartOutlined />
              使用统计
            </span>
          }
          key="statistics"
        >
          {renderStatisticsTab()}
        </TabPane>

        {/* 回复历史标签页 */}
        <TabPane
          tab={
            <span>
              <HistoryOutlined />
              回复历史
            </span>
          }
          key="history"
        >
          {renderHistoryTab()}
        </TabPane>

        {/* 功能测试标签页 */}
        <TabPane
          tab={
            <span>
              <TestTubeOutlined />
              功能测试
            </span>
          }
          key="test"
        >
          {renderTestTab()}
        </TabPane>
      </Tabs>
    </div>
  );

  // 渲染配置标签页
  function renderConfigTab() {
    return (
      <Card title="AI配置" loading={loading}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          initialValues={config}
        >
          <Alert
            message="AI提供商配置"
            description="选择AI服务提供商。推荐使用Ollama本地部署，免费且保护隐私。"
            type="info"
            showIcon
            style={{ marginBottom: 24 }}
          />

          <Form.Item
            label="AI提供商"
            name="provider"
            rules={[{ required: true, message: '请选择AI提供商' }]}
          >
            <Select
              placeholder="选择AI提供商"
              onChange={(value) => setConfig({ ...config, provider: value })}
            >
              <Option value="ollama">
                <Space>
                  <HomeOutlined />
                  Ollama (本地部署)
                  <Tag color="green">推荐</Tag>
                </Space>
              </Option>
              <Option value="openai">
                <Space>
                  <CloudOutlined />
                  OpenAI (云端服务)
                </Space>
              </Option>
            </Select>
          </Form.Item>

          {config.provider === 'ollama' && (
            <>
              <Divider>Ollama配置</Divider>

              <Form.Item
                label="Ollama服务地址"
                name="ollama_base_url"
              >
                <Input placeholder="http://************:11434" />
              </Form.Item>

              <Row gutter={16}>
                <Col span={16}>
                  <Button
                    type="default"
                    onClick={testOllamaConnection}
                    loading={testingConnection}
                    icon={<CheckCircleOutlined />}
                  >
                    测试连接
                  </Button>
                  <Button
                    type="default"
                    onClick={loadOllamaModels}
                    loading={loadingModels}
                    icon={<ReloadOutlined />}
                    style={{ marginLeft: 8 }}
                  >
                    获取模型列表
                  </Button>
                </Col>
              </Row>

              {connectionStatus && (
                <Alert
                  message={connectionStatus.message}
                  type={connectionStatus.status}
                  showIcon
                  style={{ margin: '16px 0' }}
                />
              )}

              {ollamaModels.length > 0 && (
                <Form.Item label="选择模型" style={{ marginTop: 16 }}>
                  <Select
                    placeholder="选择要使用的模型"
                    value={selectedModel}
                    onChange={setSelectedModel}
                    style={{ width: '100%' }}
                  >
                    {ollamaModels.map(model => (
                      <Option key={model.name} value={model.name}>
                        <div>
                          <div style={{ fontWeight: 'bold' }}>{model.name}</div>
                          <div style={{ fontSize: '12px', color: '#666' }}>
                            大小: {model.size_gb} GB | 格式: {model.details?.format || 'N/A'}
                          </div>
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              )}
            </>
          )}

          {config.provider === 'openai' && (
            <>
              <Divider>OpenAI配置</Divider>

              <Form.Item
                label="API密钥"
                name="api_key"
              >
                <Input.Password placeholder="sk-..." />
              </Form.Item>

              <Form.Item
                label="模型"
                name="default_model"
              >
                <Select placeholder="选择模型">
                  <Option value="gpt-3.5-turbo">GPT-3.5 Turbo</Option>
                  <Option value="gpt-4">GPT-4</Option>
                  <Option value="gpt-4-turbo">GPT-4 Turbo</Option>
                </Select>
              </Form.Item>
            </>
          )}

          <Divider>回复参数</Divider>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="温度参数" name="temperature">
                <InputNumber
                  min={0}
                  max={2}
                  step={0.1}
                  placeholder="0.7"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="最大令牌数" name="max_tokens">
                <InputNumber
                  min={1}
                  max={4000}
                  placeholder="150"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="回复语调" name="reply_tone">
                <Select placeholder="选择语调">
                  <Option value="friendly">友好</Option>
                  <Option value="professional">专业</Option>
                  <Option value="casual">随意</Option>
                  <Option value="enthusiastic">热情</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="回复语言" name="reply_language">
                <Select placeholder="选择语言">
                  <Option value="zh">中文</Option>
                  <Option value="en">English</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="包含表情" name="include_emoji" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="内容过滤" name="content_filter" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
              size="large"
            >
              保存配置
            </Button>
          </Form.Item>
        </Form>
      </Card>
    );
  }

  // 渲染统计标签页
  function renderStatisticsTab() {
    return (
      <div>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={24}>
            <Button
              type="primary"
              onClick={loadStatistics}
              loading={loadingStats}
              icon={<ReloadOutlined />}
            >
              刷新统计数据
            </Button>
          </Col>
        </Row>

        {loadingStats ? (
          <Card>
            <Spin size="large" style={{ display: 'block', textAlign: 'center', padding: '50px 0' }} />
          </Card>
        ) : statistics ? (
          <>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="今日请求数"
                    value={statistics.today?.requests || 0}
                    prefix={<ThunderboltOutlined />}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="今日成功率"
                    value={statistics.today?.success_rate || 0}
                    suffix="%"
                    prefix={<CheckCircleOutlined />}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="Token使用量"
                    value={statistics.today?.tokens_used || 0}
                    prefix={<RobotOutlined />}
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="今日成本"
                    value={`¥${statistics.today?.cost || 0}`}
                    prefix={<BarChartOutlined />}
                    valueStyle={{ color: '#fa8c16' }}
                  />
                </Card>
              </Col>
            </Row>

            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={12}>
                <Card title="本月统计">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Statistic
                        title="总请求数"
                        value={statistics.month?.requests || 0}
                        valueStyle={{ color: '#1890ff' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="平均成功率"
                        value={statistics.month?.success_rate || 0}
                        suffix="%"
                        valueStyle={{ color: '#52c41a' }}
                      />
                    </Col>
                  </Row>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="使用限制">
                  <div style={{ marginBottom: 16 }}>
                    <Text>每日限制: {statistics.limits?.daily_limit || 1000}</Text>
                    <br />
                    <Text>已使用: {statistics.limits?.daily_used || 0}</Text>
                  </div>
                  <Progress
                    percent={statistics.limits?.usage_percentage || 0}
                    status={statistics.limits?.usage_percentage > 80 ? 'exception' : 'active'}
                  />
                </Card>
              </Col>
            </Row>

            {statistics.model_performance && (
              <Card title="模型性能对比">
                <List
                  dataSource={statistics.model_performance}
                  renderItem={item => (
                    <List.Item>
                      <List.Item.Meta
                        title={item.name}
                        description={
                          <div>
                            <div>成功率: {item.success_rate}%</div>
                            <div>总请求数: {item.total_requests}</div>
                            <Progress
                              percent={item.success_rate}
                              size="small"
                              style={{ marginTop: 8 }}
                            />
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            )}
          </>
        ) : (
          <Card>
            <Empty description="暂无统计数据" />
          </Card>
        )}
      </div>
    );
  }

  // 渲染历史标签页
  function renderHistoryTab() {
    const columns = [
      {
        title: '时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 150,
        render: (text) => new Date(text).toLocaleString(),
      },
      {
        title: '原评论',
        dataIndex: 'comment_content',
        key: 'comment_content',
        ellipsis: true,
        width: 200,
      },
      {
        title: 'AI回复',
        dataIndex: 'generated_reply',
        key: 'generated_reply',
        ellipsis: true,
        width: 250,
      },
      {
        title: '模型',
        dataIndex: 'model_used',
        key: 'model_used',
        width: 120,
        render: (text) => (
          <Tag color={text?.includes('ollama') ? 'blue' : 'green'}>
            {text || 'Unknown'}
          </Tag>
        ),
      },
      {
        title: 'Token',
        dataIndex: 'tokens_used',
        key: 'tokens_used',
        width: 80,
        render: (text) => text || 'N/A',
      },
    ];

    return (
      <div>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={24}>
            <Button
              type="primary"
              onClick={() => loadHistory()}
              loading={loadingHistory}
              icon={<ReloadOutlined />}
            >
              刷新历史记录
            </Button>
          </Col>
        </Row>

        <Card title="AI回复历史">
          <Table
            columns={columns}
            dataSource={history}
            rowKey="id"
            loading={loadingHistory}
            pagination={{
              ...historyPagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
              onChange: (page, pageSize) => loadHistory(page, pageSize),
            }}
          />
        </Card>
      </div>
    );
  }

  // 渲染测试标签页
  function renderTestTab() {
    return (
      <Card title="AI回复测试">
        <Row gutter={16}>
          <Col span={16}>
            <Form.Item label="测试评论内容">
              <TextArea
                rows={4}
                value={testComment}
                onChange={(e) => setTestComment(e.target.value)}
                placeholder="输入要测试的评论内容"
              />
            </Form.Item>
            <Button
              type="primary"
              onClick={testAIReply}
              loading={testingReply}
              icon={<TestTubeOutlined />}
              size="large"
            >
              生成AI回复
            </Button>
          </Col>
          <Col span={8}>
            {testResult && (
              <Card
                title="测试结果"
                size="small"
                style={{ marginTop: 32 }}
              >
                {testResult.success ? (
                  <div>
                    <Alert
                      message="生成成功"
                      type="success"
                      showIcon
                      style={{ marginBottom: 16 }}
                    />
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>AI回复:</Text>
                    </div>
                    <div style={{
                      background: '#f5f5f5',
                      padding: 12,
                      borderRadius: 4,
                      marginBottom: 16
                    }}>
                      {testResult.reply}
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      <div>模型: {testResult.model || 'N/A'}</div>
                      <div>Token使用: {testResult.tokens_used || 'N/A'}</div>
                      {testResult.confidence_score && (
                        <div>置信度: {testResult.confidence_score}</div>
                      )}
                    </div>
                  </div>
                ) : (
                  <Alert
                    message="生成失败"
                    description={testResult.error}
                    type="error"
                    showIcon
                  />
                )}
              </Card>
            )}
          </Col>
        </Row>
      </Card>
    );
  }
};

export default ConfigPage;
