"""
API数据模型模块
包含所有Pydantic模型定义
"""
from .auth import (
    UserBase,
    UserCreate,
    UserUpdate,
    UserResponse,
    UserLogin,
    Token,
    TokenData,
    PasswordChange,
    PasswordReset,
    PasswordResetConfirm
)
from .xiaohongshu import (
    XiaohongshuAccountBase,
    XiaohongshuAccountCreate,
    XiaohongshuAccountUpdate,
    XiaohongshuAccountResponse,
    XiaohongshuAccountLogin,
    XiaohongshuAccountStatus,
    CookieData,
    XiaohongshuAccountStats
)
from .note import (
    NoteBase,
    NoteCreate,
    NoteUpdate,
    NoteResponse,
    NoteStats,
    NoteBatchOperation,
    NoteSearchFilter,
    NoteStatusEnum
)
from .comment import (
    CommentBase,
    CommentCreate,
    CommentUpdate,
    CommentResponse,
    CommentReply,
    CommentBatchReply,
    CommentSearchFilter,
    CommentStats,
    CommentStatusEnum,
    CrawlRequest,
    CrawlResult
)

__all__ = [
    # 认证相关
    "UserBase",
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "UserLogin",
    "Token",
    "TokenData",
    "PasswordChange",
    "PasswordReset",
    "PasswordResetConfirm",
    # 小红书账号相关
    "XiaohongshuAccountBase",
    "XiaohongshuAccountCreate",
    "XiaohongshuAccountUpdate",
    "XiaohongshuAccountResponse",
    "XiaohongshuAccountLogin",
    "XiaohongshuAccountStatus",
    "CookieData",
    "XiaohongshuAccountStats",
    # 笔记相关
    "NoteBase",
    "NoteCreate",
    "NoteUpdate",
    "NoteResponse",
    "NoteStats",
    "NoteBatchOperation",
    "NoteSearchFilter",
    "NoteStatusEnum",
    # 留言相关
    "CommentBase",
    "CommentCreate",
    "CommentUpdate",
    "CommentResponse",
    "CommentReply",
    "CommentBatchReply",
    "CommentSearchFilter",
    "CommentStats",
    "CommentStatusEnum",
    "CrawlRequest",
    "CrawlResult"
]
