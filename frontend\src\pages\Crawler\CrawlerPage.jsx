import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Statistic,
  Progress,
  List,
  Tag,
  Modal,
  Form,
  InputNumber,
  Switch,
  Select,
  Alert,
  Tabs,
  Timeline,
  Badge,
  Tooltip,
  message,
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  SettingOutlined,
  RobotOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  Bar<PERSON>hartOutlined,
  ThunderboltOutlined,
} from '@ant-design/icons';
import { crawlerService } from '../../services/crawlerService';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

const CrawlerPage = () => {
  const [crawlerStatus, setCrawlerStatus] = useState('stopped');
  const [loading, setLoading] = useState(false);
  const [configModalVisible, setConfigModalVisible] = useState(false);
  const [logs, setLogs] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [stats, setStats] = useState({});
  const [form] = Form.useForm();

  // 模拟数据
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // 调用真实的后端API
      const response = await crawlerService.getCrawlerStatus();

      if (response.success) {
        const data = response.data;

        // 设置统计数据
        setStats({
          totalTasks: data.total_tasks || 0,
          runningTasks: data.running_tasks || 0,
          completedTasks: data.completed_tasks || 0,
          failedTasks: data.failed_tasks || 0,
          totalComments: data.total_comments || 0,
          newComments: data.new_comments || 0,
          successRate: data.success_rate || 0,
          avgResponseTime: data.avg_response_time || 0,
        });

        // 设置任务列表
        setTasks(data.tasks || []);

        // 设置爬虫状态
        setCrawlerStatus(data.crawler_status || 'stopped');
      } else {
        message.error('获取爬虫状态失败');
        // 如果API调用失败，使用默认数据
        setDefaultData();
      }
    } catch (error) {
      console.error('获取爬虫状态失败:', error);
      message.error('连接服务器失败，显示默认数据');
      // 如果API调用失败，使用默认数据
      setDefaultData();
    } finally {
      setLoading(false);
    }
  };

  // 设置默认数据的函数
  const setDefaultData = () => {
    setStats({
      totalTasks: 3,
      runningTasks: 1,
      completedTasks: 1,
      failedTasks: 1,
      totalComments: 20,
      newComments: 4,
      successRate: 67,
      avgResponseTime: 1.2,
    });

    setTasks([
      {
        id: 1,
        note_title: '美妆分享 - 夏日护肤心得',
        account_name: '主账号',
        status: 'running',
        progress: 65,
        start_time: '2024-01-18 10:30:00',
        last_update: '2024-01-18 10:35:00',
        comments_found: 12,
        errors: 0,
      },
      {
        id: 2,
        note_title: '时尚穿搭 - 春季搭配指南',
        account_name: '主账号',
        status: 'completed',
        progress: 100,
        start_time: '2024-01-18 10:00:00',
        last_update: '2024-01-18 10:25:00',
        comments_found: 8,
        errors: 0,
      },
      {
        id: 3,
        note_title: '美食探店 - 网红咖啡厅推荐',
        account_name: '备用账号',
        status: 'failed',
        progress: 30,
        start_time: '2024-01-18 09:45:00',
        last_update: '2024-01-18 09:50:00',
        comments_found: 0,
        errors: 1,
      },
    ]);

    setLogs([
      {
        id: 1,
        time: '2024-01-18 10:35:00',
        level: 'info',
        message: '成功抓取笔记"美妆分享"的12条新留言',
        task_id: 1,
      },
      {
        id: 2,
        time: '2024-01-18 10:25:00',
        level: 'success',
        message: '任务完成：时尚穿搭 - 春季搭配指南',
        task_id: 2,
      },
      {
        id: 3,
        time: '2024-01-18 09:50:00',
        level: 'error',
        message: '抓取失败：Cookie已过期，请更新账号登录信息',
        task_id: 3,
      },
      {
        id: 4,
        time: '2024-01-18 09:30:00',
        level: 'info',
        message: '定时任务启动，开始抓取3个笔记的留言',
        task_id: null,
      },
    ]);
  };

  // 启动爬虫
  const handleStartCrawler = () => {
    setCrawlerStatus('running');
    message.success('爬虫已启动');
    // 这里应该调用API启动爬虫
  };

  // 停止爬虫
  const handleStopCrawler = () => {
    setCrawlerStatus('stopped');
    message.success('爬虫已停止');
    // 这里应该调用API停止爬虫
  };

  // 手动抓取
  const handleManualCrawl = () => {
    Modal.confirm({
      title: '手动抓取',
      content: '确定要立即执行一次完整的数据抓取吗？',
      onOk: () => {
        message.success('手动抓取任务已启动');
        // 这里应该调用API执行手动抓取
      },
    });
  };

  // 配置爬虫参数
  const handleConfig = () => {
    form.setFieldsValue({
      crawl_interval: 30,
      max_concurrent: 3,
      request_delay: 2,
      retry_times: 3,
      enable_proxy: false,
      auto_restart: true,
    });
    setConfigModalVisible(true);
  };

  // 提交配置
  const handleSubmitConfig = async (values) => {
    try {
      // 这里应该调用API保存配置
      message.success('配置保存成功');
      setConfigModalVisible(false);
    } catch (error) {
      message.error('配置保存失败');
    }
  };

  // 获取状态标签
  const getStatusTag = (status) => {
    const config = {
      running: { color: 'processing', text: '运行中' },
      completed: { color: 'success', text: '已完成' },
      failed: { color: 'error', text: '失败' },
      pending: { color: 'default', text: '等待中' },
    };
    return <Tag color={config[status]?.color}>{config[status]?.text}</Tag>;
  };

  // 获取日志级别图标
  const getLogIcon = (level) => {
    const config = {
      info: { icon: <ClockCircleOutlined />, color: '#1890ff' },
      success: { icon: <CheckCircleOutlined />, color: '#52c41a' },
      error: { icon: <ExclamationCircleOutlined />, color: '#ff4d4f' },
      warning: { icon: <ExclamationCircleOutlined />, color: '#fa8c16' },
    };
    return <span style={{ color: config[level]?.color }}>{config[level]?.icon}</span>;
  };

  return (
    <div className="crawler-page">
      <div className="page-header">
        <Title level={2}>爬虫管理</Title>
        <p>管理数据抓取任务，监控爬虫运行状态和性能</p>
      </div>

      {/* 控制面板 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Space size="large">
              <div>
                <Badge 
                  status={crawlerStatus === 'running' ? 'processing' : 'default'} 
                  text={crawlerStatus === 'running' ? '运行中' : '已停止'}
                />
                <div style={{ fontSize: '24px', fontWeight: 'bold', marginTop: 8 }}>
                  爬虫状态
                </div>
              </div>
            </Space>
          </Col>
          <Col span={18} style={{ textAlign: 'right' }}>
            <Space size="middle">
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={handleStartCrawler}
                disabled={crawlerStatus === 'running'}
                loading={loading}
              >
                启动爬虫
              </Button>
              <Button
                icon={<PauseCircleOutlined />}
                onClick={handleStopCrawler}
                disabled={crawlerStatus === 'stopped'}
              >
                停止爬虫
              </Button>
              <Button
                icon={<ThunderboltOutlined />}
                onClick={handleManualCrawl}
              >
                手动抓取
              </Button>
              <Button
                icon={<SettingOutlined />}
                onClick={handleConfig}
              >
                配置参数
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadData}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总任务数"
              value={stats.totalTasks}
              prefix={<RobotOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="运行中"
              value={stats.runningTasks}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="成功率"
              value={stats.successRate}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="新增留言"
              value={stats.newComments}
              prefix={<EyeOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Row gutter={16}>
        <Col span={16}>
          <Card title="任务列表" extra={
            <Space>
              <Text type="secondary">实时更新</Text>
              <Badge status="processing" />
            </Space>
          }>
            <List
              dataSource={tasks}
              renderItem={(task) => (
                <List.Item
                  actions={[
                    <Tooltip title="查看详情">
                      <Button type="link" icon={<EyeOutlined />} />
                    </Tooltip>,
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <Space>
                        <span>{task.note_title}</span>
                        {getStatusTag(task.status)}
                      </Space>
                    }
                    description={
                      <div>
                        <div style={{ marginBottom: 8 }}>
                          <Text type="secondary">账号: {task.account_name}</Text>
                          <Text type="secondary" style={{ marginLeft: 16 }}>
                            开始时间: {task.start_time}
                          </Text>
                        </div>
                        <div style={{ marginBottom: 8 }}>
                          <Progress 
                            percent={task.progress} 
                            size="small"
                            status={task.status === 'failed' ? 'exception' : 'normal'}
                          />
                        </div>
                        <Space>
                          <Text type="secondary">找到留言: {task.comments_found}</Text>
                          {task.errors > 0 && (
                            <Text type="danger">错误: {task.errors}</Text>
                          )}
                        </Space>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        <Col span={8}>
          <Card title="实时日志" style={{ height: 500 }}>
            <div style={{ height: 400, overflowY: 'auto' }}>
              <Timeline>
                {logs.map((log) => (
                  <Timeline.Item
                    key={log.id}
                    dot={getLogIcon(log.level)}
                  >
                    <div style={{ fontSize: '12px', color: '#666', marginBottom: 4 }}>
                      {log.time}
                    </div>
                    <div>{log.message}</div>
                  </Timeline.Item>
                ))}
              </Timeline>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 性能监控 */}
      <Card title="性能监控" style={{ marginTop: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '14px', color: '#666', marginBottom: 8 }}>
                平均响应时间
              </div>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                {stats.avgResponseTime}s
              </div>
            </div>
          </Col>
          <Col span={8}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '14px', color: '#666', marginBottom: 8 }}>
                今日抓取量
              </div>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                {stats.totalComments}
              </div>
            </div>
          </Col>
          <Col span={8}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '14px', color: '#666', marginBottom: 8 }}>
                系统负载
              </div>
              <Progress 
                type="circle" 
                percent={35} 
                width={60}
                format={percent => `${percent}%`}
              />
            </div>
          </Col>
        </Row>
      </Card>

      {/* 配置模态框 */}
      <Modal
        title="爬虫配置"
        open={configModalVisible}
        onCancel={() => setConfigModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmitConfig}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="crawl_interval"
                label="抓取间隔(分钟)"
                rules={[{ required: true, message: '请设置抓取间隔' }]}
              >
                <InputNumber
                  min={5}
                  max={1440}
                  style={{ width: '100%' }}
                  placeholder="5-1440分钟"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="max_concurrent"
                label="最大并发数"
                rules={[{ required: true, message: '请设置最大并发数' }]}
              >
                <InputNumber
                  min={1}
                  max={10}
                  style={{ width: '100%' }}
                  placeholder="1-10"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="request_delay"
                label="请求延迟(秒)"
                rules={[{ required: true, message: '请设置请求延迟' }]}
              >
                <InputNumber
                  min={0.5}
                  max={10}
                  step={0.5}
                  style={{ width: '100%' }}
                  placeholder="0.5-10秒"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="retry_times"
                label="重试次数"
                rules={[{ required: true, message: '请设置重试次数' }]}
              >
                <InputNumber
                  min={0}
                  max={5}
                  style={{ width: '100%' }}
                  placeholder="0-5次"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enable_proxy"
                label="启用代理"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="auto_restart"
                label="自动重启"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Alert
            message="配置说明"
            description="修改配置后需要重启爬虫才能生效。建议在低峰期进行配置调整。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setConfigModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                保存配置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CrawlerPage;
