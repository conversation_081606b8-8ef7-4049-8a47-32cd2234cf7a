import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Statistic,
  Switch,
  Tooltip,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  ReloadOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { accountsService } from '../../services/accountsService';

const { Title } = Typography;

const AccountsPage = () => {
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAccount, setEditingAccount] = useState(null);
  const [form] = Form.useForm();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 加载账号列表
  const loadAccounts = async (page = 1, size = 10) => {
    setLoading(true);
    try {
      const response = await accountsService.getAccounts({
        page,
        size,
      });

      if (response.success) {
        setAccounts(response.data || []);
        setPagination({
          current: response.page || page,
          pageSize: response.size || size,
          total: response.total || 0,
        });
      } else {
        message.error(response.message || '获取账号列表失败');
      }
    } catch (error) {
      console.error('获取账号列表失败:', error);
      message.error('获取账号列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAccounts();
  }, []);

  // 表格列配置
  const columns = [
    {
      title: '账号名称',
      dataIndex: 'account_name',
      key: 'account_name',
      render: (text) => <strong>{text}</strong>,
    },
    {
      title: '小红书ID',
      dataIndex: 'account_id',
      key: 'account_id',
    },
    {
      title: '登录手机',
      dataIndex: 'login_phone',
      key: 'login_phone',
      render: (text) => (
        <Space>
          <PhoneOutlined />
          {text}
        </Space>
      ),
    },
    {
      title: '登录邮箱',
      dataIndex: 'login_email',
      key: 'login_email',
      render: (text) => (
        <Space>
          <MailOutlined />
          {text}
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active, record) => (
        <Space>
          <Tag
            icon={active ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
            color={active ? 'success' : 'default'}
          >
            {active ? '活跃' : '停用'}
          </Tag>
          <Tooltip title={active ? '点击停用' : '点击激活'}>
            <Switch
              size="small"
              checked={active}
              onChange={(checked) => handleToggleStatus(record.id, checked)}
              loading={loading}
            />
          </Tooltip>
        </Space>
      ),
    },
    {
      title: '最后登录',
      dataIndex: 'last_login',
      key: 'last_login',
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑账号">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="账号设置">
            <Button
              type="link"
              size="small"
              icon={<SettingOutlined />}
              onClick={() => handleSettings(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个账号吗？"
            description="删除后将无法恢复，请谨慎操作"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除账号">
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 处理添加账号
  const handleAdd = () => {
    setEditingAccount(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 处理编辑账号
  const handleEdit = (account) => {
    setEditingAccount(account);
    form.setFieldsValue(account);
    setModalVisible(true);
  };

  // 处理删除账号
  const handleDelete = async (id) => {
    try {
      const response = await accountsService.deleteAccount(id);
      if (response.success) {
        message.success('账号删除成功');
        loadAccounts(pagination.current, pagination.pageSize);
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('删除账号失败:', error);
      message.error('删除失败，请稍后重试');
    }
  };

  // 处理状态切换
  const handleToggleStatus = async (id, checked) => {
    try {
      const response = checked
        ? await accountsService.activateAccount(id)
        : await accountsService.deactivateAccount(id);

      if (response.success) {
        message.success(checked ? '账号激活成功' : '账号停用成功');
        loadAccounts(pagination.current, pagination.pageSize);
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      console.error('切换账号状态失败:', error);
      message.error('操作失败，请稍后重试');
    }
  };

  // 处理账号设置
  const handleSettings = (account) => {
    // TODO: 实现账号设置功能
    message.info('账号设置功能开发中...');
  };

  // 处理表单提交
  const handleSubmit = async (values) => {
    try {
      let response;
      if (editingAccount) {
        // 更新账号
        response = await accountsService.updateAccount(editingAccount.id, values);
      } else {
        // 添加账号
        response = await accountsService.createAccount(values);
      }

      if (response.success) {
        message.success(editingAccount ? '账号更新成功' : '账号添加成功');
        setModalVisible(false);
        loadAccounts(pagination.current, pagination.pageSize);
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error('操作失败，请稍后重试');
    }
  };

  // 处理分页变化
  const handleTableChange = (paginationInfo) => {
    loadAccounts(paginationInfo.current, paginationInfo.pageSize);
  };

  // 刷新数据
  const handleRefresh = () => {
    loadAccounts(pagination.current, pagination.pageSize);
  };

  // 统计数据
  const stats = {
    total: pagination.total,
    active: accounts.filter(account => account.is_active).length,
    inactive: accounts.filter(account => !account.is_active).length,
  };

  return (
    <div className="accounts-page">
      <div className="page-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <div>
          <Title level={2}>账号管理</Title>
          <p>管理您的小红书账号，配置登录信息和状态</p>
        </div>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
          >
            刷新
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            添加账号
          </Button>
        </Space>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="总账号数"
              value={stats.total}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="活跃账号"
              value={stats.active}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="停用账号"
              value={stats.inactive}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#8c8c8c' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 账号列表 */}
      <Card title="账号列表">
        <Table
          columns={columns}
          dataSource={accounts}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            pageSizeOptions: ['10', '20', '50', '100'],
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* 添加/编辑账号模态框 */}
      <Modal
        title={editingAccount ? '编辑账号' : '添加账号'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="account_name"
            label="账号名称"
            rules={[
              { required: true, message: '请输入账号名称' },
              { max: 50, message: '账号名称不能超过50个字符' },
            ]}
          >
            <Input placeholder="请输入账号名称" />
          </Form.Item>

          <Form.Item
            name="account_id"
            label="小红书用户ID"
            rules={[
              { required: true, message: '请输入小红书用户ID' },
            ]}
          >
            <Input placeholder="请输入小红书用户ID" />
          </Form.Item>

          <Form.Item
            name="login_phone"
            label="登录手机号"
            rules={[
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
            ]}
          >
            <Input placeholder="请输入登录手机号" />
          </Form.Item>

          <Form.Item
            name="login_email"
            label="登录邮箱"
            rules={[
              { type: 'email', message: '请输入正确的邮箱地址' },
            ]}
          >
            <Input placeholder="请输入登录邮箱" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingAccount ? '更新' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AccountsPage;
