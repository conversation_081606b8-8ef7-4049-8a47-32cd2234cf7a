# 小红书助手 - 前端应用

基于 React + Vite + Ant Design 构建的现代化管理界面。

## 🚀 快速开始

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

应用将在 http://localhost:3000 启动

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 📁 项目结构

```
src/
├── components/          # 通用组件
│   ├── Layout/         # 布局组件
│   └── Auth/           # 认证组件
├── pages/              # 页面组件
│   ├── Auth/           # 登录注册页面
│   ├── Dashboard/      # 仪表盘页面
│   └── Accounts/       # 账号管理页面
├── services/           # API服务
│   ├── api.js          # API基础配置
│   └── authService.js  # 认证服务
├── stores/             # 状态管理
│   └── authStore.js    # 认证状态
├── utils/              # 工具函数
├── App.jsx             # 应用入口
└── main.jsx            # 主入口文件
```

## 🎨 已实现功能

### ✅ 用户认证
- 登录页面 (`/login`)
- 注册功能
- JWT令牌管理
- 路由保护

### ✅ 主布局
- 响应式侧边栏导航
- 用户信息显示
- 主题配置

### ✅ 仪表盘 (`/`)
- 数据统计卡片
- 最近活动列表
- 快速操作面板
- 系统提醒

### ✅ 账号管理 (`/accounts`)
- 账号列表展示
- 添加/编辑账号
- 账号状态管理
- 统计信息

### 🚧 开发中功能
- 笔记管理 (`/notes`)
- 留言管理 (`/comments`)
- 爬虫管理 (`/crawler`)
- 系统设置 (`/settings`)

## 🔧 技术栈

- **React 18** - 用户界面框架
- **Vite** - 快速构建工具
- **Ant Design 5** - 企业级UI组件库
- **React Router 6** - 路由管理
- **Zustand** - 轻量级状态管理
- **Axios** - HTTP客户端
- **Day.js** - 日期处理

## 🎯 设计特点

### 现代化界面
- 简洁美观的设计风格
- 响应式布局适配
- 流畅的动画效果
- 暗色主题支持

### 用户体验
- 直观的导航结构
- 快速的页面加载
- 友好的错误提示
- 完善的表单验证

### 开发体验
- 组件化开发
- TypeScript支持
- 热重载开发
- ESLint代码检查

## 🔗 API集成

前端应用通过RESTful API与后端通信：

- **基础URL**: `http://localhost:8000/api/v1`
- **认证方式**: JWT Bearer Token
- **请求格式**: JSON
- **响应格式**: 统一的JSON格式

### API服务配置
```javascript
// 环境变量配置
VITE_API_URL=http://localhost:8000/api/v1

// 自动添加认证头
Authorization: Bearer <token>

// 统一错误处理
- 401: 自动跳转登录
- 403: 权限不足提示
- 422: 表单验证错误
- 500: 服务器错误提示
```

## 🧪 测试

### 运行测试脚本
```bash
node test-frontend.js
```

### 手动测试
1. 访问 http://localhost:3000
2. 测试登录功能（暂时使用模拟数据）
3. 浏览各个页面功能
4. 测试响应式布局

## 📱 响应式设计

应用支持多种设备尺寸：

- **桌面端**: >= 1200px
- **平板端**: 768px - 1199px  
- **移动端**: < 768px

### 移动端优化
- 侧边栏自动折叠
- 触摸友好的按钮尺寸
- 简化的导航结构
- 优化的表格显示

## 🔒 安全特性

- JWT令牌自动管理
- 路由级别的权限控制
- XSS攻击防护
- CSRF保护
- 敏感信息加密存储

## 🚀 部署

### 开发环境
```bash
npm run dev
```

### 生产环境
```bash
# 构建
npm run build

# 使用Nginx部署
cp -r dist/* /var/www/html/

# 或使用Node.js服务器
npm run preview
```

### Docker部署
```bash
docker build -t xiaohongshu-frontend .
docker run -p 3000:3000 xiaohongshu-frontend
```

## 📞 支持

如有问题或建议，请查看：
- 项目文档: `../docs/`
- API文档: `../docs/api.md`
- 开发指南: `../docs/development.md`
