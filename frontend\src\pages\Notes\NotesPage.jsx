import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Statistic,
  Select,
  Switch,
  InputNumber,
  Tooltip,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  BookOutlined,
  EyeOutlined,
  SearchOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { notesService } from '../../services/notesService';
import { accountsService } from '../../services/accountsService';

const { Title } = Typography;
const { Option } = Select;
const { Search } = Input;

const NotesPage = () => {
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingNote, setEditingNote] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [accounts, setAccounts] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [form] = Form.useForm();

  useEffect(() => {
    loadNotes();
    loadAccounts();
  }, []);

  // 加载笔记列表
  const loadNotes = async (page = 1, size = 10, filters = {}) => {
    setLoading(true);
    try {
      const params = {
        page,
        size,
        ...filters,
      };

      if (searchText) {
        params.keyword = searchText;
      }

      if (statusFilter !== 'all') {
        if (statusFilter === 'monitoring') {
          params.status = 'ACTIVE';
        } else if (statusFilter === 'stopped') {
          params.status = 'INACTIVE';
        }
      }

      const response = await notesService.getNotes(params);

      if (response.success) {
        // 转换数据格式以匹配前端显示
        const transformedNotes = (response.data || []).map(note => ({
          id: note.id,
          title: note.title || '未知标题',
          url: note.note_url,
          note_url: note.note_url,
          account_id: note.account_id,
          account_name: getAccountName(note.account_id),
          is_monitoring: note.status === 'ACTIVE',
          status: note.status,
          crawl_interval: note.crawl_interval || 300,
          auto_reply_enabled: note.auto_reply_enabled,
          last_crawl: note.last_crawled ? formatDateTime(note.last_crawled) : '从未抓取',
          last_crawled: note.last_crawled,
          comment_count: note.comments_count || 0,
          comments_count: note.comments_count || 0,
          new_comments: 0, // 这个需要从其他接口获取
          likes_count: note.likes_count || 0,
          shares_count: note.shares_count || 0,
          created_at: formatDateTime(note.created_at),
          updated_at: note.updated_at,
        }));

        setNotes(transformedNotes);
        setPagination({
          current: response.page || page,
          pageSize: response.size || size,
          total: response.total || 0,
        });
      } else {
        message.error(response.message || '获取笔记列表失败');
      }
    } catch (error) {
      console.error('获取笔记列表失败:', error);
      message.error('获取笔记列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 加载账号列表
  const loadAccounts = async () => {
    try {
      const response = await accountsService.getAccounts({ page: 1, size: 100 });
      if (response.success) {
        setAccounts(response.data || []);
      }
    } catch (error) {
      console.error('获取账号列表失败:', error);
    }
  };

  // 获取账号名称
  const getAccountName = (accountId) => {
    const account = accounts.find(acc => acc.id === accountId);
    return account ? account.account_name : '未知账号';
  };

  // 格式化日期时间
  const formatDateTime = (dateString) => {
    if (!dateString) return '';
    try {
      return new Date(dateString).toLocaleString('zh-CN');
    } catch (error) {
      return dateString;
    }
  };

  // 处理搜索
  const handleSearch = (value) => {
    setSearchText(value);
    loadNotes(1, pagination.pageSize, { keyword: value });
  };

  // 处理状态过滤
  const handleStatusFilter = (value) => {
    setStatusFilter(value);
    const filters = {};
    if (value === 'monitoring') {
      filters.status = 'ACTIVE';
    } else if (value === 'stopped') {
      filters.status = 'INACTIVE';
    }
    loadNotes(1, pagination.pageSize, filters);
  };

  // 处理分页变化
  const handleTableChange = (paginationInfo) => {
    loadNotes(paginationInfo.current, paginationInfo.pageSize);
  };

  // 刷新数据
  const handleRefresh = () => {
    loadNotes(pagination.current, pagination.pageSize);
  };

  // 表格列配置
  const columns = [
    {
      title: '笔记标题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            <a href={record.url} target="_blank" rel="noopener noreferrer">
              {record.url}
            </a>
          </div>
        </div>
      ),
    },
    {
      title: '所属账号',
      dataIndex: 'account_name',
      key: 'account_name',
      render: (text) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: '监控状态',
      dataIndex: 'is_monitoring',
      key: 'is_monitoring',
      render: (monitoring, record) => (
        <Space direction="vertical" size="small">
          <Tag 
            icon={monitoring ? <PlayCircleOutlined /> : <PauseCircleOutlined />}
            color={monitoring ? 'success' : 'default'}
          >
            {monitoring ? '监控中' : '已停止'}
          </Tag>
          <div style={{ fontSize: '12px', color: '#666' }}>
            间隔: {Math.floor(record.crawl_interval / 60)}分钟
          </div>
        </Space>
      ),
    },
    {
      title: '留言统计',
      key: 'comments',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <div>总数: <strong>{record.comment_count}</strong></div>
          {record.new_comments > 0 && (
            <Tag color="orange">新增: {record.new_comments}</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '最后抓取',
      dataIndex: 'last_crawl',
      key: 'last_crawl',
      render: (text) => (
        <div style={{ fontSize: '12px' }}>
          {text || '从未抓取'}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="查看留言">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewComments(record)}
            />
          </Tooltip>
          <Tooltip title={record.is_monitoring ? '停止监控' : '开始监控'}>
            <Button
              type="link"
              icon={record.is_monitoring ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={() => handleToggleMonitoring(record)}
            />
          </Tooltip>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个笔记吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 处理添加笔记
  const handleAdd = () => {
    setEditingNote(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 处理编辑笔记
  const handleEdit = (note) => {
    setEditingNote(note);
    form.setFieldsValue(note);
    setModalVisible(true);
  };

  // 处理删除笔记
  const handleDelete = async (id) => {
    try {
      const response = await notesService.deleteNote(id);
      if (response.success) {
        message.success('笔记删除成功');
        loadNotes(pagination.current, pagination.pageSize);
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('删除笔记失败:', error);
      message.error('删除失败，请稍后重试');
    }
  };

  // 处理切换监控状态
  const handleToggleMonitoring = async (note) => {
    try {
      const newStatus = note.is_monitoring ? 'INACTIVE' : 'ACTIVE';
      const response = await notesService.updateNote(note.id, {
        status: newStatus
      });

      if (response.success) {
        message.success(`${note.is_monitoring ? '停止' : '开始'}监控成功`);
        loadNotes(pagination.current, pagination.pageSize);
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      console.error('切换监控状态失败:', error);
      message.error('操作失败，请稍后重试');
    }
  };

  // 处理查看留言
  const handleViewComments = (note) => {
    // 这里可以跳转到留言管理页面，并传递笔记ID作为筛选条件
    message.info(`查看笔记"${note.title}"的留言`);
  };

  // 处理表单提交
  const handleSubmit = async (values) => {
    try {
      let response;
      if (editingNote) {
        // 更新笔记
        const updateData = {
          title: values.title,
          crawl_interval: values.crawl_interval * 60, // 转换为秒
          auto_reply_enabled: values.auto_reply_enabled,
        };
        response = await notesService.updateNote(editingNote.id, updateData);
      } else {
        // 添加笔记
        const createData = {
          account_id: values.account_id,
          note_url: values.note_url,
          title: values.title,
          crawl_interval: values.crawl_interval * 60, // 转换为秒
          auto_reply_enabled: values.auto_reply_enabled,
        };
        response = await notesService.createNote(createData);
      }

      if (response.success) {
        message.success(editingNote ? '笔记更新成功' : '笔记添加成功');
        setModalVisible(false);
        loadNotes(pagination.current, pagination.pageSize);
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error('操作失败，请稍后重试');
    }
  };

  // 统计数据
  const stats = {
    total: pagination.total,
    monitoring: notes.filter(note => note.is_monitoring).length,
    stopped: notes.filter(note => !note.is_monitoring).length,
    totalComments: notes.reduce((sum, note) => sum + note.comment_count, 0),
  };

  return (
    <div className="notes-page">
      <div className="page-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <div>
          <Title level={2}>笔记管理</Title>
          <p>管理监控的小红书笔记，配置抓取参数和查看留言统计</p>
        </div>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
          >
            刷新
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            添加笔记
          </Button>
        </Space>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总笔记数"
              value={stats.total}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="监控中"
              value={stats.monitoring}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已停止"
              value={stats.stopped}
              prefix={<PauseCircleOutlined />}
              valueStyle={{ color: '#8c8c8c' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总留言数"
              value={stats.totalComments}
              prefix={<EyeOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={8}>
            <Search
              placeholder="搜索笔记标题或URL"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onSearch={handleSearch}
              prefix={<SearchOutlined />}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              value={statusFilter}
              onChange={handleStatusFilter}
              style={{ width: '100%' }}
            >
              <Option value="all">全部状态</Option>
              <Option value="monitoring">监控中</Option>
              <Option value="stopped">已停止</Option>
            </Select>
          </Col>
          <Col span={12} style={{ textAlign: 'right' }}>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 笔记列表 */}
      <Card title="笔记列表">
        <Table
          columns={columns}
          dataSource={notes}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            pageSizeOptions: ['10', '20', '50', '100'],
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* 添加/编辑笔记模态框 */}
      <Modal
        title={editingNote ? '编辑笔记' : '添加笔记'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="title"
            label="笔记标题"
            rules={[
              { required: true, message: '请输入笔记标题' },
              { max: 100, message: '标题不能超过100个字符' },
            ]}
          >
            <Input placeholder="请输入笔记标题" />
          </Form.Item>

          <Form.Item
            name="note_url"
            label="笔记URL"
            rules={[
              { required: true, message: '请输入笔记URL' },
              { type: 'url', message: '请输入有效的URL' },
            ]}
          >
            <Input placeholder="https://www.xiaohongshu.com/explore/..." />
          </Form.Item>

          <Form.Item
            name="account_id"
            label="所属账号"
            rules={[{ required: true, message: '请选择所属账号' }]}
          >
            <Select placeholder="请选择账号">
              {accounts.map(account => (
                <Option key={account.id} value={account.id}>
                  {account.account_name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="crawl_interval"
                label="抓取间隔(分钟)"
                rules={[{ required: true, message: '请设置抓取间隔' }]}
                initialValue={30}
              >
                <InputNumber
                  min={5}
                  max={1440}
                  style={{ width: '100%' }}
                  placeholder="5-1440分钟"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="auto_reply_enabled"
                label="自动回复"
                valuePropName="checked"
                initialValue={false}
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingNote ? '更新' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default NotesPage;
