from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ..deps import get_current_user
from ...models import get_db
from ...models.user import User
from ...models.note import Comment
from ...services.automation_service import AutomationEngine
from ...api.responses import success_response, error_response

router = APIRouter()


class ProcessCommentRequest(BaseModel):
    comment_id: int


class BatchProcessRequest(BaseModel):
    comment_ids: List[int]


class TestRuleRequest(BaseModel):
    rule_id: int
    test_content: str


@router.post("/process-comment")
async def process_comment(
    request: ProcessCommentRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """处理单个留言的自动化规则"""
    try:
        # 获取留言
        comment = db.query(Comment).filter(
            Comment.id == request.comment_id,
            Comment.user_id == current_user.id
        ).first()
        
        if not comment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="留言不存在"
            )
        
        # 创建自动化引擎
        automation_engine = AutomationEngine(db, current_user.id)
        
        # 处理留言
        result = await automation_engine.process_new_comment(comment)
        
        return success_response(
            data=result,
            message="留言处理完成"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        return error_response(
            message=f"处理留言失败: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/batch-process")
async def batch_process_comments(
    request: BatchProcessRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量处理留言的自动化规则"""
    try:
        # 创建自动化引擎
        automation_engine = AutomationEngine(db, current_user.id)
        
        # 在后台任务中处理
        def process_batch():
            return automation_engine.batch_process_comments(request.comment_ids)
        
        background_tasks.add_task(process_batch)
        
        return success_response(
            data={
                "comment_count": len(request.comment_ids),
                "status": "processing"
            },
            message="批量处理已开始，请稍后查看结果"
        )
        
    except Exception as e:
        return error_response(
            message=f"批量处理失败: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/test-rule")
async def test_rule(
    request: TestRuleRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """测试自动化规则"""
    try:
        automation_engine = AutomationEngine(db, current_user.id)
        result = await automation_engine.test_rule(request.rule_id, request.test_content)
        
        return success_response(
            data=result,
            message="规则测试完成"
        )
        
    except Exception as e:
        return error_response(
            message=f"规则测试失败: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/rule-stats/{rule_id}")
def get_rule_statistics(
    rule_id: int,
    days: int = 30,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取规则统计信息"""
    try:
        automation_engine = AutomationEngine(db, current_user.id)
        stats = automation_engine.get_rule_statistics(rule_id, days)
        
        return success_response(
            data=stats,
            message="获取规则统计成功"
        )
        
    except Exception as e:
        return error_response(
            message=f"获取规则统计失败: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/auto-process-new-comments")
async def auto_process_new_comments(
    background_tasks: BackgroundTasks,
    limit: int = 50,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """自动处理新留言"""
    try:
        # 获取待处理的留言
        pending_comments = db.query(Comment).filter(
            Comment.user_id == current_user.id,
            Comment.reply_status == 'pending'
        ).limit(limit).all()
        
        if not pending_comments:
            return success_response(
                data={"processed_count": 0},
                message="没有待处理的留言"
            )
        
        # 创建自动化引擎
        automation_engine = AutomationEngine(db, current_user.id)
        
        # 在后台处理
        async def process_comments():
            results = []
            for comment in pending_comments:
                try:
                    result = await automation_engine.process_new_comment(comment)
                    results.append(result)
                except Exception as e:
                    results.append({
                        "comment_id": comment.id,
                        "error": str(e)
                    })
            return results
        
        background_tasks.add_task(process_comments)
        
        return success_response(
            data={
                "comment_count": len(pending_comments),
                "status": "processing"
            },
            message="自动处理已开始"
        )
        
    except Exception as e:
        return error_response(
            message=f"自动处理失败: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/processing-status")
def get_processing_status(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取处理状态"""
    try:
        # 获取各种状态的留言数量
        total_comments = db.query(Comment).filter(
            Comment.user_id == current_user.id
        ).count()
        
        pending_comments = db.query(Comment).filter(
            Comment.user_id == current_user.id,
            Comment.reply_status == 'pending'
        ).count()
        
        replied_comments = db.query(Comment).filter(
            Comment.user_id == current_user.id,
            Comment.reply_status == 'replied'
        ).count()
        
        # 获取活跃规则数量
        from ...models.reply_template import AIRule
        active_rules = db.query(AIRule).filter(
            AIRule.user_id == current_user.id,
            AIRule.is_active == True
        ).count()
        
        return success_response(
            data={
                "total_comments": total_comments,
                "pending_comments": pending_comments,
                "replied_comments": replied_comments,
                "active_rules": active_rules,
                "processing_rate": round((replied_comments / total_comments * 100), 2) if total_comments > 0 else 0
            },
            message="获取处理状态成功"
        )
        
    except Exception as e:
        return error_response(
            message=f"获取处理状态失败: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
