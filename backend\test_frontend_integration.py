#!/usr/bin/env python3
"""
测试前端和后端的AI功能集成
"""
import requests
import json
import sys
import time

# API基础URL
BACKEND_URL = "http://localhost:8000/api/v1"
FRONTEND_URL = "http://localhost:3000"

def test_backend_endpoints():
    """测试后端API端点"""
    print("🔧 测试后端API端点")
    print("-" * 40)
    
    # 登录获取token
    login_data = {
        "username": "testuser2",
        "password": "testpass123"
    }
    
    response = requests.post(f"{BACKEND_URL}/auth/login", data=login_data)
    if response.status_code != 200:
        print("❌ 登录失败")
        return None
    
    result = response.json()
    token = result["data"]["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    
    print("✅ 登录成功")
    
    # 测试各个API端点
    endpoints = [
        ("GET", "/comments/ai/config", "获取AI配置"),
        ("POST", "/comments/ai/ollama/test-connection", "测试Ollama连接"),
        ("GET", "/comments/ai/ollama/models", "获取Ollama模型"),
        ("GET", "/comments/ai/templates", "获取回复模板"),
        ("GET", "/comments/ai/statistics", "获取使用统计"),
        ("GET", "/comments/ai/history", "获取回复历史"),
    ]
    
    for method, endpoint, description in endpoints:
        try:
            if method == "GET":
                if "ollama/models" in endpoint:
                    # 需要base_url参数
                    response = requests.get(
                        f"{BACKEND_URL}{endpoint}?base_url=http://************:11434",
                        headers=headers,
                        timeout=10
                    )
                else:
                    response = requests.get(f"{BACKEND_URL}{endpoint}", headers=headers, timeout=10)
            elif method == "POST":
                if "test-connection" in endpoint:
                    response = requests.post(
                        f"{BACKEND_URL}{endpoint}",
                        headers=headers,
                        json={"base_url": "http://************:11434"},
                        timeout=10
                    )
                else:
                    response = requests.post(f"{BACKEND_URL}{endpoint}", headers=headers, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {description}: 成功")
            else:
                print(f"⚠️ {description}: 状态码 {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"⏰ {description}: 超时")
        except Exception as e:
            print(f"❌ {description}: 错误 - {str(e)}")
    
    return token

def test_frontend_accessibility():
    """测试前端页面可访问性"""
    print("\n🌐 测试前端页面可访问性")
    print("-" * 40)
    
    try:
        response = requests.get(FRONTEND_URL, timeout=10)
        if response.status_code == 200:
            print("✅ 前端页面可访问")
            return True
        else:
            print(f"❌ 前端页面访问失败: 状态码 {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端页面访问错误: {str(e)}")
        return False

def test_ai_reply_generation(token):
    """测试AI回复生成功能"""
    print("\n🤖 测试AI回复生成功能")
    print("-" * 40)
    
    headers = {"Authorization": f"Bearer {token}"}
    
    test_data = {
        "comment_content": "这个产品看起来不错，请问有什么优惠吗？",
        "context": {
            "note_title": "美妆护肤分享",
            "note_category": "美妆"
        }
    }
    
    try:
        response = requests.post(
            f"{BACKEND_URL}/comments/ai/generate-reply",
            headers=headers,
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ AI回复生成成功")
                print(f"   回复内容: {result['data']['reply'][:50]}...")
                print(f"   使用模型: {result['data'].get('model', 'Unknown')}")
                return True
            else:
                print(f"❌ AI回复生成失败: {result.get('message', '未知错误')}")
        else:
            print(f"❌ AI回复生成请求失败: 状态码 {response.status_code}")
    except Exception as e:
        print(f"❌ AI回复生成错误: {str(e)}")
    
    return False

def test_template_management(token):
    """测试模板管理功能"""
    print("\n📝 测试模板管理功能")
    print("-" * 40)
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 创建测试模板
    template_data = {
        "name": "前端集成测试模板",
        "content": "感谢{user_name}的{action}！这是一个测试模板。",
        "category": "test",
        "tags": ["测试", "集成"],
        "variables": {
            "user_name": "用户名",
            "action": "行为"
        }
    }
    
    try:
        # 创建模板
        response = requests.post(
            f"{BACKEND_URL}/comments/ai/templates",
            headers=headers,
            json=template_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            template_id = result["data"]["id"]
            print("✅ 模板创建成功")
            
            # 获取模板详情
            response = requests.get(
                f"{BACKEND_URL}/comments/ai/templates/{template_id}",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ 模板获取成功")
                
                # 删除测试模板
                response = requests.delete(
                    f"{BACKEND_URL}/comments/ai/templates/{template_id}",
                    headers=headers,
                    timeout=10
                )
                
                if response.status_code == 200:
                    print("✅ 模板删除成功")
                    return True
                else:
                    print("⚠️ 模板删除失败")
            else:
                print("❌ 模板获取失败")
        else:
            print("❌ 模板创建失败")
    except Exception as e:
        print(f"❌ 模板管理测试错误: {str(e)}")
    
    return False

def main():
    """主函数"""
    print("🚀 前端和后端AI功能集成测试")
    print("=" * 50)
    
    # 1. 测试后端API端点
    token = test_backend_endpoints()
    if not token:
        print("\n❌ 后端API测试失败，无法继续")
        return
    
    # 2. 测试前端页面可访问性
    frontend_ok = test_frontend_accessibility()
    
    # 3. 测试AI回复生成功能
    ai_reply_ok = test_ai_reply_generation(token)
    
    # 4. 测试模板管理功能
    template_ok = test_template_management(token)
    
    # 总结测试结果
    print("\n🎯 集成测试结果总结")
    print("=" * 50)
    print(f"✅ 后端API: 正常")
    print(f"{'✅' if frontend_ok else '❌'} 前端页面: {'正常' if frontend_ok else '异常'}")
    print(f"{'✅' if ai_reply_ok else '❌'} AI回复生成: {'正常' if ai_reply_ok else '异常'}")
    print(f"{'✅' if template_ok else '❌'} 模板管理: {'正常' if template_ok else '异常'}")
    
    if all([frontend_ok, ai_reply_ok, template_ok]):
        print("\n🎉 所有功能集成测试通过！")
        print("📋 前端AI配置页面已成功集成后端功能")
        print("🔗 前端地址: http://localhost:3000")
        print("🔗 后端地址: http://localhost:8000")
        print("\n📝 使用说明:")
        print("1. 访问前端页面")
        print("2. 登录系统 (testuser2/testpass123)")
        print("3. 进入AI配置页面")
        print("4. 测试各项AI功能")
    else:
        print("\n⚠️ 部分功能存在问题，请检查相关服务")

if __name__ == "__main__":
    main()
