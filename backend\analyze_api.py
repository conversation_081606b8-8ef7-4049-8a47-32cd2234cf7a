#!/usr/bin/env python3
"""
API端点分析脚本
"""
import sys
sys.path.append('.')
from app.main import app
from fastapi.routing import APIRoute
from collections import defaultdict

def analyze_api_endpoints():
    """分析API端点"""
    print('=== API 端点分析 ===')
    
    routes = []
    for route in app.routes:
        if isinstance(route, APIRoute):
            routes.append({
                'path': route.path,
                'methods': list(route.methods),
                'name': route.name,
                'tags': route.tags
            })

    # 按标签分组
    routes_by_tag = defaultdict(list)
    for route in routes:
        tags = route['tags'] if route['tags'] else ['未分类']
        for tag in tags:
            routes_by_tag[tag].append(route)

    for tag, tag_routes in routes_by_tag.items():
        print(f'\n📁 {tag}:')
        for route in tag_routes:
            methods_str = ', '.join(route['methods'])
            print(f'  {methods_str:15} {route["path"]}')

    print(f'\n📊 总计: {len(routes)} 个API端点')
    
    # 检查关键功能覆盖
    print('\n=== 功能覆盖检查 ===')
    
    # 核心功能检查
    core_features = {
        '用户认证': ['POST /api/v1/auth/login', 'POST /api/v1/auth/register', 'GET /api/v1/auth/me'],
        '小红书账号管理': ['GET /api/v1/xiaohongshu/accounts', 'POST /api/v1/xiaohongshu/accounts'],
        '笔记管理': ['GET /api/v1/notes', 'POST /api/v1/notes', 'PUT /api/v1/notes/{id}', 'DELETE /api/v1/notes/{id}'],
        '留言管理': ['GET /api/v1/comments', 'POST /api/v1/comments'],
        '爬虫功能': ['GET /api/v1/crawler/status', 'POST /api/v1/crawler/start'],
        'AI回复': ['POST /api/v1/ai/generate-reply', 'GET /api/v1/ai/config'],
        '数据分析': ['GET /api/v1/analytics/dashboard', 'GET /api/v1/analytics/stats'],
        '自动化': ['GET /api/v1/automation/rules', 'POST /api/v1/automation/rules']
    }
    
    # 检查每个功能是否有对应的端点
    all_paths = [route['path'] for route in routes]
    all_methods_paths = [f"{method} {route['path']}" for route in routes for method in route['methods']]
    
    for feature, expected_endpoints in core_features.items():
        print(f'\n🔍 {feature}:')
        for endpoint in expected_endpoints:
            if endpoint in all_methods_paths:
                print(f'  ✅ {endpoint}')
            else:
                # 检查是否有类似的端点
                method, path = endpoint.split(' ', 1)
                similar_found = False
                for route in routes:
                    if method in route['methods'] and path.replace('{id}', '{') in route['path']:
                        print(f'  ✅ {endpoint} (实际: {method} {route["path"]})')
                        similar_found = True
                        break
                if not similar_found:
                    print(f'  ❌ {endpoint} - 缺失')

if __name__ == "__main__":
    analyze_api_endpoints()
