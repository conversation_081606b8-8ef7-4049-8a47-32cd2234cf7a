"""
数据模型模块
包含所有数据库表的定义
"""
from .database import Base, engine, SessionLocal, get_db, create_tables, drop_tables
from .user import User, <PERSON><PERSON>shuAccount
from .note import Note, Comment, NoteStatus, CommentStatus
from .reply_rule import ReplyRule, ReplyLog, SystemLog, RuleType
from .reply_template import ReplyTemplate, AIReply, AIRule, AIConfig
from .crawler_task import CrawlerTask, CrawlerTaskLog, CrawlerConfig, TaskStatus, TaskType

# 导出所有模型
__all__ = [
    "Base",
    "engine",
    "SessionLocal",
    "get_db",
    "create_tables",
    "drop_tables",
    "User",
    "XiaohongshuAccount",
    "Note",
    "Comment",
    "NoteStatus",
    "CommentStatus",
    "ReplyRule",
    "ReplyLog",
    "SystemLog",
    "RuleType",
    "ReplyTemplate",
    "AIReply",
    "AIRule",
    "AIConfig",
    "CrawlerTask",
    "CrawlerTaskLog",
    "CrawlerConfig",
    "TaskStatus",
    "TaskType"
]
