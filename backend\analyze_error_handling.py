#!/usr/bin/env python3
"""
错误处理和日志分析脚本
"""
import sys
import os
import re
sys.path.append('.')

def check_global_exception_handling():
    """检查全局异常处理"""
    print('=== 全局异常处理检查 ===')
    
    issues = []
    recommendations = []
    
    # 检查main.py中的异常处理器
    try:
        with open('app/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查HTTP异常处理器
        if '@app.exception_handler(HTTPException)' in content:
            print("✅ HTTP异常处理器已配置")
        else:
            issues.append("❌ 缺少HTTP异常处理器")
            
        # 检查通用异常处理器
        if '@app.exception_handler(Exception)' in content:
            print("✅ 通用异常处理器已配置")
        else:
            issues.append("❌ 缺少通用异常处理器")
            
        # 检查是否记录异常日志
        if 'logging.error' in content:
            print("✅ 异常日志记录已配置")
        else:
            recommendations.append("⚠️ 建议在异常处理中添加日志记录")
            
    except FileNotFoundError:
        issues.append("❌ 无法找到main.py文件")
    
    return issues, recommendations

def check_api_error_handling():
    """检查API错误处理"""
    print('\n=== API错误处理检查 ===')
    
    issues = []
    recommendations = []
    
    # 检查响应格式统一
    try:
        from app.api.responses import success_response, error_response, ErrorResponse
        print("✅ 统一响应格式已实现")
        
        # 检查错误响应模型
        error_resp = ErrorResponse(success=False, message="test")
        required_fields = ['success', 'message', 'error_code', 'details']
        
        for field in required_fields:
            if hasattr(error_resp, field):
                print(f"✅ 错误响应包含 {field} 字段")
            else:
                recommendations.append(f"⚠️ 建议在错误响应中添加 {field} 字段")
                
    except ImportError:
        issues.append("❌ 响应格式模块缺失")
    
    # 检查API路由中的异常处理
    api_files = []
    api_dir = "app/api/v1"
    if os.path.exists(api_dir):
        for file in os.listdir(api_dir):
            if file.endswith('.py') and file != '__init__.py':
                api_files.append(os.path.join(api_dir, file))
    
    exception_handling_count = 0
    total_endpoints = 0
    
    for file_path in api_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 统计端点数量
            endpoint_count = len(re.findall(r'@router\.(get|post|put|delete|patch)', content))
            total_endpoints += endpoint_count
            
            # 检查异常处理
            if 'try:' in content and 'except' in content:
                exception_handling_count += 1
                
        except Exception as e:
            recommendations.append(f"⚠️ 无法分析文件 {file_path}: {e}")
    
    if total_endpoints > 0:
        coverage = (exception_handling_count / len(api_files)) * 100
        print(f"✅ API文件异常处理覆盖率: {coverage:.1f}% ({exception_handling_count}/{len(api_files)})")
        
        if coverage < 80:
            recommendations.append("⚠️ 建议提高API异常处理覆盖率")
    
    return issues, recommendations

def check_logging_configuration():
    """检查日志配置"""
    print('\n=== 日志配置检查 ===')
    
    issues = []
    recommendations = []
    
    # 检查配置文件中的日志设置
    try:
        from app.core.config import settings
        
        # 检查日志级别
        if hasattr(settings, 'LOG_LEVEL'):
            print(f"✅ 日志级别配置: {settings.LOG_LEVEL}")
            
            if settings.LOG_LEVEL.upper() not in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:
                recommendations.append("⚠️ 日志级别配置不标准")
        else:
            recommendations.append("⚠️ 缺少日志级别配置")
            
        # 检查日志文件配置
        if hasattr(settings, 'LOG_FILE'):
            print(f"✅ 日志文件配置: {settings.LOG_FILE}")
        else:
            recommendations.append("⚠️ 缺少日志文件配置")
            
    except Exception as e:
        issues.append(f"❌ 无法检查日志配置: {e}")
    
    # 检查日志目录
    log_dir = "logs"
    if os.path.exists(log_dir):
        print(f"✅ 日志目录存在: {log_dir}")
    else:
        recommendations.append("⚠️ 建议创建日志目录")
    
    return issues, recommendations

def check_error_monitoring():
    """检查错误监控"""
    print('\n=== 错误监控检查 ===')
    
    issues = []
    recommendations = []
    
    # 检查系统日志模型
    try:
        from app.models.user import SystemLog
        print("✅ 系统日志模型存在")
        
        # 检查日志字段
        log_fields = ['level', 'module', 'action', 'message', 'details', 'user_id', 'created_at']
        for field in log_fields:
            if hasattr(SystemLog, field):
                print(f"✅ 系统日志包含 {field} 字段")
            else:
                recommendations.append(f"⚠️ 建议在系统日志中添加 {field} 字段")
                
    except ImportError:
        recommendations.append("⚠️ 建议添加系统日志模型")
    
    # 检查健康检查端点
    try:
        from app.api.v1.health import router
        print("✅ 健康检查模块存在")
    except ImportError:
        recommendations.append("⚠️ 建议添加健康检查端点")
    
    return issues, recommendations

def check_validation_errors():
    """检查数据验证错误处理"""
    print('\n=== 数据验证错误检查 ===')
    
    issues = []
    recommendations = []
    
    # 检查Pydantic模式
    schema_dir = "app/api/schemas"
    if os.path.exists(schema_dir):
        schema_files = [f for f in os.listdir(schema_dir) if f.endswith('.py') and f != '__init__.py']
        print(f"✅ 找到 {len(schema_files)} 个数据验证模式")
        
        # 检查验证器使用
        validator_count = 0
        for file in schema_files:
            try:
                with open(os.path.join(schema_dir, file), 'r', encoding='utf-8') as f:
                    content = f.read()
                    if '@validator' in content:
                        validator_count += 1
            except:
                continue
                
        if validator_count > 0:
            print(f"✅ {validator_count} 个模式文件使用了自定义验证器")
        else:
            recommendations.append("⚠️ 建议添加自定义数据验证器")
            
    else:
        issues.append("❌ 数据验证模式目录不存在")
    
    return issues, recommendations

def check_rate_limiting():
    """检查限流处理"""
    print('\n=== 限流处理检查 ===')
    
    issues = []
    recommendations = []
    
    # 检查限流配置
    try:
        from app.core.config import settings
        
        # 查找限流相关配置
        rate_limit_configs = []
        for attr in dir(settings):
            if 'rate' in attr.lower() or 'limit' in attr.lower():
                rate_limit_configs.append(attr)
        
        if rate_limit_configs:
            print(f"✅ 找到限流配置: {', '.join(rate_limit_configs)}")
        else:
            recommendations.append("⚠️ 建议添加API限流配置")
            
    except Exception as e:
        recommendations.append(f"⚠️ 无法检查限流配置: {e}")
    
    # 检查爬虫限流
    try:
        from app.crawler.crawler_strategy import RequestRateLimiter
        print("✅ 爬虫限流机制存在")
    except ImportError:
        recommendations.append("⚠️ 建议为爬虫添加限流机制")
    
    return issues, recommendations

def generate_error_handling_report():
    """生成错误处理报告"""
    print('🔧 后端错误处理和日志检测报告')
    print('=' * 50)
    
    all_issues = []
    all_recommendations = []
    
    # 执行各项检查
    checks = [
        check_global_exception_handling,
        check_api_error_handling,
        check_logging_configuration,
        check_error_monitoring,
        check_validation_errors,
        check_rate_limiting
    ]
    
    for check_func in checks:
        try:
            issues, recs = check_func()
            all_issues.extend(issues)
            all_recommendations.extend(recs)
        except Exception as e:
            all_issues.append(f"❌ 检查函数 {check_func.__name__} 执行失败: {e}")
    
    # 生成总结
    print('\n' + '=' * 50)
    print('📊 错误处理检测总结')
    print('=' * 50)
    
    if all_issues:
        print(f'\n🚨 发现 {len(all_issues)} 个问题:')
        for issue in all_issues:
            print(f'  {issue}')
    else:
        print('\n✅ 未发现严重问题')
    
    if all_recommendations:
        print(f'\n💡 {len(all_recommendations)} 个改进建议:')
        for rec in all_recommendations[:10]:  # 只显示前10个
            print(f'  {rec}')
        if len(all_recommendations) > 10:
            print(f'  ... 还有 {len(all_recommendations) - 10} 个建议')
    
    # 错误处理评分
    total_checks = 15  # 假设总共15个检查项
    issues_weight = 5
    recommendations_weight = 1
    
    score = max(0, 100 - (len(all_issues) * issues_weight + len(all_recommendations) * recommendations_weight))
    
    print(f'\n🎯 错误处理评分: {score}/100')
    
    if score >= 90:
        print('🟢 错误处理状况: 优秀')
    elif score >= 80:
        print('🟡 错误处理状况: 良好')
    elif score >= 70:
        print('🟠 错误处理状况: 一般')
    else:
        print('🔴 错误处理状况: 需要改进')
    
    return len(all_issues), len(all_recommendations), score

if __name__ == "__main__":
    generate_error_handling_report()
