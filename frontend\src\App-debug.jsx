import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { useAuthStore } from './stores/authStore';

console.log('App-debug.jsx 开始加载');

// 测试直接导入页面组件（同步）
console.log('开始同步导入页面组件...');
try {
  console.log('导入 LoginPage...');
  import('./pages/Auth/LoginPage').then(() => console.log('LoginPage 异步导入成功')).catch(e => console.error('LoginPage 导入失败:', e));

  console.log('导入 DashboardPage...');
  import('./pages/Dashboard/DashboardPage').then(() => console.log('DashboardPage 异步导入成功')).catch(e => console.error('DashboardPage 导入失败:', e));

  console.log('导入 ProtectedRoute...');
  import('./components/Auth/ProtectedRoute').then(() => console.log('ProtectedRoute 异步导入成功')).catch(e => console.error('ProtectedRoute 导入失败:', e));

  console.log('导入 MainLayout...');
  import('./components/Layout/MainLayout').then(() => console.log('MainLayout 异步导入成功')).catch(e => console.error('MainLayout 导入失败:', e));

} catch (error) {
  console.error('页面组件导入失败:', error);
}

// 第三步：测试React Router + Ant Design
function AppDebug() {
  console.log('AppDebug 组件渲染');

  try {
    const { initAuth } = useAuthStore();
    console.log('authStore 获取成功');

    useEffect(() => {
      console.log('开始调用 initAuth');
      initAuth();
      console.log('initAuth 调用完成');
    }, [initAuth]);

  } catch (error) {
    console.error('authStore 错误:', error);
    return (
      <div style={{ padding: '20px', color: 'red' }}>
        <h1>❌ authStore 错误</h1>
        <p>{error.message}</p>
      </div>
    );
  }

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 6,
        },
      }}
    >
      <AntdApp>
        <Router>
          <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
            <h1>🔍 App调试版本 - 测试Router + Antd</h1>
            <p>基础App组件正常工作</p>
            <p>authStore 导入和使用正常</p>
            <p>React Router 和 Ant Design 配置正常</p>
            <Routes>
              <Route path="/" element={<div>主页路由正常</div>} />
              <Route path="/test" element={<div>测试路由正常</div>} />
            </Routes>
          </div>
        </Router>
      </AntdApp>
    </ConfigProvider>
  );
}

console.log('App-debug.jsx 加载完成');

export default AppDebug;
