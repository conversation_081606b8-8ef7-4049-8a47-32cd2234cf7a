#!/usr/bin/env python3
"""
测试小红书账号API
"""
import requests
import json

# API配置
BASE_URL = "http://localhost:8000/api/v1"
TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************.zmspM5MCpV40B7-nYuDuQSZgAy51EFcIe2qP3gda9aw"

# 请求头
headers = {
    "Authorization": f"Bearer {TOKEN}",
    "Content-Type": "application/json"
}

def test_get_accounts():
    """测试获取账号列表"""
    print("🔍 测试获取账号列表...")
    
    try:
        response = requests.get(f"{BASE_URL}/xiaohongshu/accounts", headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 账号列表获取成功!")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return data.get('data', [])
        else:
            print(f"❌ 账号列表获取失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return []

def test_create_account():
    """测试创建账号"""
    print("\n🔍 测试创建账号...")
    
    account_data = {
        "account_name": "API测试账号",
        "account_id": "test_user_001",
        "login_phone": "***********",
        "login_email": "<EMAIL>"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/xiaohongshu/accounts", headers=headers, json=account_data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code in [200, 201]:
            data = response.json()
            print(f"✅ 账号创建成功!")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return data.get('data', {}).get('id') if 'data' in data else None
        else:
            print(f"❌ 账号创建失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_update_account(account_id):
    """测试更新账号"""
    if not account_id:
        print("\n⚠️ 跳过更新测试 - 没有有效的账号ID")
        return False
        
    print(f"\n🔍 测试更新账号 (ID: {account_id})...")
    
    update_data = {
        "account_name": "API测试账号 - 已更新",
        "login_phone": "***********"
    }
    
    try:
        response = requests.put(f"{BASE_URL}/xiaohongshu/accounts/{account_id}", headers=headers, json=update_data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 账号更新成功!")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 账号更新失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_get_account_detail(account_id):
    """测试获取账号详情"""
    if not account_id:
        print("\n⚠️ 跳过详情测试 - 没有有效的账号ID")
        return False
        
    print(f"\n🔍 测试获取账号详情 (ID: {account_id})...")
    
    try:
        response = requests.get(f"{BASE_URL}/xiaohongshu/accounts/{account_id}", headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 账号详情获取成功!")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 账号详情获取失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_toggle_account_status(account_id):
    """测试切换账号状态"""
    if not account_id:
        print("\n⚠️ 跳过状态切换测试 - 没有有效的账号ID")
        return False
        
    print(f"\n🔍 测试停用账号 (ID: {account_id})...")
    
    try:
        response = requests.post(f"{BASE_URL}/xiaohongshu/accounts/{account_id}/deactivate", headers=headers)
        print(f"停用状态码: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ 账号停用成功!")
        else:
            print(f"❌ 账号停用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
        
        # 测试激活
        print(f"\n🔍 测试激活账号 (ID: {account_id})...")
        response = requests.post(f"{BASE_URL}/xiaohongshu/accounts/{account_id}/activate", headers=headers)
        print(f"激活状态码: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ 账号激活成功!")
            return True
        else:
            print(f"❌ 账号激活失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_delete_account(account_id):
    """测试删除账号"""
    if not account_id:
        print("\n⚠️ 跳过删除测试 - 没有有效的账号ID")
        return False
        
    print(f"\n🔍 测试删除账号 (ID: {account_id})...")
    
    try:
        response = requests.delete(f"{BASE_URL}/xiaohongshu/accounts/{account_id}", headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code in [200, 204]:
            print(f"✅ 账号删除成功!")
            return True
        else:
            print(f"❌ 账号删除失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始小红书账号API测试...")
    print("=" * 50)
    
    # 1. 测试获取现有账号
    print("1️⃣ 获取现有账号列表")
    existing_accounts = test_get_accounts()
    
    # 2. 测试创建新账号
    print("\n" + "=" * 50)
    print("2️⃣ 创建新账号")
    new_account_id = test_create_account()
    
    # 3. 再次获取账号列表，验证创建
    print("\n" + "=" * 50)
    print("3️⃣ 验证账号创建")
    updated_accounts = test_get_accounts()
    
    if len(updated_accounts) > len(existing_accounts):
        print(f"✅ 账号数量增加: {len(existing_accounts)} -> {len(updated_accounts)}")
    else:
        print(f"⚠️ 账号数量未变化: {len(existing_accounts)} -> {len(updated_accounts)}")
    
    # 4. 测试获取账号详情
    print("\n" + "=" * 50)
    print("4️⃣ 获取账号详情")
    detail_success = test_get_account_detail(new_account_id)
    
    # 5. 测试更新账号
    print("\n" + "=" * 50)
    print("5️⃣ 更新账号")
    update_success = test_update_account(new_account_id)
    
    # 6. 测试状态切换
    print("\n" + "=" * 50)
    print("6️⃣ 状态切换")
    status_success = test_toggle_account_status(new_account_id)
    
    # 7. 测试删除账号
    print("\n" + "=" * 50)
    print("7️⃣ 删除账号")
    delete_success = test_delete_account(new_account_id)
    
    # 8. 最终验证
    print("\n" + "=" * 50)
    print("8️⃣ 最终验证")
    final_accounts = test_get_accounts()
    
    print("\n" + "=" * 50)
    print("✅ 小红书账号API测试完成!")
    print(f"📊 测试结果总结:")
    print(f"   初始账号数: {len(existing_accounts)}")
    print(f"   创建后账号数: {len(updated_accounts)}")
    print(f"   最终账号数: {len(final_accounts)}")
    print(f"   创建操作: {'✅ 成功' if new_account_id else '❌ 失败'}")
    print(f"   详情操作: {'✅ 成功' if detail_success else '❌ 失败'}")
    print(f"   更新操作: {'✅ 成功' if update_success else '❌ 失败'}")
    print(f"   状态切换: {'✅ 成功' if status_success else '❌ 失败'}")
    print(f"   删除操作: {'✅ 成功' if delete_success else '❌ 失败'}")

if __name__ == "__main__":
    main()
