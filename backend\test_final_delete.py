#!/usr/bin/env python3
"""
最终删除功能测试
验证笔记删除功能是否正常工作，包括级联删除留言
"""
import requests
import json

# API配置
BASE_URL = "http://localhost:8000/api/v1"

def get_auth_token():
    """获取认证token"""
    try:
        login_data = {
            'username': 'testuser2',
            'password': 'testpass123'
        }
        
        response = requests.post(f'{BASE_URL}/auth/login', data=login_data)
        if response.status_code == 200:
            data = response.json()
            return data['data']['access_token']
        
        print(f"登录失败: {response.text}")
        return None
        
    except Exception as e:
        print(f"获取token异常: {e}")
        return None

def get_notes(headers):
    """获取笔记列表"""
    print("🔍 获取笔记列表...")
    
    try:
        response = requests.get(f"{BASE_URL}/notes", headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 笔记列表获取成功!")
            notes = data.get('data', [])
            print(f"笔记数量: {len(notes)}")
            for note in notes:
                print(f"  - 笔记ID: {note.get('id')}, 标题: {note.get('title', 'N/A')}")
            return notes
        else:
            print(f"❌ 笔记列表获取失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return []

def delete_note(headers, note_id):
    """删除笔记"""
    print(f"\n🗑️ 删除笔记 ID: {note_id}...")
    
    try:
        response = requests.delete(f"{BASE_URL}/notes/{note_id}", headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ 笔记删除成功!")
            return True
        else:
            print(f"❌ 笔记删除失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 最终删除功能测试...")
    print("=" * 50)
    
    # 获取认证token
    print("0️⃣ 获取认证token")
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证token，测试终止")
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    print(f"✅ 认证token获取成功")
    
    # 获取删除前的笔记列表
    print("\n" + "=" * 50)
    print("1️⃣ 删除前 - 检查笔记列表")
    notes_before = get_notes(headers)
    
    if not notes_before:
        print("❌ 没有找到笔记，无法进行删除测试")
        return
    
    # 选择要删除的笔记（选择第一个）
    target_note = notes_before[0]
    target_note_id = target_note.get('id')
    target_note_title = target_note.get('title', 'N/A')
    
    print(f"\n📌 选择删除目标:")
    print(f"   笔记ID: {target_note_id}")
    print(f"   笔记标题: {target_note_title}")
    
    # 执行删除操作
    print("\n" + "=" * 50)
    print("2️⃣ 执行删除操作")
    delete_success = delete_note(headers, target_note_id)
    
    if not delete_success:
        print("❌ 删除操作失败，测试终止")
        return
    
    # 获取删除后的笔记列表
    print("\n" + "=" * 50)
    print("3️⃣ 删除后 - 检查笔记列表")
    notes_after = get_notes(headers)
    
    # 验证删除结果
    print("\n" + "=" * 50)
    print("4️⃣ 验证删除结果")
    
    notes_count_before = len(notes_before)
    notes_count_after = len(notes_after)
    
    print(f"删除前笔记数量: {notes_count_before}")
    print(f"删除后笔记数量: {notes_count_after}")
    
    # 检查目标笔记是否被删除
    target_still_exists = any(note.get('id') == target_note_id for note in notes_after)
    
    if target_still_exists:
        print(f"❌ 删除失败！目标笔记 {target_note_id} 仍然存在")
    else:
        print(f"✅ 删除成功！目标笔记 {target_note_id} 已被删除")
    
    if notes_count_after == notes_count_before - 1:
        print("✅ 笔记数量正确减少了1个")
    else:
        print(f"⚠️ 笔记数量变化异常：期望减少1个，实际变化 {notes_count_before - notes_count_after} 个")
    
    # 最终结果
    print("\n" + "=" * 50)
    print("🎯 测试结果总结:")
    
    if not target_still_exists and notes_count_after == notes_count_before - 1:
        print("🎉 删除功能测试通过！")
        print("   ✅ 目标笔记已被成功删除")
        print("   ✅ 笔记数量正确减少")
        print("   ✅ 没有出现外键约束错误")
        print("   ✅ 级联删除功能正常工作")
    else:
        print("❌ 删除功能测试失败！")
        if target_still_exists:
            print("   ❌ 目标笔记未被删除")
        if notes_count_after != notes_count_before - 1:
            print("   ❌ 笔记数量变化异常")

if __name__ == "__main__":
    main()
