import asyncio
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from datetime import datetime
from loguru import logger

import openai
from sqlalchemy.orm import Session

from ..core.config import settings
from ..models.reply_template import AIConfig
from .ollama_service import OllamaService


class AIProvider(str, Enum):
    """AI提供商枚举"""
    OPENAI = "openai"
    OLLAMA = "ollama"
    ANTHROPIC = "anthropic"


class AIProviderManager:
    """AI提供商管理器，统一管理不同的AI服务"""
    
    def __init__(self, db: Session, user_id: int):
        self.db = db
        self.user_id = user_id
        self.config = self._get_ai_config()
        self.ollama_service = None
    
    def _get_ai_config(self) -> AIConfig:
        """获取AI配置"""
        config = self.db.query(AIConfig).filter(
            AIConfig.user_id == self.user_id
        ).first()
        
        if not config:
            # 创建默认配置
            config = AIConfig(
                user_id=self.user_id,
                provider=settings.AI_PROVIDER or AIProvider.OLLAMA,
                api_key=settings.OPENAI_API_KEY,
                default_model=self._get_default_model(),
                temperature="0.7",
                max_tokens=150,
                reply_language="zh",
                reply_tone="friendly",
                ollama_base_url=settings.OLLAMA_BASE_URL,
                ollama_model=settings.OLLAMA_MODEL
            )
            self.db.add(config)
            self.db.commit()
            self.db.refresh(config)
        
        return config
    
    def _get_default_model(self) -> str:
        """根据提供商获取默认模型"""
        provider = settings.AI_PROVIDER or AIProvider.OLLAMA
        if provider == AIProvider.OPENAI:
            return settings.OPENAI_MODEL or "gpt-3.5-turbo"
        elif provider == AIProvider.OLLAMA:
            return settings.OLLAMA_MODEL or "llama2"
        elif provider == AIProvider.ANTHROPIC:
            return "claude-3-sonnet-20240229"
        return "llama2"
    
    async def generate_reply(
        self,
        comment_content: str,
        context: Optional[Dict] = None,
        template_id: Optional[int] = None,
        provider: Optional[AIProvider] = None
    ) -> Dict[str, Any]:
        """生成AI回复"""
        try:
            # 确定使用的提供商
            active_provider = provider or AIProvider(self.config.provider)
            
            # 构建提示词
            prompt = self._build_prompt(comment_content, context, template_id)
            
            # 根据提供商调用相应的API
            if active_provider == AIProvider.OPENAI:
                return await self._generate_with_openai(prompt)
            elif active_provider == AIProvider.OLLAMA:
                return await self._generate_with_ollama(prompt)
            elif active_provider == AIProvider.ANTHROPIC:
                return await self._generate_with_anthropic(prompt)
            else:
                return {
                    "success": False,
                    "error": f"不支持的AI提供商: {active_provider}",
                    "reply": None
                }
                
        except Exception as e:
            logger.error(f"AI回复生成失败: {str(e)}")
            return {
                "success": False,
                "error": f"生成失败: {str(e)}",
                "reply": None
            }
    
    async def _generate_with_openai(self, prompt: str) -> Dict[str, Any]:
        """使用OpenAI生成回复"""
        try:
            if not self.config.api_key:
                return {
                    "success": False,
                    "error": "OpenAI API密钥未配置",
                    "reply": None
                }
            
            openai.api_key = self.config.api_key
            if self.config.api_base_url:
                openai.api_base = self.config.api_base_url
            
            response = await openai.ChatCompletion.acreate(
                model=self.config.default_model,
                messages=[
                    {"role": "system", "content": "你是一个专业的小红书客服助手。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=float(self.config.temperature),
                max_tokens=self.config.max_tokens,
                top_p=1,
                frequency_penalty=0,
                presence_penalty=0
            )
            
            reply_content = response.choices[0].message.content.strip()
            
            return {
                "success": True,
                "reply": reply_content,
                "provider": AIProvider.OPENAI,
                "model": self.config.default_model,
                "tokens_used": response.usage.total_tokens,
                "raw_response": response
            }
            
        except Exception as e:
            logger.error(f"OpenAI API调用失败: {str(e)}")
            return {
                "success": False,
                "error": f"OpenAI调用失败: {str(e)}",
                "reply": None
            }
    
    async def _generate_with_ollama(self, prompt: str) -> Dict[str, Any]:
        """使用Ollama生成回复"""
        try:
            if not self.ollama_service:
                self.ollama_service = OllamaService(
                    base_url=self.config.ollama_base_url or settings.OLLAMA_BASE_URL,
                    timeout=settings.OLLAMA_TIMEOUT or 60
                )
            
            # 使用聊天补全API
            messages = [
                {"role": "system", "content": "你是一个专业的小红书客服助手。"},
                {"role": "user", "content": prompt}
            ]
            
            async with self.ollama_service as service:
                response = await service.chat_completion(
                    model=self.config.ollama_model or settings.OLLAMA_MODEL or "llama2",
                    messages=messages,
                    temperature=float(self.config.temperature),
                    max_tokens=self.config.max_tokens
                )
            
            if response["success"]:
                reply_content = response["message"].get("content", "").strip()
                return {
                    "success": True,
                    "reply": reply_content,
                    "provider": AIProvider.OLLAMA,
                    "model": self.config.ollama_model or settings.OLLAMA_MODEL,
                    "eval_count": response.get("eval_count"),
                    "eval_duration": response.get("eval_duration"),
                    "raw_response": response
                }
            else:
                return {
                    "success": False,
                    "error": response.get("error", "Ollama调用失败"),
                    "reply": None
                }
                
        except Exception as e:
            logger.error(f"Ollama调用失败: {str(e)}")
            return {
                "success": False,
                "error": f"Ollama调用失败: {str(e)}",
                "reply": None
            }
    
    async def _generate_with_anthropic(self, prompt: str) -> Dict[str, Any]:
        """使用Anthropic生成回复"""
        # TODO: 实现Anthropic API调用
        return {
            "success": False,
            "error": "Anthropic支持尚未实现",
            "reply": None
        }
    
    def _build_prompt(
        self, 
        comment_content: str, 
        context: Optional[Dict] = None,
        template_id: Optional[int] = None
    ) -> str:
        """构建AI提示词"""
        
        # 基础提示词
        base_prompt = f"""
你是一个专业的小红书客服助手，需要为用户留言生成合适的回复。

回复要求：
1. 语言：{self.config.reply_language}
2. 语调：{self.config.reply_tone}
3. 长度：简洁明了，不超过100字
4. 风格：友好、专业、有帮助
"""
        
        if self.config.include_emoji:
            base_prompt += "5. 可以适当使用表情符号\n"
        
        # 添加模板信息
        if template_id:
            from ..models.reply_template import ReplyTemplate
            template = self.db.query(ReplyTemplate).filter(
                ReplyTemplate.id == template_id,
                ReplyTemplate.user_id == self.user_id
            ).first()
            
            if template:
                base_prompt += f"\n参考模板：{template.content}\n"
        
        # 添加上下文信息
        if context:
            if context.get("note_title"):
                base_prompt += f"\n笔记标题：{context['note_title']}\n"
            if context.get("note_category"):
                base_prompt += f"笔记分类：{context['note_category']}\n"
        
        # 用户留言
        prompt = f"{base_prompt}\n用户留言：{comment_content}\n\n请生成一个合适的回复："
        
        return prompt
    
    async def test_connection(self, provider: Optional[AIProvider] = None) -> Dict[str, Any]:
        """测试AI提供商连接"""
        test_provider = provider or AIProvider(self.config.provider)
        
        try:
            if test_provider == AIProvider.OPENAI:
                return await self._test_openai_connection()
            elif test_provider == AIProvider.OLLAMA:
                return await self._test_ollama_connection()
            elif test_provider == AIProvider.ANTHROPIC:
                return await self._test_anthropic_connection()
            else:
                return {
                    "success": False,
                    "error": f"不支持的提供商: {test_provider}"
                }
        except Exception as e:
            return {
                "success": False,
                "error": f"连接测试失败: {str(e)}"
            }
    
    async def _test_openai_connection(self) -> Dict[str, Any]:
        """测试OpenAI连接"""
        try:
            if not self.config.api_key:
                return {
                    "success": False,
                    "error": "API密钥未配置"
                }
            
            openai.api_key = self.config.api_key
            if self.config.api_base_url:
                openai.api_base = self.config.api_base_url
            
            # 发送测试请求
            response = await openai.ChatCompletion.acreate(
                model=self.config.default_model,
                messages=[{"role": "user", "content": "测试连接"}],
                max_tokens=10
            )
            
            return {
                "success": True,
                "provider": AIProvider.OPENAI,
                "model": self.config.default_model,
                "message": "OpenAI连接正常"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"OpenAI连接失败: {str(e)}"
            }
    
    async def _test_ollama_connection(self) -> Dict[str, Any]:
        """测试Ollama连接"""
        try:
            if not self.ollama_service:
                self.ollama_service = OllamaService(
                    base_url=self.config.ollama_base_url or settings.OLLAMA_BASE_URL
                )
            
            async with self.ollama_service as service:
                health_check = await service.check_health()
                
                if health_check["success"]:
                    return {
                        "success": True,
                        "provider": AIProvider.OLLAMA,
                        "url": health_check["url"],
                        "message": "Ollama连接正常"
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Ollama连接失败: {health_check.get('error', '未知错误')}"
                    }
                    
        except Exception as e:
            return {
                "success": False,
                "error": f"Ollama连接失败: {str(e)}"
            }
    
    async def _test_anthropic_connection(self) -> Dict[str, Any]:
        """测试Anthropic连接"""
        return {
            "success": False,
            "error": "Anthropic支持尚未实现"
        }
    
    async def get_available_models(self, provider: Optional[AIProvider] = None) -> Dict[str, Any]:
        """获取可用模型列表"""
        target_provider = provider or AIProvider(self.config.provider)
        
        try:
            if target_provider == AIProvider.OPENAI:
                return {
                    "success": True,
                    "provider": AIProvider.OPENAI,
                    "models": [
                        {"id": "gpt-3.5-turbo", "name": "GPT-3.5 Turbo"},
                        {"id": "gpt-4", "name": "GPT-4"},
                        {"id": "gpt-4-turbo", "name": "GPT-4 Turbo"},
                        {"id": "gpt-4o", "name": "GPT-4o"},
                    ]
                }
            elif target_provider == AIProvider.OLLAMA:
                if not self.ollama_service:
                    self.ollama_service = OllamaService(
                        base_url=self.config.ollama_base_url or settings.OLLAMA_BASE_URL
                    )
                
                async with self.ollama_service as service:
                    models_result = await service.list_models()
                    
                    if models_result["success"]:
                        return {
                            "success": True,
                            "provider": AIProvider.OLLAMA,
                            "models": [
                                {
                                    "id": model["name"],
                                    "name": model["name"],
                                    "size": model.get("size"),
                                    "modified_at": model.get("modified_at")
                                }
                                for model in models_result["models"]
                            ]
                        }
                    else:
                        return models_result
            else:
                return {
                    "success": False,
                    "error": f"不支持的提供商: {target_provider}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"获取模型列表失败: {str(e)}"
            }
