#!/usr/bin/env python3
"""
测试数据CRUD操作
"""
import requests
import json

# API配置
BASE_URL = "http://localhost:8000/api/v1"
TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************.zmspM5MCpV40B7-nYuDuQSZgAy51EFcIe2qP3gda9aw"

# 请求头
headers = {
    "Authorization": f"Bearer {TOKEN}",
    "Content-Type": "application/json"
}

def get_account_id():
    """获取用户的第一个小红书账号ID"""
    try:
        response = requests.get(f"{BASE_URL}/xiaohongshu/accounts", headers=headers)
        if response.status_code == 200:
            data = response.json()
            accounts = data.get('data', [])
            if accounts:
                return accounts[0].get('id')
        return 1  # 默认使用ID为1的账号
    except:
        return 1  # 默认使用ID为1的账号

def test_add_note():
    """测试添加新笔记"""
    print("🔍 测试添加新笔记...")

    account_id = get_account_id()
    print(f"使用账号ID: {account_id}")

    note_data = {
        "note_url": "https://www.xiaohongshu.com/explore/test123",
        "account_id": account_id,
        "title": "API测试笔记",
        "crawl_interval": 300,
        "auto_reply_enabled": True
    }
    
    try:
        response = requests.post(f"{BASE_URL}/notes", headers=headers, json=note_data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code in [200, 201]:
            data = response.json()
            print(f"✅ 笔记添加成功!")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return data.get('data', {}).get('id') if 'data' in data else None
        else:
            print(f"❌ 笔记添加失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_get_notes():
    """测试获取笔记列表"""
    print("\n🔍 测试获取笔记列表...")
    
    try:
        response = requests.get(f"{BASE_URL}/notes", headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 笔记列表获取成功!")
            notes = data.get('data', [])
            print(f"笔记数量: {len(notes)}")
            
            for i, note in enumerate(notes[:3]):  # 只显示前3个
                print(f"  笔记 {i+1}: {note.get('title', 'N/A')} (ID: {note.get('id', 'N/A')})")
            
            return notes
        else:
            print(f"❌ 笔记列表获取失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return []

def test_update_note(note_id):
    """测试更新笔记"""
    if not note_id:
        print("\n⚠️ 跳过更新测试 - 没有有效的笔记ID")
        return False

    print(f"\n🔍 测试更新笔记 (ID: {note_id})...")

    update_data = {
        "title": "API测试笔记 - 已更新",
        "crawl_interval": 600,
        "auto_reply_enabled": False
    }
    
    try:
        response = requests.put(f"{BASE_URL}/notes/{note_id}", headers=headers, json=update_data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 笔记更新成功!")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 笔记更新失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_delete_note(note_id):
    """测试删除笔记"""
    if not note_id:
        print("\n⚠️ 跳过删除测试 - 没有有效的笔记ID")
        return False
        
    print(f"\n🔍 测试删除笔记 (ID: {note_id})...")
    
    try:
        response = requests.delete(f"{BASE_URL}/notes/{note_id}", headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code in [200, 204]:
            print(f"✅ 笔记删除成功!")
            return True
        else:
            print(f"❌ 笔记删除失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始数据CRUD操作测试...")
    print("=" * 50)
    
    # 1. 测试获取现有笔记
    print("1️⃣ 获取现有笔记列表")
    existing_notes = test_get_notes()
    
    # 2. 测试添加新笔记
    print("\n" + "=" * 50)
    print("2️⃣ 添加新笔记")
    new_note_id = test_add_note()
    
    # 3. 再次获取笔记列表，验证添加
    print("\n" + "=" * 50)
    print("3️⃣ 验证笔记添加")
    updated_notes = test_get_notes()
    
    if len(updated_notes) > len(existing_notes):
        print(f"✅ 笔记数量增加: {len(existing_notes)} -> {len(updated_notes)}")
    else:
        print(f"⚠️ 笔记数量未变化: {len(existing_notes)} -> {len(updated_notes)}")
    
    # 4. 测试更新笔记
    print("\n" + "=" * 50)
    print("4️⃣ 更新笔记")
    update_success = test_update_note(new_note_id)
    
    # 5. 测试删除笔记
    print("\n" + "=" * 50)
    print("5️⃣ 删除笔记")
    delete_success = test_delete_note(new_note_id)
    
    # 6. 最终验证
    print("\n" + "=" * 50)
    print("6️⃣ 最终验证")
    final_notes = test_get_notes()
    
    print("\n" + "=" * 50)
    print("✅ 数据CRUD操作测试完成!")
    print(f"📊 测试结果总结:")
    print(f"   初始笔记数: {len(existing_notes)}")
    print(f"   添加后笔记数: {len(updated_notes)}")
    print(f"   最终笔记数: {len(final_notes)}")
    print(f"   添加操作: {'✅ 成功' if new_note_id else '❌ 失败'}")
    print(f"   更新操作: {'✅ 成功' if update_success else '❌ 失败'}")
    print(f"   删除操作: {'✅ 成功' if delete_success else '❌ 失败'}")

if __name__ == "__main__":
    main()
