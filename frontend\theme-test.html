<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题测试页面</title>
    <style>
        /* 主题CSS变量定义 */
        :root {
            /* 浅色主题默认变量 */
            --theme-primary: #1890ff;
            --theme-primary-hover: #40a9ff;
            --theme-primary-active: #096dd9;
            
            --theme-body-bg: #ffffff;
            --theme-component-bg: #ffffff;
            --theme-layout-bg: #f0f2f5;
            --theme-sider-bg: #ffffff;
            --theme-header-bg: #ffffff;
            
            --theme-text-primary: rgba(0, 0, 0, 0.85);
            --theme-text-secondary: rgba(0, 0, 0, 0.65);
            --theme-text-disabled: rgba(0, 0, 0, 0.25);
            
            --theme-border-color: #d9d9d9;
            --theme-border-color-split: #f0f0f0;
            
            --theme-success: #52c41a;
            --theme-warning: #faad14;
            --theme-error: #ff4d4f;
            --theme-info: #1890ff;
            
            --theme-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            --theme-card-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
        }

        /* 深色主题变量 */
        .dark-theme {
            --theme-primary: #1890ff;
            --theme-primary-hover: #40a9ff;
            --theme-primary-active: #096dd9;
            
            --theme-body-bg: #141414;
            --theme-component-bg: #1f1f1f;
            --theme-layout-bg: #000000;
            --theme-sider-bg: #001529;
            --theme-header-bg: #1f1f1f;
            
            --theme-text-primary: rgba(255, 255, 255, 0.85);
            --theme-text-secondary: rgba(255, 255, 255, 0.65);
            --theme-text-disabled: rgba(255, 255, 255, 0.25);
            
            --theme-border-color: #434343;
            --theme-border-color-split: #303030;
            
            --theme-success: #52c41a;
            --theme-warning: #faad14;
            --theme-error: #ff4d4f;
            --theme-info: #1890ff;
            
            --theme-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.45);
            --theme-card-shadow: 0 1px 3px rgba(0, 0, 0, 0.32), 0 1px 2px rgba(0, 0, 0, 0.44);
        }

        * {
            transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
        }

        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: var(--theme-body-bg);
            color: var(--theme-text-primary);
            line-height: 1.5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background: var(--theme-header-bg);
            padding: 20px;
            border-radius: 8px;
            box-shadow: var(--theme-card-shadow);
            margin-bottom: 20px;
            border: 1px solid var(--theme-border-color);
        }

        .card {
            background: var(--theme-component-bg);
            padding: 20px;
            border-radius: 8px;
            box-shadow: var(--theme-card-shadow);
            margin-bottom: 20px;
            border: 1px solid var(--theme-border-color);
        }

        .button {
            background: var(--theme-primary);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .button:hover {
            background: var(--theme-primary-hover);
        }

        .button.secondary {
            background: var(--theme-component-bg);
            color: var(--theme-text-primary);
            border: 1px solid var(--theme-border-color);
        }

        .button.secondary:hover {
            border-color: var(--theme-primary);
            color: var(--theme-primary);
        }

        .text-secondary {
            color: var(--theme-text-secondary);
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .preview-box {
            height: 60px;
            border-radius: 4px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .primary-box { background: var(--theme-primary); color: white; }
        .success-box { background: var(--theme-success); color: white; }
        .warning-box { background: var(--theme-warning); color: white; }
        .error-box { background: var(--theme-error); color: white; }
    </style>
</head>
<body>
    <div class="theme-toggle">
        <button class="button" onclick="toggleTheme()">🌓 切换主题</button>
    </div>

    <div class="container">
        <div class="header">
            <h1>🎨 主题系统测试页面</h1>
            <p class="text-secondary">测试深色主题和浅色主题的切换效果</p>
        </div>

        <div class="grid">
            <div class="card">
                <h3>🎯 主要颜色</h3>
                <div class="preview-box primary-box">主要色彩</div>
                <p>当前主题: <span id="current-theme">浅色主题</span></p>
                <button class="button">主要按钮</button>
                <button class="button secondary">次要按钮</button>
            </div>

            <div class="card">
                <h3>📝 文本样式</h3>
                <p>这是主要文本颜色</p>
                <p class="text-secondary">这是次要文本颜色</p>
                <p style="color: var(--theme-text-disabled)">这是禁用文本颜色</p>
            </div>

            <div class="card">
                <h3>🎨 状态颜色</h3>
                <div class="preview-box success-box">成功状态</div>
                <div class="preview-box warning-box">警告状态</div>
                <div class="preview-box error-box">错误状态</div>
            </div>

            <div class="card">
                <h3>🔧 组件样式</h3>
                <p>卡片背景: <code>var(--theme-component-bg)</code></p>
                <p>边框颜色: <code>var(--theme-border-color)</code></p>
                <p>阴影效果: <code>var(--theme-card-shadow)</code></p>
                <div style="border: 2px solid var(--theme-border-color); padding: 10px; border-radius: 4px; margin-top: 10px;">
                    边框示例
                </div>
            </div>

            <div class="card">
                <h3>📊 主题变量</h3>
                <div id="theme-variables">
                    <p>主要色彩: <span id="var-primary"></span></p>
                    <p>背景色: <span id="var-bg"></span></p>
                    <p>文本色: <span id="var-text"></span></p>
                    <p>边框色: <span id="var-border"></span></p>
                </div>
            </div>

            <div class="card">
                <h3>🌟 交互效果</h3>
                <p>所有元素都有平滑的过渡动画效果</p>
                <button class="button" onclick="showAlert()">测试交互</button>
                <button class="button secondary" onclick="changeColors()">随机颜色</button>
            </div>
        </div>
    </div>

    <script>
        let isDark = false;

        function toggleTheme() {
            isDark = !isDark;
            document.body.classList.toggle('dark-theme', isDark);
            document.getElementById('current-theme').textContent = isDark ? '深色主题' : '浅色主题';
            updateThemeVariables();
        }

        function updateThemeVariables() {
            const root = document.documentElement;
            const computedStyle = getComputedStyle(root);
            
            document.getElementById('var-primary').textContent = computedStyle.getPropertyValue('--theme-primary');
            document.getElementById('var-bg').textContent = computedStyle.getPropertyValue('--theme-component-bg');
            document.getElementById('var-text').textContent = computedStyle.getPropertyValue('--theme-text-primary');
            document.getElementById('var-border').textContent = computedStyle.getPropertyValue('--theme-border-color');
        }

        function showAlert() {
            alert('主题切换功能正常工作！当前主题: ' + (isDark ? '深色' : '浅色'));
        }

        function changeColors() {
            const colors = ['#1890ff', '#52c41a', '#faad14', '#ff4d4f', '#722ed1', '#13c2c2'];
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            document.documentElement.style.setProperty('--theme-primary', randomColor);
        }

        // 初始化
        updateThemeVariables();

        // 检测系统主题偏好
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            toggleTheme();
        }

        // 监听系统主题变化
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
                if (e.matches !== isDark) {
                    toggleTheme();
                }
            });
        }
    </script>
</body>
</html>
