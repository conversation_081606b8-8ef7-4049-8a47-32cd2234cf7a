---
description: 
globs: 
alwaysApply: true
---
# Cursor Rules for 小红书自动笔记留言回复工具

## 1. 代码结构与命名

- 目录结构应清晰分层，前后端分离，常用结构如下：
  ```
  /backend      # 后端代码
  /frontend     # 前端代码
  /docs         # 文档
  /scripts      # 自动化脚本
  /tests        # 测试代码
  ```
- 变量、函数、类命名采用有意义的英文，遵循驼峰（camelCase）或下划线（snake_case）风格，保持一致。

## 2. 代码提交与分支管理

- 主分支（main/master）仅用于发布稳定版本，开发请在 feature/bugfix 分支进行。
- 每次提交需附带清晰的 commit message，描述本次更改内容。
- 合并代码前需自测通过，推荐使用 Pull Request 流程，至少一人 Code Review。

## 3. 代码规范

- Python 代码遵循 PEP8 规范，推荐使用 `black`、`flake8` 工具自动格式化和检查。
- 前端代码遵循 ESLint + Prettier 规范，保持代码风格统一。
- 所有新功能需配套单元测试，测试覆盖率不低于 80%。

## 4. 文档与注释

- 重要模块、复杂函数需有中英文注释，便于后续维护。
- 所有接口需在 `/docs` 目录下补充接口文档，推荐使用 OpenAPI/Swagger 自动生成。
- 变更记录、开发计划等文档需及时更新。

## 5. 安全与合规

- 不得在代码库中提交明文账号、密码、API Key 等敏感信息，统一使用环境变量管理。
- 自动化操作需遵守小红书平台规则，避免高频、异常请求，防止账号被封。
- 回复内容需合规，避免违规词汇。

## 6. 依赖与环境

- 后端依赖统一写入 `requirements.txt` 或 `pyproject.toml`。
- 前端依赖统一写入 `package.json`。
- 推荐使用 Dockerfile 维护开发与生产环境一致性。

## 7. 任务与协作

- 所有开发任务需在项目管理工具（如 Trello、Jira、GitHub Projects）中登记，分配负责人。
- 定期同步开发进度，遇到技术难题及时沟通。

---


如需生成具体的 `.editorconfig`、`.prettierrc`、`.flake8` 等配置文件，也可告知！ 