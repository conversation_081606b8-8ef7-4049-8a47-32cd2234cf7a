#!/usr/bin/env python3
"""
数据库设计分析脚本
"""
import sys
sys.path.append('.')
from app.models import Base
from sqlalchemy import inspect
from sqlalchemy.orm import relationship, RelationshipProperty
import importlib

def analyze_database_design():
    """分析数据库设计"""
    print('=== 数据库模型分析 ===')
    
    # 获取所有模型类
    models = []
    for cls in Base.registry._class_registry.values():
        if hasattr(cls, '__tablename__'):
            models.append(cls)
    
    print(f'📊 总计: {len(models)} 个数据模型')
    
    # 分析每个模型
    for model in models:
        print(f'\n📋 {model.__name__} ({model.__tablename__}):')
        
        # 获取字段信息
        inspector = inspect(model)
        columns = inspector.columns
        
        print(f'  字段数量: {len(columns)}')
        for col_name, column in columns.items():
            nullable = "可空" if column.nullable else "必填"
            primary_key = "主键" if column.primary_key else ""
            foreign_key = "外键" if column.foreign_keys else ""
            unique = "唯一" if column.unique else ""
            
            attrs = [attr for attr in [primary_key, foreign_key, unique, nullable] if attr]
            attrs_str = f" ({', '.join(attrs)})" if attrs else ""
            
            print(f'    {col_name}: {column.type}{attrs_str}')
        
        # 获取关系信息
        relationships = []
        for attr_name in dir(model):
            try:
                attr = getattr(model, attr_name)
                if hasattr(attr, 'property') and isinstance(attr.property, RelationshipProperty):
                    relationships.append((attr_name, attr.property))
            except:
                continue
        
        if relationships:
            print(f'  关系数量: {len(relationships)}')
            for rel_name, rel in relationships:
                rel_type = "一对多" if rel.uselist else "一对一"
                target = rel.mapper.class_.__name__ if hasattr(rel, 'mapper') else "未知"
                print(f'    {rel_name} -> {target} ({rel_type})')

def check_database_best_practices():
    """检查数据库最佳实践"""
    print('\n=== 数据库设计最佳实践检查 ===')
    
    models = []
    for cls in Base.registry._class_registry.values():
        if hasattr(cls, '__tablename__'):
            models.append(cls)
    
    issues = []
    recommendations = []
    
    for model in models:
        model_name = model.__name__
        table_name = model.__tablename__
        inspector = inspect(model)
        columns = inspector.columns
        
        # 检查主键
        primary_keys = [col for col in columns.values() if col.primary_key]
        if not primary_keys:
            issues.append(f"❌ {model_name}: 缺少主键")
        elif len(primary_keys) > 1:
            recommendations.append(f"⚠️ {model_name}: 使用复合主键，考虑是否必要")
        
        # 检查时间戳字段
        has_created_at = any('created' in col.lower() for col in columns.keys())
        has_updated_at = any('updated' in col.lower() for col in columns.keys())
        
        if not has_created_at:
            recommendations.append(f"💡 {model_name}: 建议添加创建时间字段")
        if not has_updated_at:
            recommendations.append(f"💡 {model_name}: 建议添加更新时间字段")
        
        # 检查外键约束
        foreign_keys = []
        for col in columns.values():
            if col.foreign_keys:
                foreign_keys.extend(col.foreign_keys)
        
        # 检查索引（基本检查）
        indexed_columns = [col for col in columns.values() if col.index]
        if not indexed_columns and len(columns) > 3:
            recommendations.append(f"💡 {model_name}: 考虑为常用查询字段添加索引")
        
        # 检查字段命名规范
        for col_name in columns.keys():
            if not col_name.islower():
                recommendations.append(f"💡 {model_name}.{col_name}: 建议使用小写字段名")
            if ' ' in col_name:
                issues.append(f"❌ {model_name}.{col_name}: 字段名包含空格")
    
    # 输出结果
    if issues:
        print('\n🚨 发现的问题:')
        for issue in issues:
            print(f'  {issue}')
    else:
        print('\n✅ 未发现严重问题')
    
    if recommendations:
        print('\n💡 改进建议:')
        for rec in recommendations[:10]:  # 只显示前10个建议
            print(f'  {rec}')
        if len(recommendations) > 10:
            print(f'  ... 还有 {len(recommendations) - 10} 个建议')
    
    return len(issues), len(recommendations)

def analyze_relationships():
    """分析模型关系"""
    print('\n=== 模型关系分析 ===')
    
    models = []
    for cls in Base.registry._class_registry.values():
        if hasattr(cls, '__tablename__'):
            models.append(cls)
    
    all_relationships = {}
    
    for model in models:
        model_name = model.__name__
        relationships = []
        
        for attr_name in dir(model):
            try:
                attr = getattr(model, attr_name)
                if hasattr(attr, 'property') and isinstance(attr.property, RelationshipProperty):
                    rel_prop = attr.property
                    target_model = rel_prop.mapper.class_.__name__ if hasattr(rel_prop, 'mapper') else "未知"
                    rel_type = "一对多" if rel_prop.uselist else "一对一"
                    back_populates = getattr(rel_prop, 'back_populates', None)
                
                    relationships.append({
                        'name': attr_name,
                        'target': target_model,
                        'type': rel_type,
                        'back_populates': back_populates
                    })
            except:
                continue
        
        if relationships:
            all_relationships[model_name] = relationships
    
    # 检查关系一致性
    print('\n🔗 关系一致性检查:')
    for model_name, rels in all_relationships.items():
        for rel in rels:
            if rel['back_populates']:
                target_model = rel['target']
                back_rel_name = rel['back_populates']
                
                # 检查目标模型是否有对应的反向关系
                if target_model in all_relationships:
                    target_rels = all_relationships[target_model]
                    back_rel_found = any(r['name'] == back_rel_name for r in target_rels)
                    
                    if back_rel_found:
                        print(f'  ✅ {model_name}.{rel["name"]} <-> {target_model}.{back_rel_name}')
                    else:
                        print(f'  ❌ {model_name}.{rel["name"]} -> {target_model}.{back_rel_name} (反向关系缺失)')
                else:
                    print(f'  ⚠️ {model_name}.{rel["name"]} -> {target_model} (目标模型未找到)')

if __name__ == "__main__":
    analyze_database_design()
    issues, recommendations = check_database_best_practices()
    analyze_relationships()
    
    print(f'\n📊 总结:')
    print(f'  问题数量: {issues}')
    print(f'  建议数量: {recommendations}')
