#!/usr/bin/env python3
"""
性能和可扩展性分析脚本
"""
import sys
import os
import re
sys.path.append('.')

def check_database_performance():
    """检查数据库性能配置"""
    print('=== 数据库性能检查 ===')
    
    issues = []
    recommendations = []
    
    # 检查数据库配置
    try:
        from app.core.config import settings
        
        # 检查连接池配置
        if hasattr(settings, 'DATABASE_POOL_SIZE'):
            print(f"✅ 数据库连接池大小: {settings.DATABASE_POOL_SIZE}")
        else:
            recommendations.append("⚠️ 建议配置数据库连接池大小")
            
        if hasattr(settings, 'DATABASE_MAX_OVERFLOW'):
            print(f"✅ 数据库最大溢出连接: {settings.DATABASE_MAX_OVERFLOW}")
        else:
            recommendations.append("⚠️ 建议配置数据库最大溢出连接")
            
    except Exception as e:
        recommendations.append(f"⚠️ 无法检查数据库配置: {e}")
    
    # 检查模型索引
    try:
        from app.models import Base
        
        indexed_models = 0
        total_models = 0
        
        for cls in Base.registry._class_registry.values():
            if hasattr(cls, '__tablename__'):
                total_models += 1
                
                # 检查是否有索引定义
                if hasattr(cls, '__table_args__'):
                    table_args = cls.__table_args__
                    if table_args and any('Index' in str(arg) for arg in table_args if hasattr(arg, '__class__')):
                        indexed_models += 1
        
        if total_models > 0:
            index_coverage = (indexed_models / total_models) * 100
            print(f"✅ 模型索引覆盖率: {index_coverage:.1f}% ({indexed_models}/{total_models})")
            
            if index_coverage < 50:
                recommendations.append("⚠️ 建议为更多模型添加数据库索引")
        
    except Exception as e:
        recommendations.append(f"⚠️ 无法检查模型索引: {e}")
    
    return issues, recommendations

def check_caching_strategy():
    """检查缓存策略"""
    print('\n=== 缓存策略检查 ===')
    
    issues = []
    recommendations = []
    
    # 检查Redis配置
    try:
        from app.core.config import settings
        
        if hasattr(settings, 'REDIS_URL'):
            print(f"✅ Redis配置存在: {settings.REDIS_URL}")
        else:
            recommendations.append("⚠️ 建议配置Redis缓存")
            
    except Exception as e:
        recommendations.append(f"⚠️ 无法检查Redis配置: {e}")
    
    # 检查缓存装饰器使用
    cache_usage = 0
    api_files = []
    api_dir = "app/api/v1"
    
    if os.path.exists(api_dir):
        for file in os.listdir(api_dir):
            if file.endswith('.py') and file != '__init__.py':
                api_files.append(os.path.join(api_dir, file))
    
    for file_path in api_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if '@cache' in content or 'cache' in content.lower():
                cache_usage += 1
                
        except Exception:
            continue
    
    if cache_usage > 0:
        print(f"✅ {cache_usage} 个API文件使用了缓存")
    else:
        recommendations.append("⚠️ 建议在API中使用缓存机制")
    
    return issues, recommendations

def check_async_performance():
    """检查异步性能"""
    print('\n=== 异步性能检查 ===')
    
    issues = []
    recommendations = []
    
    # 检查异步函数使用
    async_usage = 0
    sync_usage = 0
    
    api_files = []
    api_dir = "app/api/v1"
    
    if os.path.exists(api_dir):
        for file in os.listdir(api_dir):
            if file.endswith('.py') and file != '__init__.py':
                api_files.append(os.path.join(api_dir, file))
    
    for file_path in api_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 统计async def函数
            async_count = len(re.findall(r'async def ', content))
            sync_count = len(re.findall(r'^def ', content, re.MULTILINE)) - async_count
            
            async_usage += async_count
            sync_usage += sync_count
            
        except Exception:
            continue
    
    total_functions = async_usage + sync_usage
    if total_functions > 0:
        async_ratio = (async_usage / total_functions) * 100
        print(f"✅ 异步函数使用率: {async_ratio:.1f}% ({async_usage}/{total_functions})")
        
        if async_ratio < 70:
            recommendations.append("⚠️ 建议提高异步函数使用率以改善性能")
    
    # 检查数据库异步操作
    try:
        from app.models.database import AsyncSessionLocal
        print("✅ 异步数据库会话配置存在")
    except ImportError:
        recommendations.append("⚠️ 建议配置异步数据库会话")
    
    return issues, recommendations

def check_api_optimization():
    """检查API优化"""
    print('\n=== API优化检查 ===')
    
    issues = []
    recommendations = []
    
    # 检查分页实现
    pagination_usage = 0
    api_files = []
    api_dir = "app/api/v1"
    
    if os.path.exists(api_dir):
        for file in os.listdir(api_dir):
            if file.endswith('.py') and file != '__init__.py':
                api_files.append(os.path.join(api_dir, file))
    
    for file_path in api_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'page' in content and 'size' in content:
                pagination_usage += 1
                
        except Exception:
            continue
    
    if pagination_usage > 0:
        print(f"✅ {pagination_usage} 个API文件实现了分页")
    else:
        recommendations.append("⚠️ 建议为列表API实现分页功能")
    
    # 检查响应压缩
    try:
        from app.main import app
        
        # 检查中间件
        middleware_names = [middleware.cls.__name__ for middleware in app.user_middleware]
        
        if any('gzip' in name.lower() or 'compress' in name.lower() for name in middleware_names):
            print("✅ 响应压缩中间件已配置")
        else:
            recommendations.append("⚠️ 建议配置响应压缩中间件")
            
    except Exception as e:
        recommendations.append(f"⚠️ 无法检查中间件配置: {e}")
    
    return issues, recommendations

def check_scalability_design():
    """检查可扩展性设计"""
    print('\n=== 可扩展性设计检查 ===')
    
    issues = []
    recommendations = []
    
    # 检查服务层架构
    service_files = []
    service_dir = "app/services"
    
    if os.path.exists(service_dir):
        for file in os.listdir(service_dir):
            if file.endswith('.py') and file != '__init__.py':
                service_files.append(file)
        
        if service_files:
            print(f"✅ 服务层架构: {len(service_files)} 个服务文件")
        else:
            recommendations.append("⚠️ 建议实现服务层架构")
    else:
        recommendations.append("⚠️ 建议创建服务层目录")
    
    # 检查依赖注入
    try:
        from app.api.deps import get_db, get_current_user
        print("✅ 依赖注入机制已实现")
    except ImportError:
        issues.append("❌ 依赖注入机制缺失")
    
    # 检查配置管理
    try:
        from app.core.config import settings
        
        config_attrs = [attr for attr in dir(settings) if not attr.startswith('_')]
        if len(config_attrs) > 10:
            print(f"✅ 配置管理完善: {len(config_attrs)} 个配置项")
        else:
            recommendations.append("⚠️ 建议完善配置管理")
            
    except Exception as e:
        recommendations.append(f"⚠️ 无法检查配置管理: {e}")
    
    # 检查模块化设计
    api_modules = []
    api_dir = "app/api/v1"
    
    if os.path.exists(api_dir):
        for file in os.listdir(api_dir):
            if file.endswith('.py') and file != '__init__.py':
                api_modules.append(file)
        
        if len(api_modules) > 5:
            print(f"✅ 模块化设计良好: {len(api_modules)} 个API模块")
        else:
            recommendations.append("⚠️ 建议进一步模块化API设计")
    
    return issues, recommendations

def check_monitoring_readiness():
    """检查监控就绪性"""
    print('\n=== 监控就绪性检查 ===')
    
    issues = []
    recommendations = []
    
    # 检查健康检查端点
    try:
        from app.api.v1.health import router
        print("✅ 健康检查端点存在")
    except ImportError:
        issues.append("❌ 缺少健康检查端点")
    
    # 检查指标收集
    metrics_indicators = ['prometheus', 'metrics', 'monitoring', 'stats']
    
    found_metrics = False
    for root, dirs, files in os.walk('app'):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read().lower()
                        
                    if any(indicator in content for indicator in metrics_indicators):
                        found_metrics = True
                        break
                except:
                    continue
        if found_metrics:
            break
    
    if found_metrics:
        print("✅ 指标收集机制存在")
    else:
        recommendations.append("⚠️ 建议添加指标收集机制")
    
    return issues, recommendations

def generate_performance_report():
    """生成性能报告"""
    print('⚡ 后端性能和可扩展性检测报告')
    print('=' * 50)
    
    all_issues = []
    all_recommendations = []
    
    # 执行各项检查
    checks = [
        check_database_performance,
        check_caching_strategy,
        check_async_performance,
        check_api_optimization,
        check_scalability_design,
        check_monitoring_readiness
    ]
    
    for check_func in checks:
        try:
            issues, recs = check_func()
            all_issues.extend(issues)
            all_recommendations.extend(recs)
        except Exception as e:
            all_issues.append(f"❌ 检查函数 {check_func.__name__} 执行失败: {e}")
    
    # 生成总结
    print('\n' + '=' * 50)
    print('📊 性能检测总结')
    print('=' * 50)
    
    if all_issues:
        print(f'\n🚨 发现 {len(all_issues)} 个性能问题:')
        for issue in all_issues:
            print(f'  {issue}')
    else:
        print('\n✅ 未发现严重性能问题')
    
    if all_recommendations:
        print(f'\n💡 {len(all_recommendations)} 个优化建议:')
        for rec in all_recommendations[:10]:  # 只显示前10个
            print(f'  {rec}')
        if len(all_recommendations) > 10:
            print(f'  ... 还有 {len(all_recommendations) - 10} 个建议')
    
    # 性能评分
    total_checks = 20  # 假设总共20个检查项
    issues_weight = 5
    recommendations_weight = 1
    
    score = max(0, 100 - (len(all_issues) * issues_weight + len(all_recommendations) * recommendations_weight))
    
    print(f'\n🎯 性能评分: {score}/100')
    
    if score >= 90:
        print('🟢 性能状况: 优秀')
    elif score >= 80:
        print('🟡 性能状况: 良好')
    elif score >= 70:
        print('🟠 性能状况: 一般')
    else:
        print('🔴 性能状况: 需要优化')
    
    return len(all_issues), len(all_recommendations), score

if __name__ == "__main__":
    generate_performance_report()
