"""
小红书自动化爬虫原型
使用Playwright实现自动登录和留言抓取的基础原型
"""
import asyncio
import json
import random
import time
from typing import Dict, List, Optional
from playwright.async_api import async_playwright, <PERSON>, BrowserContext
from loguru import logger
import os
from pathlib import Path


class XiaohongshuCrawler:
    """小红书自动化爬虫"""
    
    def __init__(self, headless: bool = False):
        self.headless = headless
        self.base_url = "https://www.xiaohongshu.com"
        self.login_url = "https://www.xiaohongshu.com/explore"
        self.session_file = "xiaohongshu_session.json"
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
        
    async def create_browser_context(self) -> tuple:
        """创建浏览器上下文"""
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(
            headless=self.headless,
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage'
            ]
        )
        
        context = await browser.new_context(
            user_agent=random.choice(self.user_agents),
            viewport={'width': 1920, 'height': 1080},
            locale='zh-CN'
        )
        
        # 加载已保存的会话
        if os.path.exists(self.session_file):
            try:
                with open(self.session_file, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)
                    await context.add_cookies(session_data.get('cookies', []))
                logger.info("已加载保存的会话")
            except Exception as e:
                logger.warning(f"加载会话失败: {e}")
                
        return playwright, browser, context
        
    async def save_session(self, context: BrowserContext) -> None:
        """保存会话信息"""
        try:
            cookies = await context.cookies()
            session_data = {
                'cookies': cookies,
                'timestamp': time.time()
            }
            with open(self.session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)
            logger.info("会话已保存")
        except Exception as e:
            logger.error(f"保存会话失败: {e}")
            
    async def simulate_human_behavior(self, page: Page) -> None:
        """模拟人类行为"""
        # 随机鼠标移动
        for _ in range(random.randint(2, 5)):
            x = random.randint(100, 800)
            y = random.randint(100, 600)
            await page.mouse.move(x, y)
            await page.wait_for_timeout(random.randint(100, 500))
            
        # 随机滚动
        scroll_distance = random.randint(200, 800)
        await page.evaluate(f"window.scrollBy(0, {scroll_distance})")
        await page.wait_for_timeout(random.randint(1000, 3000))
        
    async def check_login_status(self, page: Page) -> bool:
        """检查登录状态"""
        try:
            # 查找登录指示器
            login_indicators = [
                '[data-testid="user-avatar"]',
                '.user-avatar',
                'text="个人主页"',
                '[class*="profile"]'
            ]
            
            for indicator in login_indicators:
                try:
                    element = await page.wait_for_selector(indicator, timeout=2000)
                    if element:
                        logger.info("检测到已登录状态")
                        return True
                except:
                    continue
                    
            # 查找登录按钮（表示未登录）
            login_buttons = [
                'text="登录"',
                '[data-testid="login"]',
                '.login-btn'
            ]
            
            for button in login_buttons:
                try:
                    element = await page.wait_for_selector(button, timeout=2000)
                    if element:
                        logger.info("检测到未登录状态")
                        return False
                except:
                    continue
                    
            return False
            
        except Exception as e:
            logger.error(f"检查登录状态失败: {e}")
            return False
            
    async def manual_login_prompt(self, page: Page) -> bool:
        """提示用户手动登录"""
        logger.info("请在浏览器中手动完成登录...")
        logger.info("登录完成后，程序将自动继续")
        
        # 等待用户登录
        max_wait_time = 300  # 5分钟
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            if await self.check_login_status(page):
                logger.info("检测到登录成功！")
                return True
            await page.wait_for_timeout(2000)
            
        logger.warning("登录超时")
        return False
        
    async def extract_note_comments(self, page: Page, note_url: str) -> List[Dict]:
        """提取笔记评论"""
        logger.info(f"开始提取笔记评论: {note_url}")
        
        try:
            await page.goto(note_url, wait_until='networkidle')
            await self.simulate_human_behavior(page)
            
            # 滚动到评论区
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await page.wait_for_timeout(2000)
            
            comments = []
            
            # 尝试多种评论选择器
            comment_selectors = [
                '[data-testid="comment-item"]',
                '.comment-item',
                '.note-comment-item',
                '[class*="comment"]'
            ]
            
            for selector in comment_selectors:
                try:
                    comment_elements = await page.query_selector_all(selector)
                    if comment_elements:
                        logger.info(f"找到评论元素: {len(comment_elements)} 条")
                        
                        for element in comment_elements:
                            try:
                                # 提取评论信息
                                comment_data = await self._extract_comment_data(element)
                                if comment_data:
                                    comments.append(comment_data)
                            except Exception as e:
                                logger.warning(f"提取单条评论失败: {e}")
                                continue
                        break
                except Exception as e:
                    logger.warning(f"使用选择器 {selector} 失败: {e}")
                    continue
                    
            logger.info(f"成功提取 {len(comments)} 条评论")
            return comments
            
        except Exception as e:
            logger.error(f"提取评论失败: {e}")
            return []
            
    async def _extract_comment_data(self, element) -> Optional[Dict]:
        """提取单条评论数据"""
        try:
            # 提取用户名
            username_selectors = [
                '.username',
                '.user-name',
                '[class*="name"]',
                'a[href*="/user/"]'
            ]
            
            username = None
            for selector in username_selectors:
                try:
                    username_element = await element.query_selector(selector)
                    if username_element:
                        username = await username_element.inner_text()
                        break
                except:
                    continue
                    
            # 提取评论内容
            content_selectors = [
                '.comment-content',
                '.content',
                '[class*="content"]',
                'p'
            ]
            
            content = None
            for selector in content_selectors:
                try:
                    content_element = await element.query_selector(selector)
                    if content_element:
                        content = await content_element.inner_text()
                        break
                except:
                    continue
                    
            # 提取时间
            time_selectors = [
                '.time',
                '.comment-time',
                '[class*="time"]',
                'time'
            ]
            
            comment_time = None
            for selector in time_selectors:
                try:
                    time_element = await element.query_selector(selector)
                    if time_element:
                        comment_time = await time_element.inner_text()
                        break
                except:
                    continue
                    
            if username and content:
                return {
                    'username': username.strip(),
                    'content': content.strip(),
                    'time': comment_time.strip() if comment_time else None,
                    'extracted_at': time.time()
                }
                
        except Exception as e:
            logger.warning(f"提取评论数据失败: {e}")
            
        return None
        
    async def crawl_note_comments(self, note_url: str) -> List[Dict]:
        """爬取笔记评论的主要方法"""
        playwright, browser, context = await self.create_browser_context()
        
        try:
            page = await context.new_page()
            
            # 访问主页检查登录状态
            await page.goto(self.login_url)
            await page.wait_for_timeout(3000)
            
            # 检查是否需要登录
            if not await self.check_login_status(page):
                logger.info("需要登录，启动手动登录流程...")
                if not await self.manual_login_prompt(page):
                    logger.error("登录失败，无法继续")
                    return []
                    
                # 保存登录会话
                await self.save_session(context)
                
            # 提取评论
            comments = await self.extract_note_comments(page, note_url)
            
            return comments
            
        except Exception as e:
            logger.error(f"爬取过程出错: {e}")
            return []
        finally:
            await browser.close()
            await playwright.stop()


async def main():
    """主函数"""
    crawler = XiaohongshuCrawler(headless=False)
    
    # 示例笔记URL（需要替换为实际的笔记链接）
    note_url = "https://www.xiaohongshu.com/explore/[note_id]"
    
    logger.info("开始爬取小红书笔记评论...")
    comments = await crawler.crawl_note_comments(note_url)
    
    if comments:
        # 保存结果
        output_file = f"comments_{int(time.time())}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(comments, f, ensure_ascii=False, indent=2)
        logger.info(f"评论已保存到: {output_file}")
        
        # 打印统计信息
        logger.info(f"总共提取了 {len(comments)} 条评论")
        for i, comment in enumerate(comments[:5]):  # 显示前5条
            logger.info(f"评论 {i+1}: {comment['username']} - {comment['content'][:50]}...")
    else:
        logger.warning("未提取到任何评论")


if __name__ == "__main__":
    asyncio.run(main())
