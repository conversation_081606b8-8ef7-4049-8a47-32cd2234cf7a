#!/usr/bin/env python3
"""
测试爬虫API的真实数据对接
"""
import requests
import json

# API配置
BASE_URL = "http://localhost:8000/api/v1"

def test_login():
    """测试登录功能"""
    print("🔍 测试登录功能...")
    
    try:
        login_data = {
            'username': 'testuser2',
            'password': 'testpass123'
        }
        
        response = requests.post(f'{BASE_URL}/auth/login', data=login_data, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 登录成功!")
            return data['data']['access_token']
        else:
            print(f"❌ 登录失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_crawler_status(token):
    """测试爬虫状态API"""
    print("\n🔍 测试爬虫状态API...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{BASE_URL}/crawler/status", headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 爬虫状态API调用成功!")
            
            # 打印统计信息
            stats = data.get('data', {})
            print(f"📊 统计信息:")
            print(f"   总任务数: {stats.get('total_tasks', 0)}")
            print(f"   运行中任务: {stats.get('running_tasks', 0)}")
            print(f"   已完成任务: {stats.get('completed_tasks', 0)}")
            print(f"   失败任务: {stats.get('failed_tasks', 0)}")
            print(f"   总评论数: {stats.get('total_comments', 0)}")
            print(f"   新评论数: {stats.get('new_comments', 0)}")
            print(f"   成功率: {stats.get('success_rate', 0)}%")
            print(f"   平均响应时间: {stats.get('avg_response_time', 0)}秒")
            print(f"   爬虫状态: {stats.get('crawler_status', 'unknown')}")
            
            # 打印任务列表
            tasks = stats.get('tasks', [])
            print(f"\n📋 任务列表 ({len(tasks)} 个任务):")
            for i, task in enumerate(tasks, 1):
                print(f"   {i}. {task.get('task_name', 'Unknown')}")
                print(f"      状态: {task.get('status', 'unknown')}")
                print(f"      进度: {task.get('progress', 0)}%")
                print(f"      评论数: {task.get('comments_found', 0)}")
                print(f"      新评论: {task.get('new_comments', 0)}")
                print(f"      错误数: {task.get('errors', 0)}")
                print(f"      任务类型: {task.get('task_type', 'unknown')}")
                print()
            
            return True
        else:
            print(f"❌ 爬虫状态API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_create_task(token):
    """测试创建爬虫任务API"""
    print("\n🔍 测试创建爬虫任务API...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        # 创建一个新任务
        params = {
            'task_name': '测试API创建的任务',
            'account_id': 1,
            'note_id': 1,
            'task_type': 'manual'
        }
        
        response = requests.post(f"{BASE_URL}/crawler/tasks", params=params, headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 创建爬虫任务成功!")
            task_data = data.get('data', {})
            print(f"   任务ID: {task_data.get('task_id')}")
            print(f"   任务名称: {task_data.get('task_name')}")
            print(f"   任务状态: {task_data.get('status')}")
            print(f"   创建时间: {task_data.get('created_at')}")
            return task_data.get('task_id')
        else:
            print(f"❌ 创建爬虫任务失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_task_logs(token, task_id):
    """测试获取任务日志API"""
    print(f"\n🔍 测试获取任务日志API (任务ID: {task_id})...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{BASE_URL}/crawler/tasks/{task_id}/logs", headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取任务日志成功!")
            
            logs = data.get('data', {}).get('logs', [])
            print(f"📝 日志记录 ({len(logs)} 条):")
            for i, log in enumerate(logs[:5], 1):  # 只显示前5条
                print(f"   {i}. [{log.get('level', 'info').upper()}] {log.get('message', '')}")
                print(f"      时间: {log.get('created_at', '')}")
                if log.get('details'):
                    print(f"      详情: {log.get('details')}")
                print()
            
            if len(logs) > 5:
                print(f"   ... 还有 {len(logs) - 5} 条日志")
            
            return True
        else:
            print(f"❌ 获取任务日志失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 爬虫API真实数据对接测试")
    print("=" * 50)
    
    # 1. 登录测试
    print("1️⃣ 登录测试")
    token = test_login()
    
    if not token:
        print("❌ 登录失败，测试终止")
        return
    
    # 2. 爬虫状态测试
    print("\n" + "=" * 50)
    print("2️⃣ 爬虫状态测试")
    status_ok = test_crawler_status(token)
    
    # 3. 创建任务测试
    print("\n" + "=" * 50)
    print("3️⃣ 创建任务测试")
    task_id = test_create_task(token)
    
    # 4. 任务日志测试
    if task_id:
        print("\n" + "=" * 50)
        print("4️⃣ 任务日志测试")
        logs_ok = test_task_logs(token, task_id)
    else:
        # 使用示例任务的ID
        print("\n" + "=" * 50)
        print("4️⃣ 任务日志测试 (使用示例任务)")
        logs_ok = test_task_logs(token, 1)  # 使用第一个示例任务
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 测试结果总结:")
    
    results = {
        "登录功能": token is not None,
        "爬虫状态API": status_ok,
        "创建任务API": task_id is not None,
        "任务日志API": logs_ok if 'logs_ok' in locals() else False
    }
    
    all_passed = all(results.values())
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    if all_passed:
        print("\n🎉 所有测试通过！爬虫管理页面真实数据对接成功")
        print("✅ 数据库连接正常")
        print("✅ API功能完整")
        print("✅ 示例数据可用")
        print("✅ 前端可以正常显示真实数据")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
