#!/usr/bin/env python3
"""
创建测试笔记用于前端删除测试
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import sessionmaker
from app.database import engine
from app.models.note import Note
from app.models.user import <PERSON><PERSON>shuAccount
from datetime import datetime

# 创建数据库会话
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def create_test_note():
    """创建测试笔记"""
    db = SessionLocal()
    
    try:
        # 查找现有的小红书账号
        account = db.query(XiaohongshuAccount).first()
        if not account:
            print("❌ 没有找到小红书账号，无法创建测试笔记")
            return None
        
        print(f"✅ 找到账号: {account.account_name} (ID: {account.id})")
        
        # 创建测试笔记
        test_note = Note(
            account_id=account.id,
            note_id="test_note_for_deletion_123",
            note_url="https://www.xiaohongshu.com/explore/test_note_for_deletion_123",
            title="测试删除功能的笔记",
            content="这是一个用于测试删除功能的笔记，可以安全删除。",
            author_name="测试用户",
            author_id="test_author_123",
            publish_time=datetime.now(),
            likes_count=10,
            comments_count=2,
            shares_count=1,
            status="active",
            crawl_interval=300,
            auto_reply_enabled=True
        )
        
        db.add(test_note)
        db.commit()
        db.refresh(test_note)
        
        print(f"✅ 测试笔记创建成功!")
        print(f"   笔记ID: {test_note.id}")
        print(f"   标题: {test_note.title}")
        print(f"   URL: {test_note.note_url}")
        
        return test_note.id
        
    except Exception as e:
        db.rollback()
        print(f"❌ 创建测试笔记失败: {e}")
        return None
        
    finally:
        db.close()

def main():
    """主函数"""
    print("🚀 创建测试笔记用于删除功能测试...")
    print("=" * 50)
    
    note_id = create_test_note()
    
    if note_id:
        print("\n" + "=" * 50)
        print("🎉 测试笔记创建完成!")
        print(f"现在可以在前端页面测试删除笔记ID: {note_id}")
        print("请在笔记管理页面找到这个笔记并测试删除功能。")
    else:
        print("\n" + "=" * 50)
        print("❌ 测试笔记创建失败!")

if __name__ == "__main__":
    main()
