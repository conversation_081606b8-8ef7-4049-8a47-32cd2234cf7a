import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Typography,
  Space,
  Progress,
  List,
  Avatar,
  Tag,
  Button,
  Divider,
  Alert,
} from 'antd';
import {
  UserOutlined,
  BookOutlined,
  MessageOutlined,
  RobotOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import './DashboardPage.css';

const { Title, Text } = Typography;

const DashboardPage = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    accounts: 0,
    notes: 0,
    comments: 0,
    replies: 0,
  });
  const [recentActivities, setRecentActivities] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 模拟加载数据
    setTimeout(() => {
      setStats({
        accounts: 3,
        notes: 12,
        comments: 156,
        replies: 89,
      });
      
      setRecentActivities([
        {
          id: 1,
          type: 'comment',
          title: '收到新留言',
          description: '用户"小红书用户123"在笔记"美妆分享"下留言',
          time: '2分钟前',
          status: 'new',
        },
        {
          id: 2,
          type: 'reply',
          title: '自动回复成功',
          description: '已自动回复用户"时尚达人"的留言',
          time: '5分钟前',
          status: 'success',
        },
        {
          id: 3,
          type: 'crawl',
          title: '数据抓取完成',
          description: '成功抓取笔记"护肤心得"的最新留言',
          time: '10分钟前',
          status: 'success',
        },
        {
          id: 4,
          type: 'error',
          title: '抓取失败',
          description: '账号"主账号"的Cookie可能已过期',
          time: '1小时前',
          status: 'error',
        },
      ]);
      
      setLoading(false);
    }, 1000);
  }, []);

  const getActivityIcon = (type) => {
    switch (type) {
      case 'comment':
        return <MessageOutlined style={{ color: '#1890ff' }} />;
      case 'reply':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'crawl':
        return <RobotOutlined style={{ color: '#722ed1' }} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  const getStatusTag = (status) => {
    switch (status) {
      case 'new':
        return <Tag color="blue">新消息</Tag>;
      case 'success':
        return <Tag color="green">成功</Tag>;
      case 'error':
        return <Tag color="red">错误</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  return (
    <div className="dashboard-page">
      <div className="dashboard-header">
        <Title level={2}>仪表盘</Title>
        <Text type="secondary">欢迎回来！这里是您的工作概览</Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="stats-row">
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="小红书账号"
              value={stats.accounts}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
            <div className="stat-footer">
              <Button type="link" onClick={() => navigate('/accounts')}>
                管理账号
              </Button>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="监控笔记"
              value={stats.notes}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
            <div className="stat-footer">
              <Button type="link" onClick={() => navigate('/notes')}>
                管理笔记
              </Button>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="总留言数"
              value={stats.comments}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
            <div className="stat-footer">
              <Button type="link" onClick={() => navigate('/comments')}>
                查看留言
              </Button>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="已回复"
              value={stats.replies}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
            <div className="stat-footer">
              <Progress 
                percent={Math.round((stats.replies / stats.comments) * 100)} 
                size="small" 
                showInfo={false}
              />
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} className="content-row">
        {/* 最近活动 */}
        <Col xs={24} lg={16}>
          <Card 
            title="最近活动" 
            className="activity-card"
            extra={
              <Button type="link" onClick={() => navigate('/comments')}>
                查看全部
              </Button>
            }
          >
            <List
              loading={loading}
              dataSource={recentActivities}
              renderItem={(item) => (
                <List.Item className="activity-item">
                  <List.Item.Meta
                    avatar={
                      <Avatar 
                        icon={getActivityIcon(item.type)}
                        style={{ backgroundColor: '#f5f5f5' }}
                      />
                    }
                    title={
                      <Space>
                        <span>{item.title}</span>
                        {getStatusTag(item.status)}
                      </Space>
                    }
                    description={
                      <div>
                        <div>{item.description}</div>
                        <Text type="secondary" className="activity-time">
                          {item.time}
                        </Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 快速操作和提醒 */}
        <Col xs={24} lg={8}>
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            {/* 快速操作 */}
            <Card title="快速操作" className="quick-actions-card">
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <Button 
                  type="primary" 
                  icon={<UserOutlined />} 
                  block
                  onClick={() => navigate('/accounts')}
                >
                  添加小红书账号
                </Button>
                <Button 
                  icon={<BookOutlined />} 
                  block
                  onClick={() => navigate('/notes')}
                >
                  添加监控笔记
                </Button>
                <Button 
                  icon={<RobotOutlined />} 
                  block
                  onClick={() => navigate('/crawler')}
                >
                  启动数据抓取
                </Button>
              </Space>
            </Card>

            {/* 系统提醒 */}
            <Card title="系统提醒" className="alerts-card">
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <Alert
                  message="Cookie即将过期"
                  description="主账号的登录状态将在2天后过期，请及时更新"
                  type="warning"
                  showIcon
                  closable
                />
                <Alert
                  message="新功能上线"
                  description="AI智能回复功能已上线，快去体验吧！"
                  type="info"
                  showIcon
                  closable
                />
              </Space>
            </Card>

            {/* 使用统计 */}
            <Card title="本月统计" className="monthly-stats-card">
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <div className="stat-item">
                  <div className="stat-label">回复率</div>
                  <Progress 
                    percent={87} 
                    strokeColor="#52c41a"
                    format={percent => `${percent}%`}
                  />
                </div>
                <div className="stat-item">
                  <div className="stat-label">抓取成功率</div>
                  <Progress 
                    percent={94} 
                    strokeColor="#1890ff"
                    format={percent => `${percent}%`}
                  />
                </div>
                <div className="stat-item">
                  <div className="stat-label">系统稳定性</div>
                  <Progress 
                    percent={99} 
                    strokeColor="#722ed1"
                    format={percent => `${percent}%`}
                  />
                </div>
              </Space>
            </Card>
          </Space>
        </Col>
      </Row>
    </div>
  );
};

export default DashboardPage;
