from typing import List, Optional, Any, Dict
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ....core.deps import get_current_user, get_db
from ....models.user import User
from ....models.reply_template import AIConfig
from ....services.ai_provider_manager import AIProviderManager, AIProvider
from ....services.ollama_service import get_ollama_service


router = APIRouter()


class AIConfigCreate(BaseModel):
    """AI配置创建模型"""
    provider: str = "ollama"
    api_key: Optional[str] = None
    api_base_url: Optional[str] = None
    default_model: str = "llama2"
    temperature: str = "0.7"
    max_tokens: int = 150
    reply_language: str = "zh"
    reply_tone: str = "friendly"
    include_emoji: bool = True
    content_filter: bool = True
    max_daily_requests: int = 1000
    max_monthly_cost: int = 100
    ollama_base_url: Optional[str] = "http://localhost:11434"
    ollama_model: Optional[str] = "llama2"
    ollama_timeout: int = 60


class AIConfigUpdate(BaseModel):
    """AI配置更新模型"""
    provider: Optional[str] = None
    api_key: Optional[str] = None
    api_base_url: Optional[str] = None
    default_model: Optional[str] = None
    temperature: Optional[str] = None
    max_tokens: Optional[int] = None
    reply_language: Optional[str] = None
    reply_tone: Optional[str] = None
    include_emoji: Optional[bool] = None
    content_filter: Optional[bool] = None
    max_daily_requests: Optional[int] = None
    max_monthly_cost: Optional[int] = None
    ollama_base_url: Optional[str] = None
    ollama_model: Optional[str] = None
    ollama_timeout: Optional[int] = None


class AIConfigResponse(BaseModel):
    """AI配置响应模型"""
    id: int
    provider: str
    api_key: Optional[str] = None
    api_base_url: Optional[str] = None
    default_model: str
    temperature: str
    max_tokens: int
    reply_language: str
    reply_tone: str
    include_emoji: bool
    content_filter: bool
    max_daily_requests: int
    current_daily_requests: int
    max_monthly_cost: int
    current_monthly_cost: int
    ollama_base_url: Optional[str] = None
    ollama_model: Optional[str] = None
    ollama_timeout: int
    
    class Config:
        from_attributes = True


class ModelInfo(BaseModel):
    """模型信息"""
    id: str
    name: str
    size: Optional[int] = None
    modified_at: Optional[str] = None


class TestConnectionResponse(BaseModel):
    """连接测试响应"""
    success: bool
    provider: Optional[str] = None
    model: Optional[str] = None
    url: Optional[str] = None
    message: Optional[str] = None
    error: Optional[str] = None


@router.get("/config", response_model=AIConfigResponse)
async def get_ai_config(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取AI配置"""
    config = db.query(AIConfig).filter(AIConfig.user_id == current_user.id).first()
    
    if not config:
        # 创建默认配置
        config = AIConfig(
            user_id=current_user.id,
            provider="ollama",
            default_model="llama2",
            temperature="0.7",
            max_tokens=150,
            reply_language="zh",
            reply_tone="friendly",
            include_emoji=True,
            content_filter=True,
            max_daily_requests=1000,
            max_monthly_cost=100,
            ollama_base_url="http://localhost:11434",
            ollama_model="llama2",
            ollama_timeout=60
        )
        db.add(config)
        db.commit()
        db.refresh(config)
    
    return config


@router.put("/config", response_model=AIConfigResponse)
async def update_ai_config(
    config_update: AIConfigUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新AI配置"""
    config = db.query(AIConfig).filter(AIConfig.user_id == current_user.id).first()
    
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="AI配置不存在"
        )
    
    # 更新配置
    update_data = config_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(config, field, value)
    
    db.commit()
    db.refresh(config)
    
    return config


@router.post("/config", response_model=AIConfigResponse)
async def create_ai_config(
    config_create: AIConfigCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建AI配置"""
    # 检查是否已存在配置
    existing_config = db.query(AIConfig).filter(AIConfig.user_id == current_user.id).first()
    if existing_config:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="AI配置已存在，请使用更新接口"
        )
    
    # 创建新配置
    config = AIConfig(
        user_id=current_user.id,
        **config_create.dict()
    )
    
    db.add(config)
    db.commit()
    db.refresh(config)
    
    return config


@router.post("/test-connection", response_model=TestConnectionResponse)
async def test_ai_connection(
    provider: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """测试AI提供商连接"""
    try:
        ai_manager = AIProviderManager(db, current_user.id)
        
        test_provider = None
        if provider:
            test_provider = AIProvider(provider)
        
        result = await ai_manager.test_connection(test_provider)
        return TestConnectionResponse(**result)
        
    except Exception as e:
        return TestConnectionResponse(
            success=False,
            error=f"连接测试失败: {str(e)}"
        )


@router.get("/models", response_model=Dict[str, Any])
async def get_available_models(
    provider: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取可用模型列表"""
    try:
        ai_manager = AIProviderManager(db, current_user.id)
        
        target_provider = None
        if provider:
            target_provider = AIProvider(provider)
        
        result = await ai_manager.get_available_models(target_provider)
        return result
        
    except Exception as e:
        return {
            "success": False,
            "error": f"获取模型列表失败: {str(e)}",
            "models": []
        }


@router.get("/providers")
async def get_ai_providers():
    """获取支持的AI提供商列表"""
    return {
        "providers": [
            {
                "id": "ollama",
                "name": "Ollama",
                "description": "本地部署的开源大语言模型",
                "features": ["免费", "本地部署", "隐私保护", "多模型支持"],
                "requirements": ["需要本地安装Ollama服务"]
            },
            {
                "id": "openai",
                "name": "OpenAI",
                "description": "OpenAI官方API服务",
                "features": ["高质量", "稳定可靠", "多模型支持"],
                "requirements": ["需要API密钥", "按使用量付费"]
            },
            {
                "id": "anthropic",
                "name": "Anthropic Claude",
                "description": "Anthropic的Claude模型",
                "features": ["安全可靠", "长上下文", "高质量回复"],
                "requirements": ["需要API密钥", "按使用量付费"],
                "status": "coming_soon"
            }
        ]
    }


@router.post("/ollama/pull-model")
async def pull_ollama_model(
    model_name: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """拉取Ollama模型"""
    try:
        ollama_service = await get_ollama_service()
        
        async with ollama_service as service:
            result = await service.pull_model(model_name)
            return result
            
    except Exception as e:
        return {
            "success": False,
            "error": f"拉取模型失败: {str(e)}"
        }


@router.get("/ollama/health")
async def check_ollama_health():
    """检查Ollama服务健康状态"""
    try:
        ollama_service = await get_ollama_service()
        
        async with ollama_service as service:
            result = await service.check_health()
            return result
            
    except Exception as e:
        return {
            "success": False,
            "status": "error",
            "error": f"健康检查失败: {str(e)}"
        }


@router.post("/generate-reply")
async def generate_ai_reply(
    comment_content: str,
    context: Optional[Dict] = None,
    template_id: Optional[int] = None,
    provider: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """生成AI回复"""
    try:
        ai_manager = AIProviderManager(db, current_user.id)
        
        target_provider = None
        if provider:
            target_provider = AIProvider(provider)
        
        result = await ai_manager.generate_reply(
            comment_content=comment_content,
            context=context,
            template_id=template_id,
            provider=target_provider
        )
        
        return result
        
    except Exception as e:
        return {
            "success": False,
            "error": f"生成回复失败: {str(e)}",
            "reply": None
        }
