# 小红书自动回复系统 - API文档

## 📖 目录

1. [API概述](#api概述)
2. [认证方式](#认证方式)
3. [响应格式](#响应格式)
4. [错误处理](#错误处理)
5. [API接口](#api接口)
6. [WebSocket接口](#websocket接口)
7. [SDK和示例](#sdk和示例)

## 🌐 API概述

小红书自动回复系统提供RESTful API，支持所有核心功能的程序化访问。

### 基础信息
- **Base URL**: `http://localhost:8000/api/v1`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

### API特性
- 🔐 JWT Token认证
- 📊 请求限流保护
- 🔄 自动重试机制
- 📝 详细错误信息
- 🚀 高性能响应

## 🔐 认证方式

### JWT Token认证
所有API请求（除了登录和注册）都需要在请求头中包含JWT Token：

```http
Authorization: Bearer <your_jwt_token>
```

### 获取Token
```http
POST /auth/login
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

### Token刷新
```http
POST /auth/refresh
Authorization: Bearer <your_jwt_token>
```

## 📋 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 响应数据
  },
  "timestamp": "2024-01-18T10:30:00Z"
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "error_code": "ERROR_CODE",
  "details": {
    // 错误详情
  },
  "timestamp": "2024-01-18T10:30:00Z"
}
```

## ❌ 错误处理

### HTTP状态码
- `200` - 成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未授权
- `403` - 禁止访问
- `404` - 资源不存在
- `422` - 数据验证失败
- `429` - 请求过于频繁
- `500` - 服务器内部错误

### 错误代码
- `INVALID_CREDENTIALS` - 登录凭据无效
- `TOKEN_EXPIRED` - Token已过期
- `INSUFFICIENT_PERMISSIONS` - 权限不足
- `RESOURCE_NOT_FOUND` - 资源不存在
- `VALIDATION_ERROR` - 数据验证错误
- `RATE_LIMIT_EXCEEDED` - 超出请求限制
- `AI_SERVICE_ERROR` - AI服务错误

## 🔌 API接口

### 认证接口

#### 用户注册
```http
POST /auth/register
Content-Type: application/json

{
  "username": "string",
  "email": "string",
  "password": "string"
}
```

#### 用户登录
```http
POST /auth/login
Content-Type: application/x-www-form-urlencoded

username=string&password=string
```

#### 获取当前用户信息
```http
GET /auth/me
Authorization: Bearer <token>
```

### 账号管理接口

#### 获取账号列表
```http
GET /accounts/
Authorization: Bearer <token>
```

#### 添加账号
```http
POST /accounts/
Authorization: Bearer <token>
Content-Type: application/json

{
  "account_name": "string",
  "account_id": "string",
  "cookies": "string",
  "is_active": true
}
```

#### 更新账号
```http
PUT /accounts/{account_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "account_name": "string",
  "cookies": "string",
  "is_active": true
}
```

### 留言管理接口

#### 获取留言列表
```http
GET /comments/?page=1&limit=20&status=pending
Authorization: Bearer <token>
```

#### 回复留言
```http
POST /comments/{comment_id}/reply
Authorization: Bearer <token>
Content-Type: application/json

{
  "reply_content": "string"
}
```

#### 批量标记已读
```http
POST /comments/batch-mark-read
Authorization: Bearer <token>
Content-Type: application/json

{
  "comment_ids": [1, 2, 3]
}
```

### AI功能接口

#### 生成AI回复
```http
POST /ai/generate-reply
Authorization: Bearer <token>
Content-Type: application/json

{
  "comment_content": "string",
  "context": {
    "note_title": "string"
  },
  "template_id": 1
}
```

#### 获取回复建议
```http
POST /ai/generate-suggestions
Authorization: Bearer <token>
Content-Type: application/json

{
  "comment_content": "string"
}
```

#### 批量生成回复
```http
POST /ai/batch-generate
Authorization: Bearer <token>
Content-Type: application/json

{
  "comments": [
    {
      "id": 1,
      "content": "string"
    }
  ]
}
```

### 模板管理接口

#### 获取模板列表
```http
GET /ai/templates?page=1&limit=20&category=感谢类
Authorization: Bearer <token>
```

#### 创建模板
```http
POST /ai/templates
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "string",
  "category": "string",
  "content": "string",
  "tags": ["string"],
  "is_active": true
}
```

#### 更新模板
```http
PUT /ai/templates/{template_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "string",
  "content": "string"
}
```

### 数据分析接口

#### 获取分析概览
```http
GET /analytics/overview?days=30
Authorization: Bearer <token>
```

#### 获取留言分析
```http
GET /analytics/comments?days=30
Authorization: Bearer <token>
```

#### 获取AI分析
```http
GET /analytics/ai?days=30
Authorization: Bearer <token>
```

#### 导出数据
```http
GET /analytics/export?days=30&format=json&data_type=all
Authorization: Bearer <token>
```

### 自动化接口

#### 处理留言
```http
POST /automation/process-comment
Authorization: Bearer <token>
Content-Type: application/json

{
  "comment_id": 1
}
```

#### 批量处理
```http
POST /automation/batch-process
Authorization: Bearer <token>
Content-Type: application/json

{
  "comment_ids": [1, 2, 3]
}
```

#### 测试规则
```http
POST /automation/test-rule
Authorization: Bearer <token>
Content-Type: application/json

{
  "rule_id": 1,
  "test_content": "string"
}
```

### 系统监控接口

#### 健康检查
```http
GET /health
```

#### 详细健康检查
```http
GET /health/detailed
```

#### 系统指标
```http
GET /metrics
Authorization: Bearer <token>
```

## 🔌 WebSocket接口

### 连接地址
```
ws://localhost:8000/api/v1/ws?token=<your_jwt_token>
```

### 消息格式
```json
{
  "type": "message_type",
  "data": {},
  "timestamp": "2024-01-18T10:30:00Z"
}
```

### 消息类型
- `connection_established` - 连接建立
- `new_comment` - 新留言通知
- `ai_reply_generated` - AI回复生成
- `system_notification` - 系统通知
- `error` - 错误消息

### 客户端消息
```json
{
  "type": "ping",
  "timestamp": 1642492200000
}
```

## 💻 SDK和示例

### Python SDK示例
```python
import requests

class XiaohongshuAPI:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
    
    def get_comments(self, page=1, limit=20):
        response = requests.get(
            f'{self.base_url}/comments/',
            headers=self.headers,
            params={'page': page, 'limit': limit}
        )
        return response.json()
    
    def generate_ai_reply(self, comment_content):
        response = requests.post(
            f'{self.base_url}/ai/generate-reply',
            headers=self.headers,
            json={'comment_content': comment_content}
        )
        return response.json()

# 使用示例
api = XiaohongshuAPI('http://localhost:8000/api/v1', 'your_token')
comments = api.get_comments()
reply = api.generate_ai_reply('你的笔记很棒！')
```

### JavaScript SDK示例
```javascript
class XiaohongshuAPI {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl;
        this.headers = {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
    }
    
    async getComments(page = 1, limit = 20) {
        const response = await fetch(
            `${this.baseUrl}/comments/?page=${page}&limit=${limit}`,
            { headers: this.headers }
        );
        return response.json();
    }
    
    async generateAIReply(commentContent) {
        const response = await fetch(
            `${this.baseUrl}/ai/generate-reply`,
            {
                method: 'POST',
                headers: this.headers,
                body: JSON.stringify({ comment_content: commentContent })
            }
        );
        return response.json();
    }
}

// 使用示例
const api = new XiaohongshuAPI('http://localhost:8000/api/v1', 'your_token');
const comments = await api.getComments();
const reply = await api.generateAIReply('你的笔记很棒！');
```

### cURL示例
```bash
# 登录获取Token
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=your_username&password=your_password"

# 获取留言列表
curl -X GET "http://localhost:8000/api/v1/comments/" \
  -H "Authorization: Bearer your_token"

# 生成AI回复
curl -X POST "http://localhost:8000/api/v1/ai/generate-reply" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{"comment_content": "你的笔记很棒！"}'
```

## 📝 注意事项

1. **请求限制**: 每个用户每分钟最多60个请求
2. **Token有效期**: JWT Token默认30分钟有效期
3. **数据格式**: 所有时间使用ISO 8601格式
4. **文件上传**: 最大文件大小10MB
5. **WebSocket**: 支持最多1000个并发连接

## 🔄 版本更新

当前API版本：`v1`

版本更新时会保持向后兼容，新功能通过新的端点提供。

---

**更多信息请访问在线API文档：http://localhost:8000/docs**
