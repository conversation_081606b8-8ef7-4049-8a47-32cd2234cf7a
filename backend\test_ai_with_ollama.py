#!/usr/bin/env python3
"""
测试AI生成回复功能（使用Ollama）
"""
import requests
import json
import sys

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def login():
    """登录获取token"""
    login_data = {
        "username": "testuser2",
        "password": "testpass123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
    if response.status_code == 200:
        result = response.json()
        if "data" in result and "access_token" in result["data"]:
            return result["data"]["access_token"]
    return None

def test_ai_generate_reply(token):
    """测试AI生成回复"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n🤖 测试AI生成回复")
    print("-" * 30)
    
    # 测试数据
    test_data = {
        "comment_content": "这个产品看起来不错，请问有什么优惠吗？",
        "context": {
            "note_title": "美妆护肤分享",
            "note_category": "美妆"
        }
    }
    
    response = requests.post(
        f"{BASE_URL}/ai/generate-reply",
        headers=headers,
        json=test_data
    )
    
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ AI生成回复成功!")
        
        if result.get("success") and result.get("data"):
            data = result["data"]
            print(f"   生成的回复: {data.get('reply', '')}")
            print(f"   使用的模型: {data.get('model', 'Unknown')}")
            print(f"   生成时间: {data.get('generation_time', 0)} 秒")
        else:
            print(f"   响应数据: {result}")
        
        return True
    else:
        result = response.json()
        print(f"❌ AI生成回复失败: {result.get('message', '未知错误')}")
        print(f"   详细信息: {result}")
        return False

def test_batch_generate(token):
    """测试批量生成回复"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n🔄 测试批量生成回复")
    print("-" * 30)
    
    # 测试数据
    test_data = {
        "comments": [
            {
                "id": 1,
                "content": "这个产品效果怎么样？",
                "context": {"category": "美妆"}
            },
            {
                "id": 2,
                "content": "价格有点贵，有优惠吗？",
                "context": {"category": "购物"}
            },
            {
                "id": 3,
                "content": "谢谢分享，很有用！",
                "context": {"category": "感谢"}
            }
        ]
    }
    
    response = requests.post(
        f"{BASE_URL}/ai/batch-generate",
        headers=headers,
        json=test_data
    )
    
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 批量生成回复成功!")
        
        if result.get("success") and result.get("data"):
            data = result["data"]
            results = data.get("results", [])
            
            print(f"   处理了 {len(results)} 条评论:")
            for i, item in enumerate(results, 1):
                comment_id = item.get("comment_id")
                reply_result = item.get("result", {})
                
                print(f"\n   {i}. 评论ID: {comment_id}")
                if reply_result.get("success"):
                    print(f"      ✅ 生成成功: {reply_result.get('reply', '')[:50]}...")
                else:
                    print(f"      ❌ 生成失败: {reply_result.get('error', '未知错误')}")
        else:
            print(f"   响应数据: {result}")
        
        return True
    else:
        result = response.json()
        print(f"❌ 批量生成失败: {result.get('message', '未知错误')}")
        print(f"   详细信息: {result}")
        return False

def get_ai_config(token):
    """获取AI配置"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n⚙️ 获取AI配置")
    print("-" * 30)
    
    response = requests.get(f"{BASE_URL}/ai/config", headers=headers)
    
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 获取AI配置成功!")
        
        if result.get("success") and result.get("data"):
            data = result["data"]
            print(f"   提供商: {data.get('provider', 'Unknown')}")
            print(f"   Ollama URL: {data.get('ollama_base_url', 'N/A')}")
            print(f"   Ollama模型: {data.get('ollama_model', 'N/A')}")
            print(f"   默认模型: {data.get('default_model', 'N/A')}")
            print(f"   温度参数: {data.get('temperature', 'N/A')}")
            print(f"   最大tokens: {data.get('max_tokens', 'N/A')}")
        else:
            print(f"   响应数据: {result}")
        
        return True
    else:
        result = response.json()
        print(f"❌ 获取配置失败: {result.get('message', '未知错误')}")
        return False

def main():
    """主函数"""
    print("🤖 AI生成回复功能测试（使用Ollama）")
    print("=" * 50)
    
    # 1. 登录
    print("\n1️⃣ 登录测试")
    token = login()
    if not token:
        print("❌ 登录失败，无法继续测试")
        return
    
    print("✅ 登录成功!")
    
    # 2. 获取AI配置
    get_ai_config(token)
    
    # 3. 测试单个回复生成
    test_ai_generate_reply(token)
    
    # 4. 测试批量回复生成
    test_batch_generate(token)
    
    print("\n🎯 测试结果总结:")
    print("✅ AI生成回复功能测试完成")
    print("🤖 Ollama集成测试完成")
    print("📝 回复生成功能已就绪")

if __name__ == "__main__":
    main()
