import React from 'react';
import { But<PERSON>, Tooltip, Dropdown } from 'antd';
import { 
  SunOutlined, 
  MoonOutlined, 
  BgColorsOutlined,
  CheckOutlined 
} from '@ant-design/icons';
import { useThemeStore } from '../../stores/themeStore';

const ThemeToggle = ({ size = 'middle', type = 'text', showText = false }) => {
  const { currentTheme, isDark, setTheme, toggleTheme, themes } = useThemeStore();

  // 主题选项菜单
  const themeMenuItems = [
    {
      key: 'light',
      label: (
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', minWidth: 120 }}>
          <span>
            <SunOutlined style={{ marginRight: 8, color: '#faad14' }} />
            浅色主题
          </span>
          {currentTheme === 'light' && <CheckOutlined style={{ color: '#1890ff' }} />}
        </div>
      ),
      onClick: () => setTheme('light'),
    },
    {
      key: 'dark',
      label: (
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', minWidth: 120 }}>
          <span>
            <MoonOutlined style={{ marginRight: 8, color: '#722ed1' }} />
            深色主题
          </span>
          {currentTheme === 'dark' && <CheckOutlined style={{ color: '#1890ff' }} />}
        </div>
      ),
      onClick: () => setTheme('dark'),
    },
    {
      type: 'divider',
    },
    {
      key: 'system',
      label: (
        <span>
          <BgColorsOutlined style={{ marginRight: 8 }} />
          跟随系统
        </span>
      ),
      onClick: () => {
        const systemTheme = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        setTheme(systemTheme);
      },
    },
  ];

  // 简单切换按钮（只在浅色和深色之间切换）
  const SimpleToggle = () => (
    <Tooltip title={`切换到${isDark ? '浅色' : '深色'}主题`}>
      <Button
        type={type}
        size={size}
        icon={isDark ? <SunOutlined /> : <MoonOutlined />}
        onClick={toggleTheme}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        {showText && (isDark ? '浅色' : '深色')}
      </Button>
    </Tooltip>
  );

  // 下拉菜单切换按钮
  const DropdownToggle = () => (
    <Dropdown
      menu={{ items: themeMenuItems }}
      trigger={['click']}
      placement="bottomRight"
    >
      <Button
        type={type}
        size={size}
        icon={<BgColorsOutlined />}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        {showText && '主题'}
      </Button>
    </Dropdown>
  );

  return (
    <div className="theme-toggle">
      {/* 可以根据需要选择简单切换或下拉菜单 */}
      <DropdownToggle />
    </div>
  );
};

// 主题状态指示器
export const ThemeIndicator = () => {
  const { currentTheme, isDark } = useThemeStore();
  
  return (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: 4,
      fontSize: '12px',
      color: 'var(--theme-text-secondary)',
    }}>
      {isDark ? <MoonOutlined /> : <SunOutlined />}
      <span>{isDark ? '深色' : '浅色'}</span>
    </div>
  );
};

// 主题预览卡片组件
export const ThemePreviewCard = ({ themeName, theme, isActive, onClick }) => {
  return (
    <div
      className={`theme-preview-card ${isActive ? 'active' : ''}`}
      onClick={onClick}
      style={{
        border: `2px solid ${isActive ? '#1890ff' : '#d9d9d9'}`,
        borderRadius: '8px',
        padding: '12px',
        cursor: 'pointer',
        transition: 'all 0.3s ease',
        position: 'relative',
        backgroundColor: theme.colors.componentBg,
        minHeight: '80px',
      }}
    >
      {isActive && (
        <CheckOutlined 
          style={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            color: '#1890ff',
            fontSize: '16px'
          }}
        />
      )}
      <div style={{ 
        marginBottom: '8px', 
        fontWeight: 'bold', 
        color: theme.colors.textPrimary,
        fontSize: '14px'
      }}>
        {theme.displayName}
      </div>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
        <div style={{ 
          height: '16px', 
          backgroundColor: theme.colors.primary, 
          borderRadius: '4px',
          opacity: 0.8
        }} />
        <div style={{ 
          height: '24px', 
          backgroundColor: theme.colors.layoutBg, 
          borderRadius: '4px',
          opacity: 0.6
        }} />
        <div style={{ 
          height: '12px', 
          backgroundColor: theme.colors.textSecondary, 
          borderRadius: '4px',
          opacity: 0.4,
          width: '60%'
        }} />
      </div>
    </div>
  );
};

export default ThemeToggle;
