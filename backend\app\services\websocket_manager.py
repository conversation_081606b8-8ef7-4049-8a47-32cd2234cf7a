import asyncio
import json
from typing import Dict, List, Set
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
from loguru import logger


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 存储活跃连接 {user_id: {connection_id: websocket}}
        self.active_connections: Dict[int, Dict[str, WebSocket]] = {}
        # 存储连接元数据
        self.connection_metadata: Dict[str, Dict] = {}
        
    async def connect(self, websocket: WebSocket, user_id: int, connection_id: str):
        """建立WebSocket连接"""
        await websocket.accept()
        
        if user_id not in self.active_connections:
            self.active_connections[user_id] = {}
            
        self.active_connections[user_id][connection_id] = websocket
        self.connection_metadata[connection_id] = {
            "user_id": user_id,
            "connected_at": datetime.now(),
            "last_ping": datetime.now()
        }
        
        logger.info(f"WebSocket连接建立: user_id={user_id}, connection_id={connection_id}")
        
        # 发送连接成功消息
        await self.send_personal_message({
            "type": "connection_established",
            "message": "WebSocket连接已建立",
            "timestamp": datetime.now().isoformat()
        }, user_id, connection_id)
    
    def disconnect(self, user_id: int, connection_id: str):
        """断开WebSocket连接"""
        if user_id in self.active_connections:
            if connection_id in self.active_connections[user_id]:
                del self.active_connections[user_id][connection_id]
                
            # 如果用户没有其他连接，删除用户记录
            if not self.active_connections[user_id]:
                del self.active_connections[user_id]
        
        if connection_id in self.connection_metadata:
            del self.connection_metadata[connection_id]
            
        logger.info(f"WebSocket连接断开: user_id={user_id}, connection_id={connection_id}")
    
    async def send_personal_message(self, message: dict, user_id: int, connection_id: str = None):
        """发送个人消息"""
        if user_id not in self.active_connections:
            return False
            
        message_str = json.dumps(message, ensure_ascii=False)
        
        if connection_id:
            # 发送到指定连接
            if connection_id in self.active_connections[user_id]:
                try:
                    await self.active_connections[user_id][connection_id].send_text(message_str)
                    return True
                except Exception as e:
                    logger.error(f"发送消息失败: {e}")
                    self.disconnect(user_id, connection_id)
                    return False
        else:
            # 发送到用户的所有连接
            success_count = 0
            failed_connections = []
            
            for conn_id, websocket in self.active_connections[user_id].items():
                try:
                    await websocket.send_text(message_str)
                    success_count += 1
                except Exception as e:
                    logger.error(f"发送消息失败: {e}")
                    failed_connections.append(conn_id)
            
            # 清理失败的连接
            for conn_id in failed_connections:
                self.disconnect(user_id, conn_id)
                
            return success_count > 0
    
    async def broadcast_message(self, message: dict, user_ids: List[int] = None):
        """广播消息"""
        message_str = json.dumps(message, ensure_ascii=False)
        
        target_users = user_ids if user_ids else list(self.active_connections.keys())
        success_count = 0
        
        for user_id in target_users:
            if user_id in self.active_connections:
                failed_connections = []
                
                for conn_id, websocket in self.active_connections[user_id].items():
                    try:
                        await websocket.send_text(message_str)
                        success_count += 1
                    except Exception as e:
                        logger.error(f"广播消息失败: {e}")
                        failed_connections.append(conn_id)
                
                # 清理失败的连接
                for conn_id in failed_connections:
                    self.disconnect(user_id, conn_id)
        
        return success_count
    
    def get_user_connections(self, user_id: int) -> int:
        """获取用户连接数"""
        return len(self.active_connections.get(user_id, {}))
    
    def get_total_connections(self) -> int:
        """获取总连接数"""
        return sum(len(connections) for connections in self.active_connections.values())
    
    def get_online_users(self) -> List[int]:
        """获取在线用户列表"""
        return list(self.active_connections.keys())


class NotificationService:
    """通知服务"""
    
    def __init__(self, connection_manager: ConnectionManager):
        self.connection_manager = connection_manager
    
    async def send_comment_notification(self, user_id: int, comment_data: dict):
        """发送新留言通知"""
        notification = {
            "type": "new_comment",
            "title": "收到新留言",
            "message": f"用户 {comment_data.get('user_name')} 在笔记中留言",
            "data": comment_data,
            "timestamp": datetime.now().isoformat(),
            "priority": "normal"
        }
        
        await self.connection_manager.send_personal_message(notification, user_id)
        logger.info(f"发送新留言通知: user_id={user_id}")
    
    async def send_reply_notification(self, user_id: int, reply_data: dict):
        """发送回复成功通知"""
        notification = {
            "type": "reply_success",
            "title": "回复发送成功",
            "message": f"已成功回复留言",
            "data": reply_data,
            "timestamp": datetime.now().isoformat(),
            "priority": "low"
        }
        
        await self.connection_manager.send_personal_message(notification, user_id)
    
    async def send_ai_reply_notification(self, user_id: int, ai_reply_data: dict):
        """发送AI回复通知"""
        notification = {
            "type": "ai_reply_generated",
            "title": "AI回复已生成",
            "message": f"AI已为留言生成回复建议",
            "data": ai_reply_data,
            "timestamp": datetime.now().isoformat(),
            "priority": "normal"
        }
        
        await self.connection_manager.send_personal_message(notification, user_id)
    
    async def send_crawler_notification(self, user_id: int, crawler_data: dict):
        """发送爬虫状态通知"""
        notification = {
            "type": "crawler_status",
            "title": "爬虫状态更新",
            "message": f"爬虫任务状态: {crawler_data.get('status')}",
            "data": crawler_data,
            "timestamp": datetime.now().isoformat(),
            "priority": "normal"
        }
        
        await self.connection_manager.send_personal_message(notification, user_id)
    
    async def send_error_notification(self, user_id: int, error_data: dict):
        """发送错误通知"""
        notification = {
            "type": "error",
            "title": "系统错误",
            "message": error_data.get("message", "发生未知错误"),
            "data": error_data,
            "timestamp": datetime.now().isoformat(),
            "priority": "high"
        }
        
        await self.connection_manager.send_personal_message(notification, user_id)
    
    async def send_system_notification(self, message: str, user_ids: List[int] = None, priority: str = "normal"):
        """发送系统通知"""
        notification = {
            "type": "system",
            "title": "系统通知",
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "priority": priority
        }
        
        await self.connection_manager.broadcast_message(notification, user_ids)
    
    async def send_cost_alert(self, user_id: int, cost_data: dict):
        """发送成本警告"""
        notification = {
            "type": "cost_alert",
            "title": "费用警告",
            "message": f"AI使用费用已达到 {cost_data.get('percentage')}%",
            "data": cost_data,
            "timestamp": datetime.now().isoformat(),
            "priority": "high"
        }
        
        await self.connection_manager.send_personal_message(notification, user_id)


# 全局连接管理器实例
connection_manager = ConnectionManager()
notification_service = NotificationService(connection_manager)
