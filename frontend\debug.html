<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React应用调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success {
            background-color: #f6ffed;
            border-color: #b7eb8f;
        }
        .error {
            background-color: #fff2f0;
            border-color: #ffccc7;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        #console-output {
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 React应用调试页面</h1>
        
        <div class="test-item">
            <h3>1. 基础检查</h3>
            <p id="basic-check">检查中...</p>
        </div>

        <div class="test-item">
            <h3>2. React检查</h3>
            <button onclick="checkReact()">检查React</button>
            <div id="react-result"></div>
        </div>

        <div class="test-item">
            <h3>3. DOM检查</h3>
            <button onclick="checkDOM()">检查DOM结构</button>
            <div id="dom-result"></div>
        </div>

        <div class="test-item">
            <h3>4. 控制台日志</h3>
            <button onclick="clearConsole()">清空日志</button>
            <pre id="console-output"></pre>
        </div>

        <div class="test-item">
            <h3>5. 手动测试React</h3>
            <button onclick="testReactManually()">手动创建React元素</button>
            <div id="manual-react-test"></div>
        </div>
    </div>

    <script>
        // 捕获所有控制台输出
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleOutput.textContent += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole('log', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole('error', ...args);
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole('warn', ...args);
        };

        // 基础检查
        function basicCheck() {
            const checks = [];
            
            // 检查基本对象
            checks.push(`window对象: ${typeof window !== 'undefined' ? '✅' : '❌'}`);
            checks.push(`document对象: ${typeof document !== 'undefined' ? '✅' : '❌'}`);
            checks.push(`fetch API: ${typeof fetch !== 'undefined' ? '✅' : '❌'}`);
            
            // 检查root元素
            const rootElement = document.getElementById('root');
            checks.push(`root元素: ${rootElement ? '✅ 存在' : '❌ 不存在'}`);
            if (rootElement) {
                checks.push(`root内容: ${rootElement.innerHTML ? '有内容' : '空白'}`);
                checks.push(`root子元素数量: ${rootElement.children.length}`);
            }
            
            document.getElementById('basic-check').innerHTML = checks.join('<br>');
        }

        function checkReact() {
            const resultDiv = document.getElementById('react-result');
            const checks = [];
            
            // 检查React相关的全局变量
            checks.push(`React: ${typeof window.React !== 'undefined' ? '✅ 已加载' : '❌ 未加载'}`);
            checks.push(`ReactDOM: ${typeof window.ReactDOM !== 'undefined' ? '✅ 已加载' : '❌ 未加载'}`);
            
            // 检查是否有React应用实例
            const rootElement = document.getElementById('root');
            if (rootElement) {
                const reactRoot = rootElement._reactInternalInstance || rootElement._reactRootContainer;
                checks.push(`React根实例: ${reactRoot ? '✅ 存在' : '❌ 不存在'}`);
            }
            
            resultDiv.innerHTML = checks.join('<br>');
        }

        function checkDOM() {
            const resultDiv = document.getElementById('dom-result');
            const info = [];
            
            // 检查页面结构
            info.push(`页面标题: ${document.title}`);
            info.push(`body子元素数量: ${document.body.children.length}`);
            
            // 检查script标签
            const scripts = document.querySelectorAll('script');
            info.push(`script标签数量: ${scripts.length}`);
            
            scripts.forEach((script, index) => {
                if (script.src) {
                    info.push(`Script ${index + 1}: ${script.src}`);
                } else if (script.textContent) {
                    info.push(`Inline Script ${index + 1}: ${script.textContent.substring(0, 50)}...`);
                }
            });
            
            // 检查CSS
            const links = document.querySelectorAll('link[rel="stylesheet"]');
            info.push(`CSS文件数量: ${links.length}`);
            
            resultDiv.innerHTML = '<pre>' + info.join('\n') + '</pre>';
        }

        function clearConsole() {
            consoleOutput.textContent = '';
        }

        function testReactManually() {
            const resultDiv = document.getElementById('manual-react-test');
            
            try {
                // 尝试手动创建一个简单的React元素
                if (typeof window.React !== 'undefined') {
                    const element = window.React.createElement('div', null, 'React手动测试成功！');
                    if (typeof window.ReactDOM !== 'undefined') {
                        window.ReactDOM.render(element, resultDiv);
                    } else {
                        resultDiv.innerHTML = '❌ ReactDOM未加载';
                    }
                } else {
                    resultDiv.innerHTML = '❌ React未加载';
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ 错误: ${error.message}`;
                console.error('手动React测试失败:', error);
            }
        }

        // 页面加载时执行基础检查
        window.addEventListener('load', function() {
            setTimeout(basicCheck, 1000);
            
            // 监听错误
            window.addEventListener('error', function(e) {
                console.error('页面错误:', e.error);
            });
            
            window.addEventListener('unhandledrejection', function(e) {
                console.error('未处理的Promise拒绝:', e.reason);
            });
        });

        // 定期检查root元素变化
        setInterval(function() {
            const rootElement = document.getElementById('root');
            if (rootElement && rootElement.innerHTML) {
                console.log('Root元素有内容了:', rootElement.innerHTML.substring(0, 100));
            }
        }, 2000);
    </script>
</body>
</html>
