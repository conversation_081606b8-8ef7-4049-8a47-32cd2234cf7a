﻿import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Modal,
  Form,
  message,
  Typography,
  Row,
  Col,
  Statistic,
  Badge,
  Tooltip,
  Popconfirm,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  MessageOutlined,
  CheckOutlined,
  EyeOutlined,
  DeleteOutlined,
  RobotOutlined,
} from '@ant-design/icons';
import { commentsService } from '../../services/commentsService';
import { notesService } from '../../services/notesService';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const CommentsPage = () => {
  const [loading, setLoading] = useState(false);
  const [comments, setComments] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [replyModalVisible, setReplyModalVisible] = useState(false);
  const [selectedComment, setSelectedComment] = useState(null);
  const [notes, setNotes] = useState([]);
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    replied: 0,
    todayNew: 0
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [replyForm] = Form.useForm();

  useEffect(() => {
    loadComments();
    loadNotes();
    loadStats();
  }, []);

  // 加载留言列表
  const loadComments = async (page = 1, size = 10, filters = {}) => {
    setLoading(true);
    try {
      const params = {
        page,
        size,
        ...filters,
      };

      if (searchText) {
        params.keyword = searchText;
      }

      if (statusFilter !== 'all') {
        params.status = statusFilter.toUpperCase();
      }

      const response = await commentsService.getComments(params);

      if (response.success) {
        // 转换数据格式以匹配前端显示
        const transformedComments = (response.data || []).map(comment => ({
          id: comment.id,
          note_id: comment.note_id,
          comment_id: comment.comment_id,
          user_name: comment.author_name || '匿名用户',
          author_name: comment.author_name,
          author_id: comment.author_id,
          content: comment.content,
          note_title: getNoteTitleById(comment.note_id),
          created_at: formatDateTime(comment.created_at),
          publish_time: comment.publish_time,
          status: comment.status ? comment.status.toLowerCase() : 'new',
          reply_content: comment.reply_content,
          reply_time: comment.replied_at ? formatDateTime(comment.replied_at) : null,
          replied_at: comment.replied_at,
          likes: 0, // 这个字段需要从其他接口获取
          parent_comment_id: comment.parent_comment_id,
          updated_at: comment.updated_at,
        }));

        setComments(transformedComments);
        setPagination({
          current: response.page || page,
          pageSize: response.size || size,
          total: response.total || 0,
        });
      } else {
        message.error(response.message || '获取留言列表失败');
      }
    } catch (error) {
      console.error('获取留言列表失败:', error);
      message.error('获取留言列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 加载笔记列表
  const loadNotes = async () => {
    try {
      const response = await notesService.getNotes({ page: 1, size: 100 });
      if (response.success) {
        setNotes(response.data || []);
      }
    } catch (error) {
      console.error('获取笔记列表失败:', error);
    }
  };

  // 加载统计数据
  const loadStats = async () => {
    try {
      const response = await commentsService.getCommentsStats();
      if (response.success) {
        setStats(response.data || {
          total: 0,
          pending: 0,
          replied: 0,
          todayNew: 0
        });
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  // 获取笔记标题
  const getNoteTitleById = (noteId) => {
    const note = notes.find(n => n.id === noteId);
    return note ? note.title : '未知笔记';
  };

  // 格式化日期时间
  const formatDateTime = (dateString) => {
    if (!dateString) return '';
    try {
      return new Date(dateString).toLocaleString('zh-CN');
    } catch (error) {
      return dateString;
    }
  };

  // 处理搜索
  const handleSearch = (value) => {
    setSearchText(value);
    loadComments(1, pagination.pageSize, { keyword: value });
  };

  // 处理状态过滤
  const handleStatusFilter = (value) => {
    setStatusFilter(value);
    const filters = {};
    if (value !== 'all') {
      filters.status = value.toUpperCase();
    }
    loadComments(1, pagination.pageSize, filters);
  };

  // 处理分页变化
  const handleTableChange = (paginationInfo) => {
    loadComments(paginationInfo.current, paginationInfo.pageSize);
  };

  // 刷新数据
  const handleRefresh = () => {
    loadComments(pagination.current, pagination.pageSize);
    loadStats();
  };

  const handleReply = (comment) => {
    setSelectedComment(comment);
    setReplyModalVisible(true);
    replyForm.resetFields();
  };

  const handleSubmitReply = async (values) => {
    try {
      setLoading(true);
      const response = await commentsService.replyComment(
        selectedComment.id,
        values.reply_content
      );

      if (response.success) {
        message.success('回复发送成功');
        setReplyModalVisible(false);
        loadComments(pagination.current, pagination.pageSize);
        loadStats();
      } else {
        message.error(response.message || '回复发送失败');
      }
    } catch (error) {
      console.error('回复留言失败:', error);
      message.error('回复发送失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleMarkAsReplied = async (commentId) => {
    try {
      const response = await commentsService.updateComment(commentId, {
        status: 'REPLIED',
        reply_content: '已处理',
        replied_at: new Date().toISOString()
      });

      if (response.success) {
        message.success('标记为已回复');
        loadComments(pagination.current, pagination.pageSize);
        loadStats();
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      console.error('标记留言失败:', error);
      message.error('操作失败，请稍后重试');
    }
  };

  const handleDelete = async (commentId) => {
    try {
      // 注意：这里可能需要根据后端API调整，可能是软删除或标记为忽略
      const response = await commentsService.batchIgnore([commentId]);

      if (response.success) {
        message.success('留言已忽略');
        loadComments(pagination.current, pagination.pageSize);
        loadStats();
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      console.error('忽略留言失败:', error);
      message.error('操作失败，请稍后重试');
    }
  };

  const getStatusTag = (status) => {
    switch (status) {
      case 'new':
      case 'pending':
        return <Tag color="orange">待回复</Tag>;
      case 'replied':
        return <Tag color="green">已回复</Tag>;
      case 'ignored':
        return <Tag color="gray">已忽略</Tag>;
      default:
        return <Tag>{status || '未知'}</Tag>;
    }
  };

  const columns = [
    {
      title: '用户',
      dataIndex: 'user_name',
      key: 'user_name',
      width: 120,
      render: (text) => <Text strong>{text}</Text>,
    },
    {
      title: '留言内容',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      render: (text, record) => (
        <div>
          <Paragraph ellipsis={{ rows: 2, expandable: true }}>
            {text}
          </Paragraph>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            来自: {record.note_title}
          </Text>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => getStatusTag(status),
    },
    {
      title: '时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (text) => <Text type="secondary">{text}</Text>,
    },
    {
      title: '点赞',
      dataIndex: 'likes',
      key: 'likes',
      width: 80,
      render: (likes) => <Badge count={likes} style={{ backgroundColor: '#52c41a' }} />,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          {record.status === 'pending' && (
            <>
              <Tooltip title="回复">
                <Button
                  type="primary"
                  size="small"
                  icon={<MessageOutlined />}
                  onClick={() => handleReply(record)}
                />
              </Tooltip>
              <Tooltip title="标记已回复">
                <Button
                  size="small"
                  icon={<CheckOutlined />}
                  onClick={() => handleMarkAsReplied(record.id)}
                />
              </Tooltip>
            </>
          )}
          {record.status === 'replied' && (
            <Tooltip title="查看回复">
              <Button
                size="small"
                icon={<EyeOutlined />}
                onClick={() => {
                  Modal.info({
                    title: '回复内容',
                    content: (
                      <div>
                        <p><strong>原留言：</strong>{record.content}</p>
                        <p><strong>回复内容：</strong>{record.reply_content}</p>
                        <p><strong>回复时间：</strong>{record.reply_time}</p>
                      </div>
                    ),
                  });
                }}
              />
            </Tooltip>
          )}
          <Popconfirm
            title="确定删除这条留言吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button size="small" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="comments-page">
      <div className="page-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <div>
          <Title level={2}>留言管理</Title>
          <Paragraph type="secondary">
            管理用户留言，及时回复用户问题，提升用户体验
          </Paragraph>
        </div>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      </div>

      <Card style={{ marginBottom: 24 }}>
        <Row gutter={24}>
          <Col span={6}>
            <Statistic
              title="总留言数"
              value={stats.total}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="待回复"
              value={stats.pending}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="已回复"
              value={stats.replied}
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="今日新增"
              value={stats.todayNew}
              valueStyle={{ color: '#722ed1' }}
            />
          </Col>
        </Row>
      </Card>

      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col span={8}>
            <Input.Search
              placeholder="搜索留言内容、用户名或笔记标题"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onSearch={handleSearch}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="状态筛选"
              value={statusFilter}
              onChange={handleStatusFilter}
              style={{ width: '100%' }}
            >
              <Option value="all">全部状态</Option>
              <Option value="new">新留言</Option>
              <Option value="pending">待回复</Option>
              <Option value="replied">已回复</Option>
              <Option value="ignored">已忽略</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      <Card title="留言列表">
        <Table
          columns={columns}
          dataSource={comments}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            pageSizeOptions: ['10', '20', '50', '100'],
          }}
          onChange={handleTableChange}
        />
      </Card>

      <Modal
        title="回复留言"
        open={replyModalVisible}
        onCancel={() => setReplyModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedComment && (
          <div>
            <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 6 }}>
              <Text strong>原留言：</Text>
              <div style={{ marginTop: 8 }}>
                <Text>{selectedComment.content}</Text>
              </div>
              <div style={{ marginTop: 8 }}>
                <Text type="secondary">
                  来自：{selectedComment.user_name} | {selectedComment.created_at}
                </Text>
              </div>
            </div>
            
            <Form
              form={replyForm}
              onFinish={handleSubmitReply}
              layout="vertical"
            >
              <Form.Item
                label="回复内容"
                name="reply_content"
                rules={[{ required: true, message: '请输入回复内容' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="请输入回复内容..."
                  showCount
                  maxLength={500}
                />
              </Form.Item>
              
              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    icon={<MessageOutlined />}
                  >
                    发送回复
                  </Button>
                  <Button
                    icon={<RobotOutlined />}
                    onClick={() => {
                      const aiReply = `感谢您的留言！针对您的问题，我来为您详细解答...`;
                      replyForm.setFieldsValue({ reply_content: aiReply });
                      message.success('AI回复已生成');
                    }}
                  >
                    AI生成回复
                  </Button>
                  <Button onClick={() => setReplyModalVisible(false)}>
                    取消
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default CommentsPage;
