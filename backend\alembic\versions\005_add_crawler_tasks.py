"""Add crawler tasks tables

Revision ID: 005_add_crawler_tasks
Revises: 004_add_ai_tables
Create Date: 2025-07-22 13:15:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '005_add_crawler_tasks'
down_revision = '004_add_ai_tables'
branch_labels = None
depends_on = None


def upgrade():
    # 创建任务状态枚举
    task_status_enum = postgresql.ENUM(
        'pending', 'running', 'completed', 'failed', 'cancelled', 'paused',
        name='taskstatus'
    )
    task_status_enum.create(op.get_bind())
    
    # 创建任务类型枚举
    task_type_enum = postgresql.ENUM(
        'manual', 'scheduled', 'auto',
        name='tasktype'
    )
    task_type_enum.create(op.get_bind())
    
    # 创建爬虫配置表
    op.create_table('crawler_configs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('config_name', sa.String(length=100), nullable=False),
        sa.Column('is_default', sa.Boolean(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('crawl_interval', sa.Integer(), nullable=True),
        sa.Column('max_concurrent', sa.Integer(), nullable=True),
        sa.Column('request_delay', sa.Float(), nullable=True),
        sa.Column('retry_times', sa.Integer(), nullable=True),
        sa.Column('timeout', sa.Integer(), nullable=True),
        sa.Column('enable_proxy', sa.Boolean(), nullable=True),
        sa.Column('proxy_config', sa.Text(), nullable=True),
        sa.Column('user_agent', sa.String(length=500), nullable=True),
        sa.Column('headers', sa.Text(), nullable=True),
        sa.Column('cookies', sa.Text(), nullable=True),
        sa.Column('auto_restart', sa.Boolean(), nullable=True),
        sa.Column('auto_retry', sa.Boolean(), nullable=True),
        sa.Column('enable_scheduling', sa.Boolean(), nullable=True),
        sa.Column('notify_on_success', sa.Boolean(), nullable=True),
        sa.Column('notify_on_failure', sa.Boolean(), nullable=True),
        sa.Column('notification_config', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_crawler_configs_id'), 'crawler_configs', ['id'], unique=False)
    
    # 创建爬虫任务表
    op.create_table('crawler_tasks',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('note_id', sa.Integer(), nullable=True),
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.Column('task_name', sa.String(length=200), nullable=False),
        sa.Column('task_type', task_type_enum, nullable=True),
        sa.Column('status', task_status_enum, nullable=True),
        sa.Column('target_urls', sa.Text(), nullable=True),
        sa.Column('crawl_config', sa.Text(), nullable=True),
        sa.Column('start_time', sa.DateTime(timezone=True), nullable=True),
        sa.Column('end_time', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_update', sa.DateTime(timezone=True), nullable=True),
        sa.Column('progress', sa.Integer(), nullable=True),
        sa.Column('total_items', sa.Integer(), nullable=True),
        sa.Column('processed_items', sa.Integer(), nullable=True),
        sa.Column('success_items', sa.Integer(), nullable=True),
        sa.Column('failed_items', sa.Integer(), nullable=True),
        sa.Column('comments_found', sa.Integer(), nullable=True),
        sa.Column('new_comments', sa.Integer(), nullable=True),
        sa.Column('error_count', sa.Integer(), nullable=True),
        sa.Column('avg_response_time', sa.Float(), nullable=True),
        sa.Column('total_requests', sa.Integer(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('error_details', sa.Text(), nullable=True),
        sa.Column('log_file_path', sa.String(length=500), nullable=True),
        sa.Column('debug_info', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['account_id'], ['xiaohongshu_accounts.id'], ),
        sa.ForeignKeyConstraint(['note_id'], ['notes.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_crawler_tasks_id'), 'crawler_tasks', ['id'], unique=False)
    
    # 创建爬虫任务日志表
    op.create_table('crawler_task_logs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('task_id', sa.Integer(), nullable=False),
        sa.Column('level', sa.String(length=20), nullable=False),
        sa.Column('message', sa.Text(), nullable=False),
        sa.Column('details', sa.Text(), nullable=True),
        sa.Column('note_id', sa.Integer(), nullable=True),
        sa.Column('url', sa.String(length=500), nullable=True),
        sa.Column('request_id', sa.String(length=100), nullable=True),
        sa.Column('response_time', sa.Float(), nullable=True),
        sa.Column('memory_usage', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['note_id'], ['notes.id'], ),
        sa.ForeignKeyConstraint(['task_id'], ['crawler_tasks.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_crawler_task_logs_id'), 'crawler_task_logs', ['id'], unique=False)


def downgrade():
    # 删除表
    op.drop_index(op.f('ix_crawler_task_logs_id'), table_name='crawler_task_logs')
    op.drop_table('crawler_task_logs')
    op.drop_index(op.f('ix_crawler_tasks_id'), table_name='crawler_tasks')
    op.drop_table('crawler_tasks')
    op.drop_index(op.f('ix_crawler_configs_id'), table_name='crawler_configs')
    op.drop_table('crawler_configs')
    
    # 删除枚举类型
    task_type_enum = postgresql.ENUM(name='tasktype')
    task_type_enum.drop(op.get_bind())
    task_status_enum = postgresql.ENUM(name='taskstatus')
    task_status_enum.drop(op.get_bind())
