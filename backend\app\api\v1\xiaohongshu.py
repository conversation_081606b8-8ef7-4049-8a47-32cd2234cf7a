"""
小红书账号管理API路由
"""
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from ...models import get_db, User
from ...services.xiaohongshu_service import XiaohongshuService
from ...api.schemas.xiaohongshu import (
    XiaohongshuAccountCreate,
    XiaohongshuAccountUpdate,
    XiaohongshuAccountResponse,
    CookieData
)
from ...api.responses import success_response, paginated_response
from ...api.deps import get_current_active_user
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/accounts", response_model=dict)
def create_xiaohongshu_account(
    account_data: XiaohongshuAccountCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """创建小红书账号"""
    try:
        xiaohongshu_service = XiaohongshuService(db)
        account = xiaohongshu_service.create_account(current_user.id, account_data)
        
        # 构造响应数据
        account_response = XiaohongshuAccountResponse(
            id=account.id,
            user_id=account.user_id,
            account_name=account.account_name,
            account_id=account.account_id,
            login_phone=account.login_phone,
            login_email=account.login_email,
            is_active=account.is_active,
            last_login=account.last_login,
            created_at=account.created_at,
            updated_at=account.updated_at
        )
        
        return success_response(
            data=account_response.dict(),
            message="小红书账号创建成功"
        ).model_dump()
        
    except HTTPException as e:
        logger.warning(f"创建小红书账号失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"创建小红书账号时发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="账号创建失败，请稍后重试"
        )


@router.get("/accounts", response_model=dict)
def get_xiaohongshu_accounts(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取用户的小红书账号列表"""
    try:
        xiaohongshu_service = XiaohongshuService(db)
        
        # 计算偏移量
        skip = (page - 1) * size
        
        # 获取账号列表和总数
        accounts = xiaohongshu_service.get_user_accounts(current_user.id, skip, size)
        total = xiaohongshu_service.get_user_accounts_count(current_user.id)
        
        # 构造响应数据
        accounts_data = []
        for account in accounts:
            account_response = XiaohongshuAccountResponse(
                id=account.id,
                user_id=account.user_id,
                account_name=account.account_name,
                account_id=account.account_id,
                login_phone=account.login_phone,
                login_email=account.login_email,
                is_active=account.is_active,
                last_login=account.last_login,
                created_at=account.created_at,
                updated_at=account.updated_at
            )
            accounts_data.append(account_response.dict())
        
        return paginated_response(
            data=accounts_data,
            page=page,
            size=size,
            total=total,
            message="获取账号列表成功"
        ).model_dump()
        
    except Exception as e:
        logger.error(f"获取小红书账号列表时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取账号列表失败"
        )


@router.get("/accounts/{account_id}", response_model=dict)
def get_xiaohongshu_account(
    account_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取单个小红书账号详情"""
    try:
        xiaohongshu_service = XiaohongshuService(db)
        account = xiaohongshu_service.get_account_by_id(account_id)
        
        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账号不存在"
            )
        
        # 检查账号所有权
        if account.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限访问此账号"
            )
        
        # 构造响应数据
        account_response = XiaohongshuAccountResponse(
            id=account.id,
            user_id=account.user_id,
            account_name=account.account_name,
            account_id=account.account_id,
            login_phone=account.login_phone,
            login_email=account.login_email,
            is_active=account.is_active,
            last_login=account.last_login,
            created_at=account.created_at,
            updated_at=account.updated_at
        )
        
        return success_response(
            data=account_response.dict(),
            message="获取账号详情成功"
        ).model_dump()
        
    except HTTPException as e:
        logger.warning(f"获取小红书账号详情失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"获取小红书账号详情时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取账号详情失败"
        )


@router.put("/accounts/{account_id}", response_model=dict)
def update_xiaohongshu_account(
    account_id: int,
    account_data: XiaohongshuAccountUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """更新小红书账号信息"""
    try:
        xiaohongshu_service = XiaohongshuService(db)
        account = xiaohongshu_service.update_account(account_id, current_user.id, account_data)
        
        # 构造响应数据
        account_response = XiaohongshuAccountResponse(
            id=account.id,
            user_id=account.user_id,
            account_name=account.account_name,
            account_id=account.account_id,
            login_phone=account.login_phone,
            login_email=account.login_email,
            is_active=account.is_active,
            last_login=account.last_login,
            created_at=account.created_at,
            updated_at=account.updated_at
        )
        
        return success_response(
            data=account_response.dict(),
            message="账号信息更新成功"
        ).model_dump()
        
    except HTTPException as e:
        logger.warning(f"更新小红书账号失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"更新小红书账号时发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="账号更新失败，请稍后重试"
        )


@router.delete("/accounts/{account_id}", response_model=dict)
def delete_xiaohongshu_account(
    account_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """删除小红书账号"""
    try:
        xiaohongshu_service = XiaohongshuService(db)
        success = xiaohongshu_service.delete_account(account_id, current_user.id)
        
        if success:
            return success_response(
                data=None,
                message="账号删除成功"
            ).model_dump()
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="账号删除失败"
            )
            
    except HTTPException as e:
        logger.warning(f"删除小红书账号失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"删除小红书账号时发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="账号删除失败，请稍后重试"
        )


@router.post("/accounts/{account_id}/cookies", response_model=dict)
def update_account_cookies(
    account_id: int,
    cookie_data: CookieData,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """更新账号Cookies"""
    try:
        xiaohongshu_service = XiaohongshuService(db)
        success = xiaohongshu_service.update_cookies(account_id, current_user.id, cookie_data)
        
        if success:
            return success_response(
                data=None,
                message="Cookies更新成功"
            ).model_dump()
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cookies更新失败"
            )
            
    except HTTPException as e:
        logger.warning(f"更新账号Cookies失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"更新账号Cookies时发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Cookies更新失败，请稍后重试"
        )


@router.get("/accounts/{account_id}/cookies", response_model=dict)
def get_account_cookies(
    account_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取账号Cookies"""
    try:
        xiaohongshu_service = XiaohongshuService(db)
        cookies_data = xiaohongshu_service.get_account_cookies(account_id, current_user.id)
        
        return success_response(
            data=cookies_data,
            message="获取Cookies成功"
        ).model_dump()
        
    except HTTPException as e:
        logger.warning(f"获取账号Cookies失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"获取账号Cookies时发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取Cookies失败，请稍后重试"
        )


@router.post("/accounts/{account_id}/activate", response_model=dict)
def activate_account(
    account_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """激活账号"""
    try:
        xiaohongshu_service = XiaohongshuService(db)
        success = xiaohongshu_service.activate_account(account_id, current_user.id)
        
        if success:
            return success_response(
                data=None,
                message="账号激活成功"
            ).model_dump()
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="账号激活失败"
            )
            
    except HTTPException as e:
        logger.warning(f"激活账号失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"激活账号时发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="账号激活失败，请稍后重试"
        )


@router.post("/accounts/{account_id}/deactivate", response_model=dict)
def deactivate_account(
    account_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """停用账号"""
    try:
        xiaohongshu_service = XiaohongshuService(db)
        success = xiaohongshu_service.deactivate_account(account_id, current_user.id)
        
        if success:
            return success_response(
                data=None,
                message="账号停用成功"
            ).model_dump()
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="账号停用失败"
            )
            
    except HTTPException as e:
        logger.warning(f"停用账号失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"停用账号时发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="账号停用失败，请稍后重试"
        )
