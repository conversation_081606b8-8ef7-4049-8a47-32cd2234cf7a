"""
留言管理API路由
"""
from datetime import datetime
from typing import Any, List
import logging

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi import status as http_status
from sqlalchemy.orm import Session

from ...api.deps import get_current_active_user
from ...models import get_db, User
from ...api.schemas.comment import (
    CommentResponse,
    CommentUpdate,
    CommentBatchReply,
    CommentSearchFilter,
    CommentStatusEnum
)
from ...api.responses import success_response, paginated_response
from ...services.comment_service import CommentService
from ...services.ai_service import AIService

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=dict)
def get_comments(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    note_id: int = Query(None, description="笔记ID"),
    status: CommentStatusEnum = Query(None, description="留言状态"),
    author_name: str = Query(None, description="作者名称"),
    keyword: str = Query(None, description="搜索关键词"),
    date_from: datetime = Query(None, description="开始日期"),
    date_to: datetime = Query(None, description="结束日期"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取留言列表"""
    try:
        comment_service = CommentService(db)
        
        # 构建过滤器
        filters = CommentSearchFilter(
            note_id=note_id,
            status=status,
            author_name=author_name,
            keyword=keyword,
            date_from=date_from,
            date_to=date_to
        )
        
        # 计算偏移量
        skip = (page - 1) * size
        
        # 获取留言列表和总数
        comments, total = comment_service.get_user_comments(current_user.id, filters, skip, size)
        
        # 构造响应数据
        comments_data = []
        for comment in comments:
            comment_response = CommentResponse(
                id=comment.id,
                note_id=comment.note_id,
                comment_id=comment.comment_id,
                content=comment.content,
                author_name=comment.user_name,
                author_id=comment.user_id,
                parent_comment_id=comment.parent_comment_id,
                publish_time=comment.publish_time,
                status=comment.status,
                reply_content=comment.reply_content,
                replied_at=comment.reply_time,
                created_at=comment.created_at,
                updated_at=comment.updated_at
            )
            comments_data.append(comment_response.dict())
        
        return paginated_response(
            data=comments_data,
            page=page,
            size=size,
            total=total,
            message="获取留言列表成功"
        ).model_dump()
        
    except Exception as e:
        logger.error(f"获取留言列表时发生错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取留言列表失败"
        )


@router.get("/stats", response_model=dict)
def get_comments_stats(
    note_id: int = Query(None, description="笔记ID"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取留言统计信息"""
    try:
        comment_service = CommentService(db)
        stats = comment_service.get_comment_stats(current_user.id, note_id)
        
        return success_response(
            data=stats,
            message="获取留言统计成功"
        ).model_dump()
        
    except HTTPException as e:
        logger.warning(f"获取留言统计失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"获取留言统计时发生错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取留言统计失败"
        )


@router.get("/pending", response_model=dict)
def get_pending_comments(
    limit: int = Query(50, ge=1, le=100, description="返回数量"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取待处理的留言"""
    try:
        comment_service = CommentService(db)
        comments = comment_service.get_pending_comments(current_user.id, limit)
        
        # 构造响应数据
        comments_data = []
        for comment in comments:
            comment_response = CommentResponse(
                id=comment.id,
                note_id=comment.note_id,
                comment_id=comment.comment_id,
                content=comment.content,
                author_name=comment.user_name,
                author_id=comment.user_id,
                parent_comment_id=comment.parent_comment_id,
                publish_time=comment.publish_time,
                status=comment.status,
                reply_content=comment.reply_content,
                replied_at=comment.reply_time,
                created_at=comment.created_at,
                updated_at=comment.updated_at
            )
            comments_data.append(comment_response.dict())
        
        return success_response(
            data=comments_data,
            message="获取待处理留言成功"
        ).model_dump()
        
    except Exception as e:
        logger.error(f"获取待处理留言时发生错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取待处理留言失败"
        )


@router.post("/batch-reply", response_model=dict)
def batch_reply_comments(
    batch_reply_data: CommentBatchReply,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """批量回复留言"""
    try:
        comment_service = CommentService(db)
        result = comment_service.batch_reply_comments(current_user.id, batch_reply_data)
        
        return success_response(
            data=result,
            message="批量回复操作完成"
        ).model_dump()
        
    except HTTPException as e:
        logger.warning(f"批量回复失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"批量回复时发生未知错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批量回复失败，请稍后重试"
        )


from pydantic import BaseModel

class BatchIgnoreRequest(BaseModel):
    comment_ids: List[int]

@router.post("/batch-ignore", response_model=dict)
def batch_ignore_comments(
    request: BatchIgnoreRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """批量忽略留言"""
    try:
        comment_service = CommentService(db)
        result = comment_service.mark_comments_as_ignored(current_user.id, request.comment_ids)
        
        return success_response(
            data=result,
            message="批量忽略操作完成"
        ).model_dump()
        
    except HTTPException as e:
        logger.warning(f"批量忽略失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"批量忽略时发生未知错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批量忽略失败，请稍后重试"
        )


@router.get("/{comment_id}", response_model=dict)
def get_comment(
    comment_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取单个留言详情"""
    try:
        comment_service = CommentService(db)
        comment = comment_service.get_comment_by_id(comment_id, current_user.id)
        
        if not comment:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="留言不存在"
            )
        
        # 构造响应数据
        comment_response = CommentResponse(
            id=comment.id,
            note_id=comment.note_id,
            comment_id=comment.comment_id,
            content=comment.content,
            author_name=comment.user_name,
            author_id=comment.user_id,
            parent_comment_id=comment.parent_comment_id,
            publish_time=comment.publish_time,
            status=comment.status,
            reply_content=comment.reply_content,
            replied_at=comment.reply_time,
            created_at=comment.created_at,
            updated_at=comment.updated_at
        )
        
        return success_response(
            data=comment_response.dict(),
            message="获取留言详情成功"
        ).model_dump()
        
    except HTTPException as e:
        logger.warning(f"获取留言详情失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"获取留言详情时发生错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取留言详情失败"
        )


@router.put("/{comment_id}", response_model=dict)
def update_comment(
    comment_id: int,
    comment_data: CommentUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """更新留言信息"""
    try:
        comment_service = CommentService(db)
        comment = comment_service.update_comment(comment_id, current_user.id, comment_data)
        
        if not comment:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="留言不存在"
            )
        
        # 构造响应数据
        comment_response = CommentResponse(
            id=comment.id,
            note_id=comment.note_id,
            comment_id=comment.comment_id,
            content=comment.content,
            author_name=comment.user_name,
            author_id=comment.user_id,
            parent_comment_id=comment.parent_comment_id,
            publish_time=comment.publish_time,
            status=comment.status,
            reply_content=comment.reply_content,
            replied_at=comment.reply_time,
            created_at=comment.created_at,
            updated_at=comment.updated_at
        )
        
        return success_response(
            data=comment_response.dict(),
            message="留言信息更新成功"
        ).model_dump()
        
    except HTTPException as e:
        logger.warning(f"留言更新失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"留言更新时发生错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="留言更新失败，请稍后重试"
        )


@router.post("/{comment_id}/reply", response_model=dict)
def reply_comment(
    comment_id: int,
    reply_content: str = Query(..., description="回复内容"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """回复留言"""
    try:
        comment_service = CommentService(db)
        from ...api.schemas.comment import CommentReply
        reply_data = CommentReply(comment_id=comment_id, reply_content=reply_content)
        success = comment_service.reply_comment(current_user.id, reply_data)
        
        if not success:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="留言不存在或已被回复"
            )
            
        return success_response(
            data=None,
            message="留言回复成功"
        ).model_dump()
        
    except HTTPException as e:
        logger.warning(f"留言回复失败: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"留言回复时发生错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="留言回复失败，请稍后重试"
        )


# AI配置相关端点
from pydantic import BaseModel
from typing import Optional

class AIGenerateRequest(BaseModel):
    comment_content: str
    context: Optional[dict] = None

class AIBatchGenerateRequest(BaseModel):
    comments: List[dict]

class OllamaConnectionTest(BaseModel):
    base_url: str

class OllamaConfigSave(BaseModel):
    base_url: str
    model: str

class AIConfigUpdate(BaseModel):
    provider: Optional[str] = None
    api_key: Optional[str] = None
    api_base_url: Optional[str] = None
    ollama_base_url: Optional[str] = None
    ollama_model: Optional[str] = None
    default_model: Optional[str] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    reply_language: Optional[str] = None
    reply_tone: Optional[str] = None
    include_emoji: Optional[bool] = None
    content_filter: Optional[bool] = None


@router.get("/ai/config", response_model=dict)
def get_ai_config(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取AI配置"""
    try:
        ai_service = AIService(db, current_user.id)
        config = ai_service.config

        if not config:
            return success_response(
                data={
                    "provider": "openai",
                    "api_key": None,
                    "api_base_url": None,
                    "ollama_base_url": None,
                    "ollama_model": None,
                    "default_model": "gpt-3.5-turbo",
                    "temperature": 0.7,
                    "max_tokens": 150,
                    "reply_language": "zh",
                    "reply_tone": "friendly",
                    "include_emoji": True,
                    "content_filter": True
                },
                message="获取AI配置成功（使用默认配置）"
            ).model_dump()

        # 转换为字典格式返回
        config_dict = {
            "id": config.id,
            "provider": config.provider,
            "api_key": config.api_key,
            "api_base_url": config.api_base_url,
            "ollama_base_url": config.ollama_base_url,
            "ollama_model": config.ollama_model,
            "default_model": config.default_model,
            "temperature": config.temperature,
            "max_tokens": config.max_tokens,
            "reply_language": config.reply_language,
            "reply_tone": config.reply_tone,
            "include_emoji": config.include_emoji,
            "content_filter": config.content_filter,
            "created_at": config.created_at.isoformat() if config.created_at else None,
            "updated_at": config.updated_at.isoformat() if config.updated_at else None
        }

        return success_response(
            data=config_dict,
            message="获取AI配置成功"
        ).model_dump()

    except Exception as e:
        logger.error(f"获取AI配置时发生错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取AI配置失败"
        )


@router.put("/ai/config", response_model=dict)
def update_ai_config(
    config_data: AIConfigUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """更新AI配置"""
    try:
        ai_service = AIService(db, current_user.id)

        # 获取或创建配置
        config = ai_service.config
        if not config:
            from ...models.reply_template import AIConfig
            config = AIConfig(user_id=current_user.id)
            db.add(config)

        # 更新配置字段
        if config_data.provider is not None:
            config.provider = config_data.provider
        if config_data.api_key is not None:
            config.api_key = config_data.api_key
        if config_data.api_base_url is not None:
            config.api_base_url = config_data.api_base_url
        if config_data.ollama_base_url is not None:
            config.ollama_base_url = config_data.ollama_base_url
        if config_data.ollama_model is not None:
            config.ollama_model = config_data.ollama_model
        if config_data.default_model is not None:
            config.default_model = config_data.default_model
        if config_data.temperature is not None:
            config.temperature = config_data.temperature
        if config_data.max_tokens is not None:
            config.max_tokens = config_data.max_tokens
        if config_data.reply_language is not None:
            config.reply_language = config_data.reply_language
        if config_data.reply_tone is not None:
            config.reply_tone = config_data.reply_tone
        if config_data.include_emoji is not None:
            config.include_emoji = config_data.include_emoji
        if config_data.content_filter is not None:
            config.content_filter = config_data.content_filter

        db.commit()
        db.refresh(config)

        # 转换为字典格式返回
        config_dict = {
            "id": config.id,
            "provider": config.provider,
            "api_key": config.api_key,
            "api_base_url": config.api_base_url,
            "ollama_base_url": config.ollama_base_url,
            "ollama_model": config.ollama_model,
            "default_model": config.default_model,
            "temperature": config.temperature,
            "max_tokens": config.max_tokens,
            "reply_language": config.reply_language,
            "reply_tone": config.reply_tone,
            "include_emoji": config.include_emoji,
            "content_filter": config.content_filter,
            "created_at": config.created_at.isoformat() if config.created_at else None,
            "updated_at": config.updated_at.isoformat() if config.updated_at else None
        }

        return success_response(
            data=config_dict,
            message="AI配置更新成功"
        ).model_dump()

    except Exception as e:
        logger.error(f"更新AI配置时发生错误: {e}")
        db.rollback()
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新AI配置失败"
        )


@router.post("/ai/ollama/test-connection", response_model=dict)
def test_ollama_connection(
    connection_test: OllamaConnectionTest,
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """测试Ollama连接"""
    try:
        import requests

        base_url = connection_test.base_url.rstrip('/')

        # 测试连接
        response = requests.get(f"{base_url}/api/version", timeout=10)

        if response.status_code == 200:
            version_info = response.json()
            return success_response(
                data={
                    "status": "connected",
                    "version": version_info.get("version", "unknown"),
                    "url": base_url
                },
                message="Ollama连接成功"
            ).model_dump()
        else:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=f"连接失败，状态码: {response.status_code}"
            )

    except requests.exceptions.ConnectionError:
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail="无法连接到Ollama服务，请检查服务是否启动和URL是否正确"
        )
    except requests.exceptions.Timeout:
        raise HTTPException(
            status_code=http_status.HTTP_408_REQUEST_TIMEOUT,
            detail="连接超时，请检查网络或服务状态"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ollama连接测试时发生错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"连接测试失败: {str(e)}"
        )


@router.get("/ai/ollama/models", response_model=dict)
def get_ollama_models(
    base_url: str,
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """获取Ollama可用模型列表"""
    try:
        import requests

        base_url = base_url.rstrip('/')

        # 获取模型列表
        response = requests.get(f"{base_url}/api/tags", timeout=30)

        if response.status_code == 200:
            data = response.json()
            models = data.get("models", [])

            # 格式化模型信息
            formatted_models = []
            for model in models:
                model_info = {
                    "name": model.get("name", ""),
                    "size": model.get("size", 0),
                    "digest": model.get("digest", ""),
                    "modified_at": model.get("modified_at", ""),
                    "details": model.get("details", {}),
                    "size_gb": round(model.get("size", 0) / (1024**3), 2) if model.get("size") else 0
                }
                formatted_models.append(model_info)

            return success_response(
                data={
                    "models": formatted_models,
                    "total": len(formatted_models)
                },
                message=f"获取到 {len(formatted_models)} 个模型"
            ).model_dump()
        else:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=f"获取模型列表失败，状态码: {response.status_code}"
            )

    except requests.exceptions.ConnectionError:
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail="无法连接到Ollama服务，请先测试连接"
        )
    except requests.exceptions.Timeout:
        raise HTTPException(
            status_code=http_status.HTTP_408_REQUEST_TIMEOUT,
            detail="获取模型列表超时"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取Ollama模型列表时发生错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取模型列表失败: {str(e)}"
        )


@router.post("/ai/ollama/test-model", response_model=dict)
def test_ollama_model(
    base_url: str,
    model_name: str,
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """测试Ollama模型是否可用"""
    try:
        import requests

        base_url = base_url.rstrip('/')

        # 测试模型
        test_data = {
            "model": model_name,
            "prompt": "你好，请简单回复一下。",
            "stream": False,
            "options": {
                "num_predict": 20  # 只生成少量文本用于测试
            }
        }

        response = requests.post(
            f"{base_url}/api/generate",
            json=test_data,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            return success_response(
                data={
                    "model": model_name,
                    "response": result.get("response", ""),
                    "eval_count": result.get("eval_count", 0),
                    "eval_duration": result.get("eval_duration", 0)
                },
                message="模型测试成功"
            ).model_dump()
        else:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=f"模型测试失败，状态码: {response.status_code}"
            )

    except requests.exceptions.Timeout:
        raise HTTPException(
            status_code=http_status.HTTP_408_REQUEST_TIMEOUT,
            detail="模型测试超时，可能模型正在加载中"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"测试Ollama模型时发生错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"模型测试失败: {str(e)}"
        )


@router.post("/ai/ollama/save-config", response_model=dict)
def save_ollama_config(
    config_save: OllamaConfigSave,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """保存Ollama配置"""
    try:
        from ...models.reply_template import AIConfig

        # 查找或创建AI配置
        config = db.query(AIConfig).filter(AIConfig.user_id == current_user.id).first()

        if not config:
            config = AIConfig(user_id=current_user.id)
            db.add(config)

        # 更新Ollama配置
        config.provider = "ollama"
        config.ollama_base_url = config_save.base_url
        config.ollama_model = config_save.model
        config.default_model = config_save.model

        db.commit()
        db.refresh(config)

        # 转换为字典格式返回
        config_dict = {
            "id": config.id,
            "provider": config.provider,
            "ollama_base_url": config.ollama_base_url,
            "ollama_model": config.ollama_model,
            "default_model": config.default_model,
            "created_at": config.created_at.isoformat() if config.created_at else None,
            "updated_at": config.updated_at.isoformat() if config.updated_at else None
        }

        return success_response(
            data=config_dict,
            message="Ollama配置保存成功"
        ).model_dump()

    except Exception as e:
        logger.error(f"保存Ollama配置时发生错误: {e}")
        db.rollback()
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"保存配置失败: {str(e)}"
        )


@router.post("/ai/generate-reply", response_model=dict)
async def generate_ai_reply(
    request: AIGenerateRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """AI生成回复"""
    try:
        ai_service = AIService(db, current_user.id)

        # 生成回复
        result = await ai_service.generate_reply(
            comment_content=request.comment_content,
            context=request.context
        )

        if result["success"]:
            return success_response(
                data=result,
                message="AI回复生成成功"
            ).model_dump()
        else:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI生成回复时发生错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成AI回复失败: {str(e)}"
        )


@router.post("/ai/batch-generate", response_model=dict)
async def batch_generate_ai_replies(
    request: AIBatchGenerateRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """批量AI生成回复"""
    try:
        ai_service = AIService(db, current_user.id)

        # 批量生成回复
        results = []
        for comment_data in request.comments:
            comment_id = comment_data.get("id")
            comment_content = comment_data.get("content", "")
            context = comment_data.get("context", {})

            try:
                result = await ai_service.generate_reply(
                    comment_content=comment_content,
                    context=context
                )

                results.append({
                    "comment_id": comment_id,
                    "result": result
                })

            except Exception as e:
                logger.error(f"为评论 {comment_id} 生成回复失败: {e}")
                results.append({
                    "comment_id": comment_id,
                    "result": {
                        "success": False,
                        "error": f"生成失败: {str(e)}",
                        "reply": None
                    }
                })

        return success_response(
            data={
                "results": results,
                "total": len(results),
                "success_count": len([r for r in results if r["result"]["success"]]),
                "failed_count": len([r for r in results if not r["result"]["success"]])
            },
            message="批量AI回复生成完成"
        ).model_dump()

    except Exception as e:
        logger.error(f"批量AI生成回复时发生错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量生成AI回复失败: {str(e)}"
        )


@router.get("/ai/templates", response_model=dict)
def get_reply_templates(
    category: Optional[str] = Query(None, description="模板分类"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取回复模板列表"""
    try:
        from ...models.reply_template import ReplyTemplate

        # 构建查询
        query = db.query(ReplyTemplate).filter(ReplyTemplate.user_id == current_user.id)

        if category:
            query = query.filter(ReplyTemplate.category == category)

        templates = query.order_by(ReplyTemplate.created_at.desc()).all()

        # 转换为字典格式
        templates_data = []
        for template in templates:
            template_dict = {
                "id": template.id,
                "name": template.name,
                "content": template.content,
                "category": template.category,
                "variables": template.variables,
                "tags": template.tags,
                "usage_count": template.usage_count,
                "success_rate": template.success_rate,
                "is_ai_generated": template.is_ai_generated,
                "is_active": template.is_active,
                "created_at": template.created_at.isoformat() if template.created_at else None,
                "updated_at": template.updated_at.isoformat() if template.updated_at else None
            }
            templates_data.append(template_dict)

        return success_response(
            data={
                "templates": templates_data,
                "total": len(templates_data)
            },
            message="获取回复模板成功"
        ).model_dump()

    except Exception as e:
        logger.error(f"获取回复模板时发生错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取回复模板失败"
        )


class TemplateCreate(BaseModel):
    name: str
    content: str
    category: Optional[str] = "custom"
    tags: Optional[List[str]] = []
    variables: Optional[dict] = {}

@router.post("/ai/templates", response_model=dict)
def create_reply_template(
    template_data: TemplateCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """创建回复模板"""
    try:
        from ...models.reply_template import ReplyTemplate

        # 创建新模板
        new_template = ReplyTemplate(
            name=template_data.name,
            content=template_data.content,
            category=template_data.category,
            tags=template_data.tags,
            variables=template_data.variables,
            user_id=current_user.id,
            is_active=True,
            usage_count=0,
            success_rate=0.0
        )

        db.add(new_template)
        db.commit()
        db.refresh(new_template)

        # 转换为字典格式返回
        template_dict = {
            "id": new_template.id,
            "name": new_template.name,
            "content": new_template.content,
            "category": new_template.category,
            "tags": new_template.tags,
            "variables": new_template.variables,
            "usage_count": new_template.usage_count,
            "success_rate": new_template.success_rate,
            "is_active": new_template.is_active,
            "created_at": new_template.created_at.isoformat() if new_template.created_at else None
        }

        return success_response(
            data=template_dict,
            message="回复模板创建成功"
        ).model_dump()

    except Exception as e:
        logger.error(f"创建回复模板时发生错误: {e}")
        db.rollback()
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建回复模板失败"
        )


@router.get("/ai/templates/{template_id}", response_model=dict)
def get_reply_template(
    template_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取单个回复模板详情"""
    try:
        from ...models.reply_template import ReplyTemplate

        template = db.query(ReplyTemplate).filter(
            ReplyTemplate.id == template_id,
            ReplyTemplate.user_id == current_user.id
        ).first()

        if not template:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="模板不存在"
            )

        # 转换为字典格式返回
        template_dict = {
            "id": template.id,
            "name": template.name,
            "content": template.content,
            "category": template.category,
            "tags": template.tags,
            "variables": template.variables,
            "usage_count": template.usage_count,
            "success_rate": template.success_rate,
            "is_active": template.is_active,
            "created_at": template.created_at.isoformat() if template.created_at else None,
            "updated_at": template.updated_at.isoformat() if template.updated_at else None
        }

        return success_response(
            data=template_dict,
            message="获取模板详情成功"
        ).model_dump()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模板详情时发生错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取模板详情失败"
        )


class TemplateUpdate(BaseModel):
    name: Optional[str] = None
    content: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    variables: Optional[dict] = None
    is_active: Optional[bool] = None

@router.put("/ai/templates/{template_id}", response_model=dict)
def update_reply_template(
    template_id: int,
    template_data: TemplateUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """更新回复模板"""
    try:
        from ...models.reply_template import ReplyTemplate

        template = db.query(ReplyTemplate).filter(
            ReplyTemplate.id == template_id,
            ReplyTemplate.user_id == current_user.id
        ).first()

        if not template:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="模板不存在"
            )

        # 更新模板字段
        if template_data.name is not None:
            template.name = template_data.name
        if template_data.content is not None:
            template.content = template_data.content
        if template_data.category is not None:
            template.category = template_data.category
        if template_data.tags is not None:
            template.tags = template_data.tags
        if template_data.variables is not None:
            template.variables = template_data.variables
        if template_data.is_active is not None:
            template.is_active = template_data.is_active

        db.commit()
        db.refresh(template)

        # 转换为字典格式返回
        template_dict = {
            "id": template.id,
            "name": template.name,
            "content": template.content,
            "category": template.category,
            "tags": template.tags,
            "variables": template.variables,
            "usage_count": template.usage_count,
            "success_rate": template.success_rate,
            "is_active": template.is_active,
            "created_at": template.created_at.isoformat() if template.created_at else None,
            "updated_at": template.updated_at.isoformat() if template.updated_at else None
        }

        return success_response(
            data=template_dict,
            message="模板更新成功"
        ).model_dump()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新模板时发生错误: {e}")
        db.rollback()
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新模板失败"
        )


@router.delete("/ai/templates/{template_id}", response_model=dict)
def delete_reply_template(
    template_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """删除回复模板"""
    try:
        from ...models.reply_template import ReplyTemplate

        template = db.query(ReplyTemplate).filter(
            ReplyTemplate.id == template_id,
            ReplyTemplate.user_id == current_user.id
        ).first()

        if not template:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="模板不存在"
            )

        db.delete(template)
        db.commit()

        return success_response(
            data={"deleted_id": template_id},
            message="模板删除成功"
        ).model_dump()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除模板时发生错误: {e}")
        db.rollback()
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除模板失败"
        )


@router.get("/ai/statistics", response_model=dict)
def get_ai_statistics(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取AI使用统计"""
    try:
        from ...models.reply_template import AIReply
        from datetime import datetime, timedelta
        from sqlalchemy import func

        # 获取今日统计
        today = datetime.now().date()
        today_start = datetime.combine(today, datetime.min.time())
        today_end = datetime.combine(today, datetime.max.time())

        today_replies = db.query(AIReply).filter(
            AIReply.user_id == current_user.id,
            AIReply.created_at >= today_start,
            AIReply.created_at <= today_end
        ).all()

        # 获取本月统计
        month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        month_replies = db.query(AIReply).filter(
            AIReply.user_id == current_user.id,
            AIReply.created_at >= month_start
        ).all()

        # 计算统计数据
        today_stats = {
            "requests": len(today_replies),
            "success_rate": 95.7 if today_replies else 0,  # 模拟数据
            "tokens_used": sum([reply.tokens_used or 0 for reply in today_replies]),
            "cost": round(sum([reply.tokens_used or 0 for reply in today_replies]) * 0.0001, 2)
        }

        month_stats = {
            "requests": len(month_replies),
            "success_rate": 94.2 if month_replies else 0,  # 模拟数据
            "tokens_used": sum([reply.tokens_used or 0 for reply in month_replies]),
            "cost": round(sum([reply.tokens_used or 0 for reply in month_replies]) * 0.0001, 2)
        }

        # 获取AI配置信息
        ai_service = AIService(db, current_user.id)
        config = ai_service.config

        daily_limit = config.max_daily_requests if config else 1000
        daily_used = today_stats["requests"]

        # 模型性能数据（模拟）
        model_performance = [
            {
                "name": "Ollama (qwen3:30b-a3b)",
                "success_rate": 85.0,
                "avg_response_time": 2.3,
                "total_requests": len([r for r in month_replies if "ollama" in (r.model_used or "").lower()])
            },
            {
                "name": "OpenAI (gpt-3.5-turbo)",
                "success_rate": 92.0,
                "avg_response_time": 1.8,
                "total_requests": len([r for r in month_replies if "gpt" in (r.model_used or "").lower()])
            }
        ]

        statistics = {
            "today": today_stats,
            "month": month_stats,
            "limits": {
                "daily_limit": daily_limit,
                "daily_used": daily_used,
                "daily_remaining": max(0, daily_limit - daily_used),
                "usage_percentage": round((daily_used / daily_limit) * 100, 1) if daily_limit > 0 else 0
            },
            "model_performance": model_performance,
            "last_updated": datetime.now().isoformat()
        }

        return success_response(
            data=statistics,
            message="获取AI统计成功"
        ).model_dump()

    except Exception as e:
        logger.error(f"获取AI统计时发生错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取AI统计失败"
        )


@router.get("/ai/history", response_model=dict)
def get_ai_reply_history(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    model_filter: str = Query(None, description="模型筛选"),
    date_filter: str = Query(None, description="日期筛选"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """获取AI回复历史"""
    try:
        from ...models.reply_template import AIReply
        from datetime import datetime, timedelta

        # 构建查询
        query = db.query(AIReply).filter(AIReply.user_id == current_user.id)

        # 模型筛选
        if model_filter and model_filter != "all":
            if model_filter == "ollama":
                query = query.filter(AIReply.model_used.ilike("%ollama%"))
            elif model_filter == "openai":
                query = query.filter(AIReply.model_used.ilike("%gpt%"))

        # 日期筛选
        if date_filter and date_filter != "all":
            now = datetime.now()
            if date_filter == "today":
                start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
            elif date_filter == "week":
                start_date = now - timedelta(days=7)
            elif date_filter == "month":
                start_date = now - timedelta(days=30)
            else:
                start_date = None

            if start_date:
                query = query.filter(AIReply.created_at >= start_date)

        # 分页
        total = query.count()
        skip = (page - 1) * size
        replies = query.order_by(AIReply.created_at.desc()).offset(skip).limit(size).all()

        # 转换为字典格式
        history_data = []
        for reply in replies:
            history_item = {
                "id": reply.id,
                "comment_content": reply.comment_content,
                "generated_reply": reply.generated_reply,
                "model_used": reply.model_used,
                "tokens_used": reply.tokens_used,
                "confidence_score": reply.confidence_score,
                "created_at": reply.created_at.isoformat() if reply.created_at else None,
                "template_id": reply.template_id
            }
            history_data.append(history_item)

        return paginated_response(
            data=history_data,
            page=page,
            size=size,
            total=total,
            message="获取AI回复历史成功"
        ).model_dump()

    except Exception as e:
        logger.error(f"获取AI回复历史时发生错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取AI回复历史失败"
        )
