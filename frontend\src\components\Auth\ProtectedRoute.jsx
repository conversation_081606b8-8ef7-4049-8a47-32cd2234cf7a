import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';
import { useAuthStore } from '../../stores/authStore';

const ProtectedRoute = ({ children }) => {
  const location = useLocation();
  const { isAuthenticated, loading, initAuth, getCurrentUser } = useAuthStore();

  useEffect(() => {
    // 初始化认证状态
    initAuth();

    // 如果已认证，获取最新用户信息 - 临时禁用
    // if (isAuthenticated) {
    //   getCurrentUser();
    // }
  }, [initAuth, getCurrentUser, isAuthenticated]);

  // 显示加载状态
  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        background: '#f0f2f5'
      }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  // 未认证则重定向到登录页
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 已认证则渲染子组件
  return children;
};

export default ProtectedRoute;
