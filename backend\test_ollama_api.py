#!/usr/bin/env python3
"""
测试Ollama连接和模型管理API
"""
import requests
import json
import sys

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"
# 你的本地Ollama服务地址
OLLAMA_BASE_URL = "http://************:11434"

def login():
    """登录获取token"""
    login_data = {
        "username": "testuser2",
        "password": "testpass123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
    if response.status_code == 200:
        result = response.json()
        if "data" in result and "access_token" in result["data"]:
            return result["data"]["access_token"]
    return None

def test_ollama_connection(token):
    """测试Ollama连接"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n🔗 测试Ollama连接")
    print("-" * 30)
    
    connection_data = {
        "base_url": OLLAMA_BASE_URL
    }
    
    response = requests.post(
        f"{BASE_URL}/ai/ollama/test-connection",
        headers=headers,
        json=connection_data
    )
    
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Ollama连接测试成功!")
        
        if result.get("success") and result.get("data"):
            data = result["data"]
            print(f"   状态: {data.get('status')}")
            print(f"   版本: {data.get('version')}")
            print(f"   URL: {data.get('url')}")
        
        return True
    else:
        result = response.json()
        print(f"❌ Ollama连接测试失败: {result.get('message', '未知错误')}")
        return False

def get_ollama_models(token):
    """获取Ollama模型列表"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n📋 获取Ollama模型列表")
    print("-" * 30)
    
    params = {
        "base_url": OLLAMA_BASE_URL
    }
    
    response = requests.get(
        f"{BASE_URL}/ai/ollama/models",
        headers=headers,
        params=params
    )
    
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 获取模型列表成功!")
        
        if result.get("success") and result.get("data"):
            data = result["data"]
            models = data.get("models", [])
            total = data.get("total", 0)
            
            print(f"📊 找到 {total} 个模型:")
            
            for i, model in enumerate(models, 1):
                print(f"\n   {i}. {model.get('name', 'Unknown')}")
                print(f"      大小: {model.get('size_gb', 0)} GB")
                print(f"      修改时间: {model.get('modified_at', 'Unknown')}")
                
                # 显示模型详情
                details = model.get('details', {})
                if details:
                    print(f"      详情:")
                    for key, value in details.items():
                        print(f"        {key}: {value}")
            
            return models
        else:
            print("⚠️ 没有找到模型数据")
            return []
    else:
        result = response.json()
        print(f"❌ 获取模型列表失败: {result.get('message', '未知错误')}")
        return []

def test_ollama_model(token, model_name):
    """测试Ollama模型"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print(f"\n🧪 测试模型: {model_name}")
    print("-" * 30)
    
    params = {
        "base_url": OLLAMA_BASE_URL,
        "model_name": model_name
    }
    
    response = requests.post(
        f"{BASE_URL}/ai/ollama/test-model",
        headers=headers,
        params=params
    )
    
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 模型测试成功!")
        
        if result.get("success") and result.get("data"):
            data = result["data"]
            print(f"   模型: {data.get('model')}")
            print(f"   回复: {data.get('response', '')[:100]}...")
            print(f"   评估次数: {data.get('eval_count', 0)}")
            print(f"   评估时长: {data.get('eval_duration', 0)} ns")
        
        return True
    else:
        result = response.json()
        print(f"❌ 模型测试失败: {result.get('message', '未知错误')}")
        return False

def save_ollama_config(token, model_name):
    """保存Ollama配置"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print(f"\n💾 保存Ollama配置")
    print("-" * 30)
    
    config_data = {
        "base_url": OLLAMA_BASE_URL,
        "model": model_name
    }
    
    response = requests.post(
        f"{BASE_URL}/ai/ollama/save-config",
        headers=headers,
        json=config_data
    )
    
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Ollama配置保存成功!")
        
        if result.get("success") and result.get("data"):
            data = result["data"]
            print(f"   配置ID: {data.get('id')}")
            print(f"   提供商: {data.get('provider')}")
            print(f"   Ollama URL: {data.get('ollama_base_url')}")
            print(f"   选择的模型: {data.get('ollama_model')}")
        
        return True
    else:
        result = response.json()
        print(f"❌ 保存配置失败: {result.get('message', '未知错误')}")
        return False

def main():
    """主函数"""
    print("🤖 Ollama连接和模型管理测试")
    print("=" * 50)
    print(f"🌐 Ollama服务地址: {OLLAMA_BASE_URL}")
    
    # 1. 登录
    print("\n1️⃣ 登录测试")
    token = login()
    if not token:
        print("❌ 登录失败，无法继续测试")
        return
    
    print("✅ 登录成功!")
    
    # 2. 测试Ollama连接
    if not test_ollama_connection(token):
        print("❌ Ollama连接失败，请检查服务是否启动")
        return
    
    # 3. 获取模型列表
    models = get_ollama_models(token)
    if not models:
        print("❌ 没有可用的模型")
        return
    
    # 4. 测试第一个模型
    first_model = models[0]
    model_name = first_model.get("name")
    if model_name:
        if test_ollama_model(token, model_name):
            # 5. 保存配置
            save_ollama_config(token, model_name)
    
    print("\n🎯 测试结果总结:")
    print("✅ Ollama连接测试完成")
    print("📋 模型列表获取成功")
    print("🧪 模型测试功能正常")
    print("💾 配置保存功能正常")
    print("\n🎉 Ollama集成功能已就绪！")

if __name__ == "__main__":
    main()
