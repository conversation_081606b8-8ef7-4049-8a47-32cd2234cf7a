"""Add AI tables

Revision ID: 004
Revises: 003
Create Date: 2024-01-18 13:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '004'
down_revision = '003'
branch_labels = None
depends_on = None


def upgrade():
    # 创建回复模板表
    op.create_table('reply_templates',
        sa.<PERSON>umn('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('name', sa.String(length=100), nullable=False, comment='模板名称'),
        sa.Column('category', sa.String(length=50), nullable=False, comment='模板分类'),
        sa.Column('content', sa.Text(), nullable=False, comment='模板内容'),
        sa.Column('variables', sa.JSON(), nullable=True, comment='模板变量'),
        sa.Column('tags', sa.J<PERSON>(), nullable=True, comment='标签列表'),
        sa.Column('usage_count', sa.Integer(), nullable=True, comment='使用次数'),
        sa.Column('success_rate', sa.Integer(), nullable=True, comment='成功率百分比'),
        sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否启用'),
        sa.Column('is_ai_generated', sa.Boolean(), nullable=True, comment='是否AI生成'),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        comment='回复模板表'
    )
    op.create_index(op.f('ix_reply_templates_id'), 'reply_templates', ['id'], unique=False)

    # 创建AI回复记录表
    op.create_table('ai_replies',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('comment_id', sa.Integer(), nullable=True),
        sa.Column('template_id', sa.Integer(), nullable=True),
        sa.Column('original_content', sa.Text(), nullable=False, comment='原始留言内容'),
        sa.Column('generated_reply', sa.Text(), nullable=False, comment='AI生成的回复'),
        sa.Column('final_reply', sa.Text(), nullable=True, comment='最终发送的回复'),
        sa.Column('model_name', sa.String(length=50), nullable=False, comment='使用的AI模型'),
        sa.Column('temperature', sa.String(length=10), nullable=True, comment='温度参数'),
        sa.Column('max_tokens', sa.Integer(), nullable=True, comment='最大token数'),
        sa.Column('prompt_tokens', sa.Integer(), nullable=True, comment='提示token数'),
        sa.Column('completion_tokens', sa.Integer(), nullable=True, comment='完成token数'),
        sa.Column('total_tokens', sa.Integer(), nullable=True, comment='总token数'),
        sa.Column('confidence_score', sa.Integer(), nullable=True, comment='置信度分数'),
        sa.Column('sentiment_score', sa.String(length=20), nullable=True, comment='情感分数'),
        sa.Column('quality_rating', sa.Integer(), nullable=True, comment='质量评分1-5'),
        sa.Column('status', sa.String(length=20), nullable=True, comment='状态'),
        sa.Column('is_used', sa.Boolean(), nullable=True, comment='是否被使用'),
        sa.Column('user_feedback', sa.String(length=20), nullable=True, comment='用户反馈'),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['comment_id'], ['comments.id'], ),
        sa.ForeignKeyConstraint(['template_id'], ['reply_templates.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        comment='AI回复记录表'
    )
    op.create_index(op.f('ix_ai_replies_id'), 'ai_replies', ['id'], unique=False)

    # 创建AI规则表
    op.create_table('ai_rules',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False, comment='规则名称'),
        sa.Column('description', sa.Text(), nullable=True, comment='规则描述'),
        sa.Column('keywords', sa.JSON(), nullable=True, comment='关键词列表'),
        sa.Column('sentiment_filter', sa.JSON(), nullable=True, comment='情感过滤条件'),
        sa.Column('user_filter', sa.JSON(), nullable=True, comment='用户过滤条件'),
        sa.Column('time_filter', sa.JSON(), nullable=True, comment='时间过滤条件'),
        sa.Column('template_ids', sa.JSON(), nullable=True, comment='关联的模板ID列表'),
        sa.Column('ai_prompt', sa.Text(), nullable=True, comment='AI提示词'),
        sa.Column('reply_style', sa.String(length=50), nullable=True, comment='回复风格'),
        sa.Column('priority', sa.Integer(), nullable=True, comment='优先级'),
        sa.Column('auto_reply', sa.Boolean(), nullable=True, comment='是否自动回复'),
        sa.Column('require_approval', sa.Boolean(), nullable=True, comment='是否需要审核'),
        sa.Column('daily_limit', sa.Integer(), nullable=True, comment='每日使用限制'),
        sa.Column('usage_count_today', sa.Integer(), nullable=True, comment='今日使用次数'),
        sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否启用'),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        comment='AI规则表'
    )
    op.create_index(op.f('ix_ai_rules_id'), 'ai_rules', ['id'], unique=False)

    # 创建AI配置表
    op.create_table('ai_configs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('api_provider', sa.String(length=50), nullable=True, comment='API提供商'),
        sa.Column('api_key', sa.String(length=200), nullable=True, comment='API密钥'),
        sa.Column('api_base_url', sa.String(length=200), nullable=True, comment='API基础URL'),
        sa.Column('default_model', sa.String(length=50), nullable=True, comment='默认模型'),
        sa.Column('temperature', sa.String(length=10), nullable=True, comment='温度参数'),
        sa.Column('max_tokens', sa.Integer(), nullable=True, comment='最大token数'),
        sa.Column('reply_language', sa.String(length=10), nullable=True, comment='回复语言'),
        sa.Column('reply_tone', sa.String(length=20), nullable=True, comment='回复语调'),
        sa.Column('include_emoji', sa.Boolean(), nullable=True, comment='是否包含表情'),
        sa.Column('content_filter', sa.Boolean(), nullable=True, comment='是否启用内容过滤'),
        sa.Column('max_daily_requests', sa.Integer(), nullable=True, comment='每日最大请求数'),
        sa.Column('current_daily_requests', sa.Integer(), nullable=True, comment='当前每日请求数'),
        sa.Column('max_monthly_cost', sa.Integer(), nullable=True, comment='每月最大成本'),
        sa.Column('current_monthly_cost', sa.Integer(), nullable=True, comment='当前月成本'),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        comment='AI配置表'
    )
    op.create_index(op.f('ix_ai_configs_id'), 'ai_configs', ['id'], unique=False)

    # 设置默认值
    op.execute("ALTER TABLE reply_templates ALTER COLUMN usage_count SET DEFAULT 0")
    op.execute("ALTER TABLE reply_templates ALTER COLUMN success_rate SET DEFAULT 0")
    op.execute("ALTER TABLE reply_templates ALTER COLUMN is_active SET DEFAULT true")
    op.execute("ALTER TABLE reply_templates ALTER COLUMN is_ai_generated SET DEFAULT false")
    
    op.execute("ALTER TABLE ai_replies ALTER COLUMN status SET DEFAULT 'generated'")
    op.execute("ALTER TABLE ai_replies ALTER COLUMN is_used SET DEFAULT false")
    
    op.execute("ALTER TABLE ai_rules ALTER COLUMN priority SET DEFAULT 0")
    op.execute("ALTER TABLE ai_rules ALTER COLUMN auto_reply SET DEFAULT false")
    op.execute("ALTER TABLE ai_rules ALTER COLUMN require_approval SET DEFAULT true")
    op.execute("ALTER TABLE ai_rules ALTER COLUMN usage_count_today SET DEFAULT 0")
    op.execute("ALTER TABLE ai_rules ALTER COLUMN is_active SET DEFAULT true")
    
    op.execute("ALTER TABLE ai_configs ALTER COLUMN api_provider SET DEFAULT 'openai'")
    op.execute("ALTER TABLE ai_configs ALTER COLUMN default_model SET DEFAULT 'gpt-3.5-turbo'")
    op.execute("ALTER TABLE ai_configs ALTER COLUMN temperature SET DEFAULT '0.7'")
    op.execute("ALTER TABLE ai_configs ALTER COLUMN max_tokens SET DEFAULT 150")
    op.execute("ALTER TABLE ai_configs ALTER COLUMN reply_language SET DEFAULT 'zh'")
    op.execute("ALTER TABLE ai_configs ALTER COLUMN reply_tone SET DEFAULT 'friendly'")
    op.execute("ALTER TABLE ai_configs ALTER COLUMN include_emoji SET DEFAULT true")
    op.execute("ALTER TABLE ai_configs ALTER COLUMN content_filter SET DEFAULT true")
    op.execute("ALTER TABLE ai_configs ALTER COLUMN max_daily_requests SET DEFAULT 1000")
    op.execute("ALTER TABLE ai_configs ALTER COLUMN current_daily_requests SET DEFAULT 0")
    op.execute("ALTER TABLE ai_configs ALTER COLUMN max_monthly_cost SET DEFAULT 100")
    op.execute("ALTER TABLE ai_configs ALTER COLUMN current_monthly_cost SET DEFAULT 0")


def downgrade():
    # 删除表
    op.drop_index(op.f('ix_ai_configs_id'), table_name='ai_configs')
    op.drop_table('ai_configs')
    
    op.drop_index(op.f('ix_ai_rules_id'), table_name='ai_rules')
    op.drop_table('ai_rules')
    
    op.drop_index(op.f('ix_ai_replies_id'), table_name='ai_replies')
    op.drop_table('ai_replies')
    
    op.drop_index(op.f('ix_reply_templates_id'), table_name='reply_templates')
    op.drop_table('reply_templates')
