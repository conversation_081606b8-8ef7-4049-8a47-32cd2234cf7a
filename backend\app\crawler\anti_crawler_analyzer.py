"""
小红书反爬虫机制分析工具
分析验证码、滑块验证、请求频率限制等反爬虫措施
"""
import asyncio
import json
import time
import random
from typing import Dict, List, Optional
from playwright.async_api import async_playwright, Page, BrowserContext
from loguru import logger


class AntiCrawlerAnalyzer:
    """反爬虫机制分析器"""
    
    def __init__(self):
        self.base_url = "https://www.xiaohongshu.com"
        self.test_requests = []
        self.rate_limit_info = {}
        self.security_challenges = []
        
    async def analyze_anti_crawler_mechanisms(self) -> Dict:
        """分析反爬虫机制"""
        logger.info("开始分析小红书反爬虫机制...")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            context = await browser.new_context()
            page = await context.new_page()
            
            # 监听网络请求
            page.on("response", self._handle_response)
            
            try:
                # 1. 分析基础访问
                await self._test_basic_access(page)
                
                # 2. 分析请求频率限制
                await self._test_rate_limiting(page)
                
                # 3. 分析用户行为检测
                await self._test_behavior_detection(page)
                
                # 4. 分析验证码机制
                await self._test_captcha_mechanisms(page)
                
                # 5. 分析请求签名
                await self._analyze_request_signatures(page)
                
            except Exception as e:
                logger.error(f"分析反爬虫机制时出错: {e}")
            finally:
                await browser.close()
                
        return self._generate_anti_crawler_report()
        
    async def _handle_response(self, response) -> None:
        """处理网络响应"""
        url = response.url
        status = response.status
        
        # 记录可疑的响应
        if status in [403, 429, 503, 521]:
            request_info = {
                'timestamp': time.time(),
                'url': url,
                'status': status,
                'headers': dict(response.headers),
                'request_headers': dict(response.request.headers)
            }
            
            try:
                if 'application/json' in response.headers.get('content-type', ''):
                    content = await response.json()
                    request_info['response'] = content
            except:
                pass
                
            self.test_requests.append(request_info)
            logger.warning(f"检测到可疑响应: {status} - {url}")
            
    async def _test_basic_access(self, page: Page) -> None:
        """测试基础访问"""
        logger.info("测试基础访问...")
        
        try:
            await page.goto(self.base_url, wait_until='networkidle')
            await page.wait_for_timeout(2000)
            
            # 检查是否有验证页面
            verification_indicators = [
                'text="验证"',
                'text="人机验证"',
                '[class*="captcha"]',
                '[class*="verify"]',
                'text="请完成安全验证"'
            ]
            
            for indicator in verification_indicators:
                try:
                    element = await page.wait_for_selector(indicator, timeout=1000)
                    if element:
                        self.security_challenges.append({
                            'type': 'verification_page',
                            'indicator': indicator,
                            'timestamp': time.time()
                        })
                        logger.warning(f"检测到验证页面: {indicator}")
                except:
                    continue
                    
        except Exception as e:
            logger.error(f"基础访问测试失败: {e}")
            
    async def _test_rate_limiting(self, page: Page) -> None:
        """测试请求频率限制"""
        logger.info("测试请求频率限制...")
        
        # 快速连续请求测试
        test_urls = [
            f"{self.base_url}/explore",
            f"{self.base_url}/user/profile/123",
            f"{self.base_url}/search?keyword=test"
        ]
        
        start_time = time.time()
        request_count = 0
        
        for i in range(10):  # 发送10个快速请求
            try:
                url = random.choice(test_urls)
                await page.goto(url, timeout=5000)
                request_count += 1
                await page.wait_for_timeout(100)  # 很短的间隔
            except Exception as e:
                logger.warning(f"请求 {i+1} 失败: {e}")
                break
                
        end_time = time.time()
        
        self.rate_limit_info = {
            'total_requests': request_count,
            'duration': end_time - start_time,
            'requests_per_second': request_count / (end_time - start_time),
            'blocked_at_request': request_count if request_count < 10 else None
        }
        
        logger.info(f"频率测试完成: {request_count}/10 请求成功")
        
    async def _test_behavior_detection(self, page: Page) -> None:
        """测试用户行为检测"""
        logger.info("测试用户行为检测...")
        
        try:
            await page.goto(f"{self.base_url}/explore")
            
            # 测试1: 无鼠标移动的快速操作
            await page.evaluate("window.scrollTo(0, 1000)")
            await page.wait_for_timeout(100)
            await page.evaluate("window.scrollTo(0, 2000)")
            await page.wait_for_timeout(100)
            
            # 测试2: 模拟人类行为
            await page.mouse.move(100, 100)
            await page.wait_for_timeout(500)
            await page.mouse.move(200, 200)
            await page.wait_for_timeout(300)
            await page.evaluate("window.scrollTo(0, 3000)")
            await page.wait_for_timeout(1000)
            
            # 检查是否触发了行为检测
            behavior_indicators = [
                '[class*="robot"]',
                'text="异常行为"',
                'text="请稍后再试"'
            ]
            
            for indicator in behavior_indicators:
                try:
                    element = await page.wait_for_selector(indicator, timeout=1000)
                    if element:
                        self.security_challenges.append({
                            'type': 'behavior_detection',
                            'indicator': indicator,
                            'timestamp': time.time()
                        })
                        logger.warning(f"检测到行为检测: {indicator}")
                except:
                    continue
                    
        except Exception as e:
            logger.error(f"行为检测测试失败: {e}")
            
    async def _test_captcha_mechanisms(self, page: Page) -> None:
        """测试验证码机制"""
        logger.info("测试验证码机制...")
        
        captcha_types = {
            'image_captcha': ['img[src*="captcha"]', '[class*="captcha"] img'],
            'slider_captcha': ['[class*="slider"]', '[class*="slide"]'],
            'click_captcha': ['text="点击"', 'text="选择"'],
            'puzzle_captcha': ['[class*="puzzle"]', '[class*="jigsaw"]']
        }
        
        for captcha_type, selectors in captcha_types.items():
            for selector in selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        self.security_challenges.append({
                            'type': captcha_type,
                            'selector': selector,
                            'count': len(elements),
                            'timestamp': time.time()
                        })
                        logger.info(f"发现{captcha_type}: {selector}")
                except:
                    continue
                    
    async def _analyze_request_signatures(self, page: Page) -> None:
        """分析请求签名"""
        logger.info("分析请求签名...")
        
        # 执行一些操作来触发API请求
        try:
            await page.goto(f"{self.base_url}/explore")
            await page.wait_for_timeout(2000)
            
            # 滚动页面触发更多请求
            for i in range(3):
                await page.evaluate(f"window.scrollTo(0, {(i+1)*1000})")
                await page.wait_for_timeout(1000)
                
        except Exception as e:
            logger.error(f"请求签名分析失败: {e}")
            
    def _generate_anti_crawler_report(self) -> Dict:
        """生成反爬虫分析报告"""
        report = {
            'security_challenges': self.security_challenges,
            'rate_limit_info': self.rate_limit_info,
            'blocked_requests': [req for req in self.test_requests if req['status'] in [403, 429, 503]],
            'analysis_summary': {
                'total_challenges': len(self.security_challenges),
                'blocked_request_count': len([req for req in self.test_requests if req['status'] in [403, 429, 503]]),
                'rate_limit_triggered': self.rate_limit_info.get('blocked_at_request') is not None
            },
            'evasion_strategies': self._generate_evasion_strategies(),
            'risk_assessment': self._assess_risk_level()
        }
        
        return report
        
    def _generate_evasion_strategies(self) -> List[Dict]:
        """生成规避策略"""
        strategies = [
            {
                'strategy': 'request_frequency_control',
                'description': '控制请求频率，建议间隔2-5秒',
                'implementation': '使用随机延时，模拟人类浏览行为'
            },
            {
                'strategy': 'user_agent_rotation',
                'description': '轮换User-Agent',
                'implementation': '使用真实浏览器的User-Agent列表'
            },
            {
                'strategy': 'proxy_rotation',
                'description': '使用代理IP轮换',
                'implementation': '建立代理池，定期切换IP'
            },
            {
                'strategy': 'behavior_simulation',
                'description': '模拟真实用户行为',
                'implementation': '添加鼠标移动、随机停顿、滚动等行为'
            },
            {
                'strategy': 'session_management',
                'description': '合理管理会话',
                'implementation': '保持登录状态，避免频繁登录'
            }
        ]
        
        # 根据检测到的安全挑战调整策略
        if any(challenge['type'] == 'verification_page' for challenge in self.security_challenges):
            strategies.append({
                'strategy': 'captcha_handling',
                'description': '处理验证码挑战',
                'implementation': '集成验证码识别服务或人工处理'
            })
            
        return strategies
        
    def _assess_risk_level(self) -> str:
        """评估风险等级"""
        risk_score = 0
        
        # 基于检测到的安全措施计算风险分数
        risk_score += len(self.security_challenges) * 2
        risk_score += len([req for req in self.test_requests if req['status'] in [403, 429, 503]]) * 3
        
        if self.rate_limit_info.get('blocked_at_request'):
            risk_score += 5
            
        if risk_score <= 5:
            return "低风险"
        elif risk_score <= 15:
            return "中等风险"
        else:
            return "高风险"
            
    def save_anti_crawler_report(self, report: Dict, filename: str = "anti_crawler_analysis.json") -> None:
        """保存反爬虫分析报告"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        logger.info(f"反爬虫分析报告已保存到: {filename}")


async def main():
    """主函数"""
    analyzer = AntiCrawlerAnalyzer()
    
    logger.info("开始小红书反爬虫机制分析...")
    report = await analyzer.analyze_anti_crawler_mechanisms()
    
    logger.info("分析完成，生成报告...")
    analyzer.save_anti_crawler_report(report)
    
    # 打印关键发现
    summary = report['analysis_summary']
    logger.info(f"检测到的安全挑战数: {summary['total_challenges']}")
    logger.info(f"被阻止的请求数: {summary['blocked_request_count']}")
    logger.info(f"触发频率限制: {summary['rate_limit_triggered']}")
    logger.info(f"风险评估: {report['risk_assessment']}")


if __name__ == "__main__":
    asyncio.run(main())
