/* 主题CSS变量定义 */
:root {
  /* 浅色主题默认变量 */
  --theme-primary: #1890ff;
  --theme-primary-hover: #40a9ff;
  --theme-primary-active: #096dd9;
  
  --theme-body-bg: #ffffff;
  --theme-component-bg: #ffffff;
  --theme-layout-bg: #f0f2f5;
  --theme-sider-bg: #ffffff;
  --theme-header-bg: #ffffff;
  
  --theme-text-primary: rgba(0, 0, 0, 0.85);
  --theme-text-secondary: rgba(0, 0, 0, 0.65);
  --theme-text-disabled: rgba(0, 0, 0, 0.25);
  
  --theme-border-color: #d9d9d9;
  --theme-border-color-split: #f0f0f0;
  
  --theme-success: #52c41a;
  --theme-warning: #faad14;
  --theme-error: #ff4d4f;
  --theme-info: #1890ff;
  
  --theme-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  --theme-card-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  
  --theme-scrollbar-track: #f1f1f1;
  --theme-scrollbar-thumb: #c1c1c1;
  --theme-scrollbar-thumb-hover: #a8a8a8;
}

/* 浅色主题样式 */
.light-theme {
  color-scheme: light;
}

.light-theme .ant-layout {
  background: var(--theme-layout-bg) !important;
}

.light-theme .ant-layout-sider {
  background: var(--theme-sider-bg) !important;
}

.light-theme .ant-layout-header {
  background: var(--theme-header-bg) !important;
  border-bottom: 1px solid var(--theme-border-color-split);
}

.light-theme .ant-card {
  background: var(--theme-component-bg) !important;
  border-color: var(--theme-border-color) !important;
  box-shadow: var(--theme-card-shadow) !important;
}

.light-theme .ant-menu {
  background: var(--theme-component-bg) !important;
  color: var(--theme-text-primary) !important;
}

.light-theme .ant-menu-item {
  color: var(--theme-text-primary) !important;
}

.light-theme .ant-menu-item:hover {
  background-color: rgba(24, 144, 255, 0.1) !important;
}

.light-theme .ant-menu-item-selected {
  background-color: rgba(24, 144, 255, 0.15) !important;
  color: var(--theme-primary) !important;
}

/* 深色主题样式 */
.dark-theme {
  color-scheme: dark;
}

.dark-theme .ant-layout {
  background: var(--theme-layout-bg) !important;
}

.dark-theme .ant-layout-sider {
  background: var(--theme-sider-bg) !important;
}

.dark-theme .ant-layout-header {
  background: var(--theme-header-bg) !important;
  border-bottom: 1px solid var(--theme-border-color-split);
}

.dark-theme .ant-card {
  background: var(--theme-component-bg) !important;
  border-color: var(--theme-border-color) !important;
  box-shadow: var(--theme-card-shadow) !important;
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-card .ant-card-head {
  border-bottom-color: var(--theme-border-color) !important;
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-card .ant-card-head-title {
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-menu {
  background: var(--theme-component-bg) !important;
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-menu-item {
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-menu-item:hover {
  background-color: rgba(24, 144, 255, 0.2) !important;
}

.dark-theme .ant-menu-item-selected {
  background-color: rgba(24, 144, 255, 0.3) !important;
  color: var(--theme-primary) !important;
}

.dark-theme .ant-menu-submenu-title {
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-input {
  background: var(--theme-component-bg) !important;
  border-color: var(--theme-border-color) !important;
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-input:focus,
.dark-theme .ant-input-focused {
  border-color: var(--theme-primary) !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.dark-theme .ant-select-selector {
  background: var(--theme-component-bg) !important;
  border-color: var(--theme-border-color) !important;
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-select-selection-item {
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-btn {
  border-color: var(--theme-border-color) !important;
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger) {
  background: var(--theme-component-bg) !important;
}

.dark-theme .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):hover {
  border-color: var(--theme-primary) !important;
  color: var(--theme-primary) !important;
}

.dark-theme .ant-table {
  background: var(--theme-component-bg) !important;
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-table-thead > tr > th {
  background: var(--theme-component-bg) !important;
  border-bottom-color: var(--theme-border-color) !important;
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-table-tbody > tr > td {
  border-bottom-color: var(--theme-border-color-split) !important;
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-table-tbody > tr:hover > td {
  background: rgba(255, 255, 255, 0.04) !important;
}

.dark-theme .ant-modal-content {
  background: var(--theme-component-bg) !important;
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-modal-header {
  background: var(--theme-component-bg) !important;
  border-bottom-color: var(--theme-border-color) !important;
}

.dark-theme .ant-modal-title {
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-modal-close {
  color: var(--theme-text-secondary) !important;
}

.dark-theme .ant-modal-close:hover {
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-drawer-content {
  background: var(--theme-component-bg) !important;
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-drawer-header {
  background: var(--theme-component-bg) !important;
  border-bottom-color: var(--theme-border-color) !important;
}

.dark-theme .ant-drawer-title {
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-form-item-label > label {
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-typography {
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-typography.ant-typography-secondary {
  color: var(--theme-text-secondary) !important;
}

.dark-theme .ant-statistic-content {
  color: var(--theme-text-primary) !important;
}

.dark-theme .ant-statistic-title {
  color: var(--theme-text-secondary) !important;
}

.dark-theme .ant-tabs-tab {
  color: var(--theme-text-secondary) !important;
}

.dark-theme .ant-tabs-tab-active {
  color: var(--theme-primary) !important;
}

.dark-theme .ant-tabs-ink-bar {
  background: var(--theme-primary) !important;
}

.dark-theme .ant-divider {
  border-color: var(--theme-border-color) !important;
}

/* 滚动条主题样式 */
.light-theme ::-webkit-scrollbar-track {
  background: var(--theme-scrollbar-track);
}

.light-theme ::-webkit-scrollbar-thumb {
  background: var(--theme-scrollbar-thumb);
}

.light-theme ::-webkit-scrollbar-thumb:hover {
  background: var(--theme-scrollbar-thumb-hover);
}

.dark-theme ::-webkit-scrollbar-track {
  background: var(--theme-scrollbar-track);
}

.dark-theme ::-webkit-scrollbar-thumb {
  background: var(--theme-scrollbar-thumb);
}

.dark-theme ::-webkit-scrollbar-thumb:hover {
  background: var(--theme-scrollbar-thumb-hover);
}

/* 主题切换动画 */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

/* 主题切换按钮样式 */
.theme-switch {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.theme-switch:hover {
  background-color: rgba(24, 144, 255, 0.1);
}

.theme-switch-icon {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.theme-switch:hover .theme-switch-icon {
  transform: rotate(180deg);
}

/* 主题预览卡片 */
.theme-preview-card {
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.theme-preview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.theme-preview-card.active {
  border-color: var(--theme-primary);
}

.theme-preview-card.active::after {
  content: '✓';
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  background: var(--theme-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.theme-preview-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.theme-preview-header {
  height: 20px;
  border-radius: 4px;
  opacity: 0.8;
}

.theme-preview-body {
  height: 40px;
  border-radius: 4px;
  opacity: 0.6;
}

.theme-preview-footer {
  height: 16px;
  border-radius: 4px;
  opacity: 0.4;
  width: 60%;
}
