import React from 'react'
import ReactDOM from 'react-dom/client'

console.log('main-simple.jsx 开始执行');

// 最简单的React组件
function SimpleApp() {
  console.log('SimpleApp 组件渲染');
  return (
    <div style={{
      padding: '20px',
      fontSize: '18px',
      color: 'blue',
      border: '2px solid red'
    }}>
      <h1>🎉 React应用成功渲染！</h1>
      <p>如果你能看到这个消息，说明React基础功能正常。</p>
      <p>当前时间: {new Date().toLocaleString()}</p>
    </div>
  );
}

console.log('准备获取root元素');
const rootElement = document.getElementById('root');
console.log('Root元素:', rootElement);

if (rootElement) {
  console.log('创建React根');
  const root = ReactDOM.createRoot(rootElement);
  console.log('渲染SimpleApp');
  root.render(<SimpleApp />);
  console.log('渲染完成');
} else {
  console.error('找不到root元素！');
  document.body.innerHTML = '<h1 style="color: red;">错误：找不到root元素！</h1>';
}
