"""
API响应格式统一
"""
from typing import Any, Optional, Dict, List
from pydantic import BaseModel


class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = True
    message: str = "操作成功"
    data: Optional[Any] = None
    
    
class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = False
    message: str
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class PaginatedResponse(BaseModel):
    """分页响应模型"""
    success: bool = True
    message: str = "获取成功"
    data: List[Any]
    pagination: Dict[str, Any]
    
    
def success_response(data: Any = None, message: str = "操作成功") -> BaseResponse:
    """成功响应"""
    return BaseResponse(success=True, message=message, data=data)


def error_response(
    message: str, 
    error_code: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> ErrorResponse:
    """错误响应"""
    return ErrorResponse(
        success=False,
        message=message,
        error_code=error_code,
        details=details
    )


def paginated_response(
    data: List[Any],
    page: int,
    size: int,
    total: int,
    message: str = "获取成功"
) -> PaginatedResponse:
    """分页响应"""
    total_pages = (total + size - 1) // size
    
    pagination = {
        "page": page,
        "size": size,
        "total": total,
        "total_pages": total_pages,
        "has_next": page < total_pages,
        "has_prev": page > 1
    }
    
    return PaginatedResponse(
        success=True,
        message=message,
        data=data,
        pagination=pagination
    )
