import { request } from './api';

export const aiService = {
  // 生成AI回复
  generateReply: async (commentContent, context = null, templateId = null) => {
    const response = await request.post('/comments/ai/generate-reply', {
      comment_content: commentContent,
      context: context,
      template_id: templateId,
    });
    return response;
  },

  // 获取回复建议
  getReplysuggestions: async (commentContent) => {
    const response = await request.post('/ai/generate-suggestions', {
      comment_content: commentContent,
    });
    return response;
  },

  // 批量生成回复
  batchGenerateReplies: async (comments) => {
    const response = await request.post('/comments/ai/batch-generate', {
      comments: comments,
    });
    return response;
  },

  // 回复模板管理
  getTemplates: async (params = {}) => {
    const response = await request.get('/comments/ai/templates', { params });
    return response;
  },

  createTemplate: async (templateData) => {
    const response = await request.post('/comments/ai/templates', templateData);
    return response;
  },

  updateTemplate: async (id, templateData) => {
    const response = await request.put(`/comments/ai/templates/${id}`, templateData);
    return response;
  },

  deleteTemplate: async (id) => {
    const response = await request.delete(`/comments/ai/templates/${id}`);
    return response;
  },

  getTemplate: async (id) => {
    const response = await request.get(`/comments/ai/templates/${id}`);
    return response;
  },

  // AI规则管理
  getRules: async (params = {}) => {
    const response = await request.get('/ai/rules', { params });
    return response;
  },

  createRule: async (ruleData) => {
    const response = await request.post('/ai/rules', ruleData);
    return response;
  },

  updateRule: async (id, ruleData) => {
    const response = await request.put(`/ai/rules/${id}`, ruleData);
    return response;
  },

  deleteRule: async (id) => {
    const response = await request.delete(`/ai/rules/${id}`);
    return response;
  },

  // AI配置管理
  getConfig: async () => {
    const response = await request.get('/comments/ai/config');
    return response;
  },

  updateConfig: async (configData) => {
    const response = await request.put('/comments/ai/config', configData);
    return response;
  },

  // Ollama配置管理
  testOllamaConnection: async (baseUrl) => {
    const response = await request.post('/comments/ai/ollama/test-connection', {
      base_url: baseUrl,
    });
    return response;
  },

  getOllamaModels: async (baseUrl) => {
    const response = await request.get('/comments/ai/ollama/models', {
      params: { base_url: baseUrl },
    });
    return response;
  },

  testOllamaModel: async (baseUrl, modelName) => {
    const response = await request.post('/comments/ai/ollama/test-model', null, {
      params: { base_url: baseUrl, model_name: modelName },
    });
    return response;
  },

  saveOllamaConfig: async (baseUrl, model) => {
    const response = await request.post('/comments/ai/ollama/save-config', {
      base_url: baseUrl,
      model: model,
    });
    return response;
  },

  // AI回复历史
  getAIReplies: async (params = {}) => {
    const response = await request.get('/ai/replies', { params });
    return response;
  },

  updateReplyFeedback: async (replyId, feedback) => {
    const response = await request.put(`/ai/replies/${replyId}/feedback`, {
      feedback: feedback,
    });
    return response;
  },

  // 测试AI连接
  testConnection: async () => {
    const response = await request.post('/ai/test-connection');
    return response;
  },

  // 获取AI使用统计
  getUsageStats: async () => {
    const response = await request.get('/comments/ai/statistics');
    return response;
  },

  // 获取AI回复历史
  getAIHistory: async (params = {}) => {
    const response = await request.get('/comments/ai/history', { params });
    return response;
  },
};
