#!/usr/bin/env python3
"""
测试数据库连接池修复后的API功能
"""
import requests
import json

# API配置
BASE_URL = "http://localhost:8000/api/v1"

def test_health_check():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查成功: {data}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_login():
    """测试登录功能"""
    print("\n🔍 测试登录功能...")
    
    try:
        login_data = {
            'username': 'testuser2',
            'password': 'testpass123'
        }
        
        response = requests.post(f'{BASE_URL}/auth/login', data=login_data, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 登录成功!")
            return data['data']['access_token']
        else:
            print(f"❌ 登录失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_notes_api(token):
    """测试笔记API"""
    print("\n🔍 测试笔记API...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{BASE_URL}/notes/", headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 笔记API调用成功!")
            print(f"笔记数量: {len(data.get('data', []))}")
            return True
        else:
            print(f"❌ 笔记API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_multiple_requests(token):
    """测试多个并发请求，验证连接池"""
    print("\n🔍 测试多个并发请求...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    success_count = 0
    total_requests = 5
    
    for i in range(total_requests):
        try:
            response = requests.get(f"{BASE_URL}/notes/", headers=headers, timeout=10)
            if response.status_code == 200:
                success_count += 1
                print(f"  请求 {i+1}: ✅ 成功")
            else:
                print(f"  请求 {i+1}: ❌ 失败 ({response.status_code})")
        except Exception as e:
            print(f"  请求 {i+1}: ❌ 异常 ({e})")
    
    print(f"\n并发请求结果: {success_count}/{total_requests} 成功")
    return success_count == total_requests

def main():
    """主测试函数"""
    print("🚀 数据库连接池修复验证...")
    print("=" * 50)
    
    # 1. 健康检查
    print("1️⃣ 健康检查")
    health_ok = test_health_check()
    
    if not health_ok:
        print("❌ 健康检查失败，测试终止")
        return
    
    # 2. 登录测试
    print("\n" + "=" * 50)
    print("2️⃣ 登录测试")
    token = test_login()
    
    if not token:
        print("❌ 登录失败，测试终止")
        return
    
    # 3. 笔记API测试
    print("\n" + "=" * 50)
    print("3️⃣ 笔记API测试")
    notes_ok = test_notes_api(token)
    
    # 4. 并发请求测试
    print("\n" + "=" * 50)
    print("4️⃣ 并发请求测试")
    concurrent_ok = test_multiple_requests(token)
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 测试结果总结:")
    
    results = {
        "健康检查": health_ok,
        "登录功能": token is not None,
        "笔记API": notes_ok,
        "并发请求": concurrent_ok
    }
    
    all_passed = all(results.values())
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    if all_passed:
        print("\n🎉 所有测试通过！数据库连接池修复成功")
        print("✅ 不再出现 'set_session cannot be used inside a transaction' 错误")
        print("✅ API可以正常处理多个并发请求")
        print("✅ 网络连接问题已完全解决")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
