#!/usr/bin/env python3
"""
检查数据库中的枚举值
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.database import engine
from sqlalchemy import text

def check_enum_values():
    """检查数据库中的枚举值"""
    try:
        with engine.connect() as conn:
            # 检查tasktype枚举值
            result = conn.execute(text("""
                SELECT enumlabel 
                FROM pg_enum 
                WHERE enumtypid = (
                    SELECT oid FROM pg_type WHERE typname = 'tasktype'
                )
                ORDER BY enumsortorder
            """))
            
            tasktype_values = [row[0] for row in result.fetchall()]
            print("TaskType枚举值:", tasktype_values)
            
            # 检查taskstatus枚举值
            result = conn.execute(text("""
                SELECT enumlabel 
                FROM pg_enum 
                WHERE enumtypid = (
                    SELECT oid FROM pg_type WHERE typname = 'taskstatus'
                )
                ORDER BY enumsortorder
            """))
            
            taskstatus_values = [row[0] for row in result.fetchall()]
            print("TaskStatus枚举值:", taskstatus_values)
            
    except Exception as e:
        print(f"检查枚举值失败: {e}")

if __name__ == "__main__":
    check_enum_values()
