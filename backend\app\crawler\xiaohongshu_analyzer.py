"""
小红书Web端接口分析工具
用于分析小红书的网络请求，找到留言相关的API接口
"""
import asyncio
import json
import re
from typing import Dict, List, Optional
from playwright.async_api import async_playwright, Page, Response
from loguru import logger
import time


class XiaohongshuAnalyzer:
    """小红书接口分析器"""
    
    def __init__(self):
        self.base_url = "https://www.xiaohongshu.com"
        self.requests_log = []
        self.api_patterns = {
            'comments': [
                r'/api/sns/web/v1/comment',
                r'/api/sns/web/v2/comment',
                r'/fe_api/burdock/weixin/v2/note/comment',
                r'/api/sns/web/v1/note/comment'
            ],
            'notes': [
                r'/api/sns/web/v1/note',
                r'/api/sns/web/v2/note',
                r'/fe_api/burdock/weixin/v2/note'
            ],
            'user': [
                r'/api/sns/web/v1/user',
                r'/api/sns/web/v2/user'
            ]
        }
        
    async def setup_page(self, page: Page) -> None:
        """设置页面监听器"""
        # 监听所有网络请求
        page.on("response", self._handle_response)
        
        # 设置用户代理
        await page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
    async def _handle_response(self, response: Response) -> None:
        """处理网络响应"""
        url = response.url
        method = response.request.method
        status = response.status
        
        # 记录所有API请求
        if '/api/' in url or '/fe_api/' in url:
            request_info = {
                'timestamp': time.time(),
                'method': method,
                'url': url,
                'status': status,
                'headers': dict(response.headers),
                'request_headers': dict(response.request.headers)
            }
            
            # 尝试获取响应内容
            try:
                if 'application/json' in response.headers.get('content-type', ''):
                    content = await response.json()
                    request_info['response'] = content
            except Exception as e:
                logger.warning(f"无法解析响应内容: {e}")
                
            self.requests_log.append(request_info)
            
            # 检查是否是我们关注的API
            self._analyze_api_pattern(request_info)
            
    def _analyze_api_pattern(self, request_info: Dict) -> None:
        """分析API模式"""
        url = request_info['url']
        
        for category, patterns in self.api_patterns.items():
            for pattern in patterns:
                if re.search(pattern, url):
                    logger.info(f"发现{category}相关API: {url}")
                    logger.info(f"请求方法: {request_info['method']}")
                    logger.info(f"状态码: {request_info['status']}")
                    
                    # 分析请求头中的关键信息
                    headers = request_info['request_headers']
                    if 'x-s' in headers:
                        logger.info(f"发现签名参数 x-s: {headers['x-s'][:50]}...")
                    if 'x-t' in headers:
                        logger.info(f"发现时间戳参数 x-t: {headers['x-t']}")
                    
    async def analyze_note_page(self, note_url: str) -> Dict:
        """分析笔记页面"""
        logger.info(f"开始分析笔记页面: {note_url}")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            context = await browser.new_context()
            page = await context.new_page()
            
            await self.setup_page(page)
            
            try:
                # 访问笔记页面
                await page.goto(note_url, wait_until='networkidle')
                await page.wait_for_timeout(3000)
                
                # 尝试滚动到评论区
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await page.wait_for_timeout(2000)
                
                # 查找评论相关元素
                comment_selectors = [
                    '[data-testid="comment"]',
                    '.comment-item',
                    '.note-comment',
                    '[class*="comment"]'
                ]
                
                for selector in comment_selectors:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        logger.info(f"找到评论元素: {selector}, 数量: {len(elements)}")
                        break
                
                # 等待更多网络请求
                await page.wait_for_timeout(5000)
                
            except Exception as e:
                logger.error(f"分析页面时出错: {e}")
            finally:
                await browser.close()
                
        return self._generate_analysis_report()
        
    def _generate_analysis_report(self) -> Dict:
        """生成分析报告"""
        report = {
            'total_requests': len(self.requests_log),
            'api_requests': [],
            'comment_apis': [],
            'note_apis': [],
            'auth_info': {},
            'headers_analysis': {}
        }
        
        for req in self.requests_log:
            if '/api/' in req['url'] or '/fe_api/' in req['url']:
                report['api_requests'].append({
                    'url': req['url'],
                    'method': req['method'],
                    'status': req['status']
                })
                
                # 分析评论相关API
                if any(pattern in req['url'] for pattern in ['comment', 'Comment']):
                    report['comment_apis'].append(req)
                    
                # 分析笔记相关API
                if any(pattern in req['url'] for pattern in ['note', 'Note']):
                    report['note_apis'].append(req)
                    
                # 分析认证信息
                headers = req['request_headers']
                if 'x-s' in headers:
                    report['auth_info']['x-s'] = headers['x-s'][:50] + '...'
                if 'x-t' in headers:
                    report['auth_info']['x-t'] = headers['x-t']
                if 'cookie' in headers:
                    report['auth_info']['has_cookie'] = True
                    
        return report
        
    def save_analysis_report(self, report: Dict, filename: str = "xiaohongshu_analysis.json") -> None:
        """保存分析报告"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        logger.info(f"分析报告已保存到: {filename}")


async def main():
    """主函数"""
    analyzer = XiaohongshuAnalyzer()
    
    # 示例笔记URL（需要替换为实际的笔记链接）
    note_url = "https://www.xiaohongshu.com/explore/[note_id]"
    
    logger.info("开始小红书接口分析...")
    report = await analyzer.analyze_note_page(note_url)
    
    logger.info("分析完成，生成报告...")
    analyzer.save_analysis_report(report)
    
    # 打印关键发现
    logger.info(f"总请求数: {report['total_requests']}")
    logger.info(f"API请求数: {len(report['api_requests'])}")
    logger.info(f"评论相关API: {len(report['comment_apis'])}")
    logger.info(f"笔记相关API: {len(report['note_apis'])}")


if __name__ == "__main__":
    asyncio.run(main())
