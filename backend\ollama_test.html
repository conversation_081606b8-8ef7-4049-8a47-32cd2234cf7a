<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ollama连接测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #666;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .model-item {
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 6px;
            background-color: white;
        }
        .model-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .model-details {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
        .model-actions {
            margin-top: 10px;
        }
        .loading {
            display: none;
            color: #007bff;
            font-style: italic;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Ollama连接和模型管理测试</h1>
        
        <!-- 登录部分 -->
        <div class="section">
            <h2>1️⃣ 用户登录</h2>
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" value="testuser2">
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="testpass123">
            </div>
            <button onclick="login()">登录</button>
            <div id="loginResult" class="result hidden"></div>
        </div>

        <!-- Ollama连接测试 -->
        <div class="section">
            <h2>2️⃣ Ollama连接测试</h2>
            <div class="form-group">
                <label for="ollamaUrl">Ollama服务地址:</label>
                <input type="text" id="ollamaUrl" value="http://192.168.1.98:11434">
            </div>
            <button onclick="testConnection()" id="testConnBtn">测试连接</button>
            <div class="loading" id="connLoading">正在测试连接...</div>
            <div id="connectionResult" class="result hidden"></div>
        </div>

        <!-- 模型列表 -->
        <div class="section">
            <h2>3️⃣ 获取模型列表</h2>
            <button onclick="getModels()" id="getModelsBtn">获取模型列表</button>
            <div class="loading" id="modelsLoading">正在获取模型列表...</div>
            <div id="modelsResult" class="result hidden"></div>
            <div id="modelsList"></div>
        </div>

        <!-- 配置保存 -->
        <div class="section">
            <h2>4️⃣ 保存配置</h2>
            <div class="form-group">
                <label for="selectedModel">选择的模型:</label>
                <input type="text" id="selectedModel" readonly placeholder="请先获取模型列表并选择模型">
            </div>
            <button onclick="saveConfig()" id="saveConfigBtn" disabled>保存配置</button>
            <div class="loading" id="saveLoading">正在保存配置...</div>
            <div id="saveResult" class="result hidden"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        let authToken = '';

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                const resultDiv = document.getElementById('loginResult');
                resultDiv.classList.remove('hidden');
                
                if (result.success && result.data.access_token) {
                    authToken = result.data.access_token;
                    resultDiv.innerHTML = `<span class="success">✅ 登录成功!</span><br>
                        <span class="info">用户: ${result.data.user.username} (${result.data.user.full_name})</span>`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 登录失败: ${result.message}</span>`;
                }
            } catch (error) {
                document.getElementById('loginResult').innerHTML = 
                    `<span class="error">❌ 登录错误: ${error.message}</span>`;
            }
        }

        async function testConnection() {
            if (!authToken) {
                alert('请先登录');
                return;
            }
            
            const ollamaUrl = document.getElementById('ollamaUrl').value;
            const btn = document.getElementById('testConnBtn');
            const loading = document.getElementById('connLoading');
            const resultDiv = document.getElementById('connectionResult');
            
            btn.disabled = true;
            loading.style.display = 'block';
            resultDiv.classList.add('hidden');
            
            try {
                const response = await fetch(`${API_BASE}/ai/ollama/test-connection`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ base_url: ollamaUrl })
                });
                
                const result = await response.json();
                resultDiv.classList.remove('hidden');
                
                if (result.success) {
                    resultDiv.innerHTML = `<span class="success">✅ 连接成功!</span><br>
                        <span class="info">状态: ${result.data.status}</span><br>
                        <span class="info">版本: ${result.data.version}</span><br>
                        <span class="info">URL: ${result.data.url}</span>`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 连接失败: ${result.message}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 连接错误: ${error.message}</span>`;
                resultDiv.classList.remove('hidden');
            } finally {
                btn.disabled = false;
                loading.style.display = 'none';
            }
        }

        async function getModels() {
            if (!authToken) {
                alert('请先登录');
                return;
            }
            
            const ollamaUrl = document.getElementById('ollamaUrl').value;
            const btn = document.getElementById('getModelsBtn');
            const loading = document.getElementById('modelsLoading');
            const resultDiv = document.getElementById('modelsResult');
            const modelsList = document.getElementById('modelsList');
            
            btn.disabled = true;
            loading.style.display = 'block';
            resultDiv.classList.add('hidden');
            modelsList.innerHTML = '';
            
            try {
                const response = await fetch(`${API_BASE}/ai/ollama/models?base_url=${encodeURIComponent(ollamaUrl)}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const result = await response.json();
                resultDiv.classList.remove('hidden');
                
                if (result.success) {
                    resultDiv.innerHTML = `<span class="success">✅ 获取成功!</span><br>
                        <span class="info">找到 ${result.data.total} 个模型</span>`;
                    
                    result.data.models.forEach(model => {
                        const modelDiv = document.createElement('div');
                        modelDiv.className = 'model-item';
                        modelDiv.innerHTML = `
                            <h4>${model.name}</h4>
                            <div class="info">大小: ${model.size_gb} GB</div>
                            <div class="info">修改时间: ${new Date(model.modified_at).toLocaleString()}</div>
                            <div class="model-details">
                                <strong>详情:</strong><br>
                                格式: ${model.details.format || 'N/A'}<br>
                                家族: ${model.details.family || 'N/A'}<br>
                                参数大小: ${model.details.parameter_size || 'N/A'}<br>
                                量化级别: ${model.details.quantization_level || 'N/A'}
                            </div>
                            <div class="model-actions">
                                <button onclick="selectModel('${model.name}')">选择此模型</button>
                                <button onclick="testModel('${model.name}')">测试模型</button>
                            </div>
                        `;
                        modelsList.appendChild(modelDiv);
                    });
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 获取失败: ${result.message}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 获取错误: ${error.message}</span>`;
                resultDiv.classList.remove('hidden');
            } finally {
                btn.disabled = false;
                loading.style.display = 'none';
            }
        }

        function selectModel(modelName) {
            document.getElementById('selectedModel').value = modelName;
            document.getElementById('saveConfigBtn').disabled = false;
        }

        async function testModel(modelName) {
            if (!authToken) {
                alert('请先登录');
                return;
            }
            
            const ollamaUrl = document.getElementById('ollamaUrl').value;
            
            try {
                const response = await fetch(`${API_BASE}/ai/ollama/test-model?base_url=${encodeURIComponent(ollamaUrl)}&model_name=${encodeURIComponent(modelName)}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert(`✅ 模型测试成功!\n模型: ${result.data.model}\n回复: ${result.data.response.substring(0, 100)}...`);
                } else {
                    alert(`❌ 模型测试失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 测试错误: ${error.message}`);
            }
        }

        async function saveConfig() {
            if (!authToken) {
                alert('请先登录');
                return;
            }
            
            const ollamaUrl = document.getElementById('ollamaUrl').value;
            const selectedModel = document.getElementById('selectedModel').value;
            const btn = document.getElementById('saveConfigBtn');
            const loading = document.getElementById('saveLoading');
            const resultDiv = document.getElementById('saveResult');
            
            btn.disabled = true;
            loading.style.display = 'block';
            resultDiv.classList.add('hidden');
            
            try {
                const response = await fetch(`${API_BASE}/ai/ollama/save-config`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        base_url: ollamaUrl,
                        model: selectedModel
                    })
                });
                
                const result = await response.json();
                resultDiv.classList.remove('hidden');
                
                if (result.success) {
                    resultDiv.innerHTML = `<span class="success">✅ 配置保存成功!</span><br>
                        <span class="info">配置ID: ${result.data.id}</span><br>
                        <span class="info">提供商: ${result.data.provider}</span><br>
                        <span class="info">Ollama URL: ${result.data.ollama_base_url}</span><br>
                        <span class="info">选择的模型: ${result.data.ollama_model}</span>`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 保存失败: ${result.message}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 保存错误: ${error.message}</span>`;
                resultDiv.classList.remove('hidden');
            } finally {
                btn.disabled = false;
                loading.style.display = 'none';
            }
        }
    </script>
</body>
</html>
