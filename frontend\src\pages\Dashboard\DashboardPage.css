.dashboard-page {
  padding: 0;
}

.dashboard-header {
  margin-bottom: 24px;
}

.dashboard-header .ant-typography {
  margin-bottom: 8px;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stat-card .ant-statistic-title {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.stat-card .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

.stat-footer {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.stat-footer .ant-btn-link {
  padding: 0;
  height: auto;
  font-size: 12px;
}

.content-row {
  margin-bottom: 24px;
}

.activity-card,
.quick-actions-card,
.alerts-card,
.monthly-stats-card {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

.activity-card .ant-card-head-title {
  font-size: 16px;
  font-weight: 600;
}

.activity-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item .ant-list-item-meta-title {
  margin-bottom: 4px;
  font-size: 14px;
}

.activity-item .ant-list-item-meta-description {
  font-size: 13px;
  color: #666;
}

.activity-time {
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

.quick-actions-card .ant-btn {
  height: 40px;
  border-radius: 6px;
  font-weight: 500;
}

.alerts-card .ant-alert {
  margin-bottom: 12px;
  border-radius: 6px;
}

.alerts-card .ant-alert:last-child {
  margin-bottom: 0;
}

.monthly-stats-card .stat-item {
  margin-bottom: 16px;
}

.monthly-stats-card .stat-item:last-child {
  margin-bottom: 0;
}

.stat-label {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
}

.monthly-stats-card .ant-progress {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-page {
    padding: 0;
  }
  
  .dashboard-header {
    margin-bottom: 16px;
  }
  
  .stats-row {
    margin-bottom: 16px;
  }
  
  .content-row {
    margin-bottom: 16px;
  }
  
  .stat-card .ant-statistic-content {
    font-size: 20px;
  }
  
  .activity-item {
    padding: 12px 0;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .stat-card,
  .activity-card,
  .quick-actions-card,
  .alerts-card,
  .monthly-stats-card {
    background: #141414;
    border-color: #303030;
  }
  
  .stat-footer {
    border-top-color: #303030;
  }
  
  .activity-item {
    border-bottom-color: #303030;
  }
  
  .stat-card .ant-statistic-title,
  .activity-item .ant-list-item-meta-description,
  .stat-label {
    color: #ccc;
  }
}
