import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Typography,
  Row,
  Col,
  Statistic,
  Popconfirm,
  Tooltip,
  Badge,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  SearchOutlined,
  ReloadOutlined,
  RobotOutlined,
  FileTextOutlined,
  ThunderboltOutlined,
  StarOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;
const { Search, TextArea } = Input;

const TemplatesPage = () => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [form] = Form.useForm();

  // 模拟数据
  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = () => {
    setLoading(true);
    setTimeout(() => {
      setTemplates([
        {
          id: 1,
          name: '感谢回复模板',
          category: '感谢类',
          content: '谢谢你的关注和支持！{emoji_heart} 如果有任何问题随时私信我哦～',
          variables: ['emoji_heart'],
          tags: ['感谢', '互动'],
          usage_count: 156,
          success_rate: 92,
          is_active: true,
          is_ai_generated: false,
          created_at: '2024-01-15 09:00:00',
        },
        {
          id: 2,
          name: '产品咨询回复',
          category: '咨询类',
          content: '这个产品确实很不错呢！{product_name}的具体信息我已经私信发给你了，记得查看哦～',
          variables: ['product_name'],
          tags: ['产品', '咨询', '私信'],
          usage_count: 89,
          success_rate: 88,
          is_active: true,
          is_ai_generated: true,
          created_at: '2024-01-16 14:30:00',
        },
        {
          id: 3,
          name: '购买链接回复',
          category: '购买类',
          content: '亲爱的，购买链接已经放在我的主页了！点击头像就能看到哦～有问题随时联系我！',
          variables: [],
          tags: ['购买', '链接', '引导'],
          usage_count: 234,
          success_rate: 95,
          is_active: true,
          is_ai_generated: false,
          created_at: '2024-01-14 16:20:00',
        },
      ]);
      setLoading(false);
    }, 1000);
  };

  // 过滤数据
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchText.toLowerCase()) ||
                         template.content.toLowerCase().includes(searchText.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || template.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  // 表格列配置
  const columns = [
    {
      title: '模板信息',
      key: 'info',
      render: (_, record) => (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
            <strong style={{ marginRight: 8 }}>{record.name}</strong>
            {record.is_ai_generated && (
              <Badge count="AI" style={{ backgroundColor: '#722ed1' }} />
            )}
          </div>
          <div style={{ fontSize: '12px', color: '#666', marginBottom: 8 }}>
            分类: {record.category}
          </div>
          <div style={{ 
            background: '#f5f5f5', 
            padding: '8px 12px', 
            borderRadius: 4,
            fontSize: '13px',
            maxWidth: 300
          }}>
            {record.content}
          </div>
        </div>
      ),
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      width: 150,
      render: (tags) => (
        <div>
          {tags?.map(tag => (
            <Tag key={tag} size="small" style={{ marginBottom: 4 }}>
              {tag}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: '使用统计',
      key: 'stats',
      width: 120,
      render: (_, record) => (
        <div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            使用: {record.usage_count}次
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            成功率: {record.success_rate}%
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 80,
      render: (active) => (
        <Tag color={active ? 'success' : 'default'}>
          {active ? '启用' : '停用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="复制模板">
            <Button
              type="link"
              icon={<CopyOutlined />}
              onClick={() => handleCopy(record)}
            />
          </Tooltip>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个模板吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 处理添加模板
  const handleAdd = () => {
    setEditingTemplate(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 处理编辑模板
  const handleEdit = (template) => {
    setEditingTemplate(template);
    form.setFieldsValue({
      ...template,
      tags: template.tags?.join(', ') || '',
    });
    setModalVisible(true);
  };

  // 处理复制模板
  const handleCopy = (template) => {
    setEditingTemplate(null);
    form.setFieldsValue({
      ...template,
      name: `${template.name} - 副本`,
      tags: template.tags?.join(', ') || '',
    });
    setModalVisible(true);
  };

  // 处理删除模板
  const handleDelete = (id) => {
    setTemplates(templates.filter(template => template.id !== id));
    message.success('模板删除成功');
  };

  // 处理表单提交
  const handleSubmit = async (values) => {
    try {
      const templateData = {
        ...values,
        tags: values.tags ? values.tags.split(',').map(tag => tag.trim()) : [],
        variables: extractVariables(values.content),
      };

      if (editingTemplate) {
        // 更新模板
        setTemplates(templates.map(template => 
          template.id === editingTemplate.id 
            ? { ...template, ...templateData }
            : template
        ));
        message.success('模板更新成功');
      } else {
        // 添加模板
        const newTemplate = {
          id: Date.now(),
          ...templateData,
          usage_count: 0,
          success_rate: 0,
          is_ai_generated: false,
          created_at: new Date().toLocaleString(),
        };
        setTemplates([...templates, newTemplate]);
        message.success('模板添加成功');
      }
      setModalVisible(false);
    } catch (error) {
      message.error('操作失败，请重试');
    }
  };

  // 提取模板变量
  const extractVariables = (content) => {
    const regex = /\{([^}]+)\}/g;
    const variables = [];
    let match;
    while ((match = regex.exec(content)) !== null) {
      variables.push(match[1]);
    }
    return variables;
  };

  // 生成AI模板
  const handleGenerateAI = () => {
    Modal.confirm({
      title: 'AI生成模板',
      content: (
        <div>
          <p>请描述您需要的回复模板类型：</p>
          <Input.TextArea 
            rows={3} 
            placeholder="例如：针对美妆产品咨询的友好回复模板"
          />
        </div>
      ),
      onOk: () => {
        message.success('AI模板生成功能开发中...');
      },
    });
  };

  // 统计数据
  const stats = {
    total: templates.length,
    active: templates.filter(t => t.is_active).length,
    ai_generated: templates.filter(t => t.is_ai_generated).length,
    total_usage: templates.reduce((sum, t) => sum + t.usage_count, 0),
  };

  return (
    <div className="templates-page">
      <div className="page-header">
        <Title level={2}>回复模板</Title>
        <p>管理AI回复模板，提高回复效率和质量</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总模板数"
              value={stats.total}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="启用中"
              value={stats.active}
              prefix={<ThunderboltOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="AI生成"
              value={stats.ai_generated}
              prefix={<RobotOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总使用次数"
              value={stats.total_usage}
              prefix={<StarOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={8}>
            <Search
              placeholder="搜索模板名称或内容"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              prefix={<SearchOutlined />}
            />
          </Col>
          <Col span={4}>
            <Select
              value={categoryFilter}
              onChange={setCategoryFilter}
              style={{ width: '100%' }}
            >
              <Option value="all">全部分类</Option>
              <Option value="感谢类">感谢类</Option>
              <Option value="咨询类">咨询类</Option>
              <Option value="购买类">购买类</Option>
              <Option value="问候类">问候类</Option>
            </Select>
          </Col>
          <Col span={12} style={{ textAlign: 'right' }}>
            <Space>
              <Button
                icon={<RobotOutlined />}
                onClick={handleGenerateAI}
              >
                AI生成
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadTemplates}
                loading={loading}
              >
                刷新
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
              >
                添加模板
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 模板列表 */}
      <Card title="模板列表">
        <Table
          columns={columns}
          dataSource={filteredTemplates}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 添加/编辑模板模态框 */}
      <Modal
        title={editingTemplate ? '编辑模板' : '添加模板'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="模板名称"
                rules={[
                  { required: true, message: '请输入模板名称' },
                  { max: 100, message: '名称不能超过100个字符' },
                ]}
              >
                <Input placeholder="请输入模板名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="category"
                label="模板分类"
                rules={[{ required: true, message: '请选择模板分类' }]}
              >
                <Select placeholder="请选择分类">
                  <Option value="感谢类">感谢类</Option>
                  <Option value="咨询类">咨询类</Option>
                  <Option value="购买类">购买类</Option>
                  <Option value="问候类">问候类</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="content"
            label="模板内容"
            rules={[
              { required: true, message: '请输入模板内容' },
              { max: 500, message: '内容不能超过500个字符' },
            ]}
          >
            <TextArea
              rows={4}
              placeholder="请输入模板内容，使用 {变量名} 来定义变量"
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item
            name="tags"
            label="标签"
            help="多个标签用逗号分隔"
          >
            <Input placeholder="例如：感谢,互动,回复" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="is_active"
                label="启用状态"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingTemplate ? '更新' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TemplatesPage;
