<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前后端连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success {
            background-color: #f6ffed;
            border-color: #b7eb8f;
        }
        .error {
            background-color: #fff2f0;
            border-color: #ffccc7;
        }
        .loading {
            background-color: #f0f9ff;
            border-color: #91d5ff;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 前后端连接测试</h1>
        
        <div class="test-item" id="frontend-test">
            <h3>1. 前端服务器状态</h3>
            <p>✅ 前端服务器运行正常 (您能看到这个页面)</p>
            <p>📍 地址: <a href="http://localhost:3000" target="_blank">http://localhost:3000</a></p>
        </div>

        <div class="test-item" id="backend-test">
            <h3>2. 后端API连接测试</h3>
            <button onclick="testBackendHealth()">测试后端健康检查</button>
            <div id="backend-result"></div>
        </div>

        <div class="test-item" id="proxy-test">
            <h3>3. 代理配置测试</h3>
            <button onclick="testProxy()">测试Vite代理</button>
            <div id="proxy-result"></div>
        </div>

        <div class="test-item" id="api-test">
            <h3>4. API接口测试</h3>
            <button onclick="testApiEndpoints()">测试主要API端点</button>
            <div id="api-result"></div>
        </div>

        <div class="test-item">
            <h3>5. 系统信息</h3>
            <ul>
                <li>前端框架: React 18 + Vite</li>
                <li>UI组件库: Ant Design 5</li>
                <li>状态管理: Zustand</li>
                <li>HTTP客户端: Axios</li>
                <li>后端框架: FastAPI + Python</li>
                <li>数据库: SQLite (开发环境)</li>
            </ul>
        </div>
    </div>

    <script>
        async function testBackendHealth() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.innerHTML = '<p class="loading">🔄 正在测试后端连接...</p>';
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/health');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <p>✅ 后端API连接成功</p>
                            <p>状态: ${data.status}</p>
                            <p>服务: ${data.service}</p>
                            <p>时间: ${data.timestamp}</p>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <p>❌ 后端API连接失败</p>
                        <p>错误: ${error.message}</p>
                        <p>请确保后端服务器正在运行在 http://localhost:8000</p>
                    </div>
                `;
            }
        }

        async function testProxy() {
            const resultDiv = document.getElementById('proxy-result');
            resultDiv.innerHTML = '<p class="loading">🔄 正在测试代理配置...</p>';
            
            try {
                const response = await fetch('/api/v1/health');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <p>✅ Vite代理配置正常</p>
                            <p>通过代理访问: /api/v1/health</p>
                            <p>响应: ${JSON.stringify(data, null, 2)}</p>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <p>❌ 代理配置有问题</p>
                        <p>错误: ${error.message}</p>
                        <p>请检查 vite.config.js 中的代理设置</p>
                    </div>
                `;
            }
        }

        async function testApiEndpoints() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '<p class="loading">🔄 正在测试API端点...</p>';
            
            const endpoints = [
                { name: '健康检查', url: '/api/v1/health', method: 'GET' },
                { name: 'API文档', url: '/api/v1/docs', method: 'GET' },
                { name: '用户登录', url: '/api/v1/auth/login', method: 'POST', requiresAuth: true }
            ];
            
            let results = [];
            
            for (const endpoint of endpoints) {
                try {
                    if (endpoint.requiresAuth) {
                        results.push(`⚠️ ${endpoint.name}: 需要认证 (跳过测试)`);
                        continue;
                    }
                    
                    const response = await fetch(endpoint.url, { method: endpoint.method });
                    
                    if (response.ok || response.status === 422) { // 422 for validation errors is expected
                        results.push(`✅ ${endpoint.name}: 正常 (${response.status})`);
                    } else {
                        results.push(`⚠️ ${endpoint.name}: ${response.status} ${response.statusText}`);
                    }
                } catch (error) {
                    results.push(`❌ ${endpoint.name}: ${error.message}`);
                }
            }
            
            resultDiv.innerHTML = `
                <div class="success">
                    <h4>API端点测试结果:</h4>
                    <ul>
                        ${results.map(result => `<li>${result}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        // 页面加载时自动测试
        window.onload = function() {
            setTimeout(() => {
                testBackendHealth();
                testProxy();
            }, 1000);
        };
    </script>
</body>
</html>
