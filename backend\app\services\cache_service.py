import json
import pickle
from typing import Any, Optional, Union
from datetime import datetime, timedelta
import hashlib
from loguru import logger

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logger.warning("Redis not available, using in-memory cache")


class CacheService:
    """缓存服务"""
    
    def __init__(self, redis_url: Optional[str] = None):
        self.redis_client = None
        self.memory_cache = {}
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0
        }
        
        if REDIS_AVAILABLE and redis_url:
            try:
                self.redis_client = redis.from_url(redis_url, decode_responses=True)
                # 测试连接
                self.redis_client.ping()
                logger.info("Redis缓存连接成功")
            except Exception as e:
                logger.error(f"Redis连接失败: {e}")
                self.redis_client = None
    
    def _generate_key(self, key: str, user_id: Optional[int] = None) -> str:
        """生成缓存键"""
        if user_id:
            return f"user:{user_id}:{key}"
        return key
    
    def _serialize_value(self, value: Any) -> str:
        """序列化值"""
        if isinstance(value, (str, int, float, bool)):
            return json.dumps(value)
        else:
            # 对于复杂对象，使用pickle序列化后base64编码
            import base64
            pickled = pickle.dumps(value)
            return base64.b64encode(pickled).decode('utf-8')
    
    def _deserialize_value(self, value: str) -> Any:
        """反序列化值"""
        try:
            return json.loads(value)
        except (json.JSONDecodeError, ValueError):
            # 尝试pickle反序列化
            try:
                import base64
                pickled = base64.b64decode(value.encode('utf-8'))
                return pickle.loads(pickled)
            except Exception:
                return value
    
    def get(self, key: str, user_id: Optional[int] = None) -> Optional[Any]:
        """获取缓存值"""
        cache_key = self._generate_key(key, user_id)
        
        try:
            if self.redis_client:
                value = self.redis_client.get(cache_key)
                if value is not None:
                    self.cache_stats["hits"] += 1
                    return self._deserialize_value(value)
            else:
                # 使用内存缓存
                cache_entry = self.memory_cache.get(cache_key)
                if cache_entry:
                    # 检查是否过期
                    if cache_entry["expires_at"] > datetime.now():
                        self.cache_stats["hits"] += 1
                        return cache_entry["value"]
                    else:
                        # 过期，删除
                        del self.memory_cache[cache_key]
            
            self.cache_stats["misses"] += 1
            return None
            
        except Exception as e:
            logger.error(f"缓存获取失败: {e}")
            self.cache_stats["misses"] += 1
            return None
    
    def set(
        self, 
        key: str, 
        value: Any, 
        ttl: int = 3600, 
        user_id: Optional[int] = None
    ) -> bool:
        """设置缓存值"""
        cache_key = self._generate_key(key, user_id)
        
        try:
            if self.redis_client:
                serialized_value = self._serialize_value(value)
                result = self.redis_client.setex(cache_key, ttl, serialized_value)
                if result:
                    self.cache_stats["sets"] += 1
                return result
            else:
                # 使用内存缓存
                expires_at = datetime.now() + timedelta(seconds=ttl)
                self.memory_cache[cache_key] = {
                    "value": value,
                    "expires_at": expires_at
                }
                self.cache_stats["sets"] += 1
                return True
                
        except Exception as e:
            logger.error(f"缓存设置失败: {e}")
            return False
    
    def delete(self, key: str, user_id: Optional[int] = None) -> bool:
        """删除缓存值"""
        cache_key = self._generate_key(key, user_id)
        
        try:
            if self.redis_client:
                result = self.redis_client.delete(cache_key)
                if result:
                    self.cache_stats["deletes"] += 1
                return bool(result)
            else:
                if cache_key in self.memory_cache:
                    del self.memory_cache[cache_key]
                    self.cache_stats["deletes"] += 1
                    return True
                return False
                
        except Exception as e:
            logger.error(f"缓存删除失败: {e}")
            return False
    
    def exists(self, key: str, user_id: Optional[int] = None) -> bool:
        """检查缓存是否存在"""
        cache_key = self._generate_key(key, user_id)
        
        try:
            if self.redis_client:
                return bool(self.redis_client.exists(cache_key))
            else:
                cache_entry = self.memory_cache.get(cache_key)
                if cache_entry:
                    return cache_entry["expires_at"] > datetime.now()
                return False
                
        except Exception as e:
            logger.error(f"缓存检查失败: {e}")
            return False
    
    def clear_user_cache(self, user_id: int) -> int:
        """清除用户相关的所有缓存"""
        pattern = f"user:{user_id}:*"
        deleted_count = 0
        
        try:
            if self.redis_client:
                keys = self.redis_client.keys(pattern)
                if keys:
                    deleted_count = self.redis_client.delete(*keys)
            else:
                keys_to_delete = [k for k in self.memory_cache.keys() if k.startswith(f"user:{user_id}:")]
                for key in keys_to_delete:
                    del self.memory_cache[key]
                deleted_count = len(keys_to_delete)
            
            self.cache_stats["deletes"] += deleted_count
            return deleted_count
            
        except Exception as e:
            logger.error(f"清除用户缓存失败: {e}")
            return 0
    
    def get_stats(self) -> dict:
        """获取缓存统计信息"""
        stats = self.cache_stats.copy()
        
        if self.redis_client:
            try:
                info = self.redis_client.info()
                stats.update({
                    "backend": "redis",
                    "connected_clients": info.get("connected_clients", 0),
                    "used_memory": info.get("used_memory_human", "0B"),
                    "keyspace_hits": info.get("keyspace_hits", 0),
                    "keyspace_misses": info.get("keyspace_misses", 0)
                })
            except Exception as e:
                logger.error(f"获取Redis统计失败: {e}")
                stats["backend"] = "redis (error)"
        else:
            stats.update({
                "backend": "memory",
                "cache_size": len(self.memory_cache)
            })
        
        # 计算命中率
        total_requests = stats["hits"] + stats["misses"]
        stats["hit_rate"] = (stats["hits"] / total_requests * 100) if total_requests > 0 else 0
        
        return stats


class AIReplyCache:
    """AI回复专用缓存"""
    
    def __init__(self, cache_service: CacheService):
        self.cache = cache_service
        self.ttl = 3600  # 1小时
    
    def _generate_reply_key(self, content: str, context: dict = None, template_id: int = None) -> str:
        """生成回复缓存键"""
        # 创建内容哈希
        content_hash = hashlib.md5(content.encode()).hexdigest()
        
        # 添加上下文和模板信息
        key_parts = [f"ai_reply:{content_hash}"]
        
        if template_id:
            key_parts.append(f"template:{template_id}")
        
        if context:
            context_str = json.dumps(context, sort_keys=True)
            context_hash = hashlib.md5(context_str.encode()).hexdigest()
            key_parts.append(f"context:{context_hash}")
        
        return ":".join(key_parts)
    
    def get_reply(
        self, 
        content: str, 
        user_id: int,
        context: dict = None, 
        template_id: int = None
    ) -> Optional[dict]:
        """获取缓存的AI回复"""
        key = self._generate_reply_key(content, context, template_id)
        return self.cache.get(key, user_id)
    
    def set_reply(
        self, 
        content: str, 
        reply_data: dict, 
        user_id: int,
        context: dict = None, 
        template_id: int = None
    ) -> bool:
        """缓存AI回复"""
        key = self._generate_reply_key(content, context, template_id)
        return self.cache.set(key, reply_data, self.ttl, user_id)
    
    def invalidate_user_replies(self, user_id: int) -> int:
        """清除用户的AI回复缓存"""
        return self.cache.clear_user_cache(user_id)


class AnalyticsCache:
    """分析数据专用缓存"""
    
    def __init__(self, cache_service: CacheService):
        self.cache = cache_service
        self.ttl = 1800  # 30分钟
    
    def get_analytics(self, analytics_type: str, user_id: int, params: dict = None) -> Optional[dict]:
        """获取缓存的分析数据"""
        key = f"analytics:{analytics_type}"
        if params:
            params_str = json.dumps(params, sort_keys=True)
            params_hash = hashlib.md5(params_str.encode()).hexdigest()
            key += f":{params_hash}"
        
        return self.cache.get(key, user_id)
    
    def set_analytics(
        self, 
        analytics_type: str, 
        data: dict, 
        user_id: int, 
        params: dict = None
    ) -> bool:
        """缓存分析数据"""
        key = f"analytics:{analytics_type}"
        if params:
            params_str = json.dumps(params, sort_keys=True)
            params_hash = hashlib.md5(params_str.encode()).hexdigest()
            key += f":{params_hash}"
        
        return self.cache.set(key, data, self.ttl, user_id)
    
    def invalidate_analytics(self, user_id: int) -> int:
        """清除用户的分析数据缓存"""
        # 清除所有analytics相关的缓存
        pattern = f"user:{user_id}:analytics:*"
        return self.cache.clear_user_cache(user_id)


# 全局缓存实例
cache_service = CacheService()
ai_reply_cache = AIReplyCache(cache_service)
analytics_cache = AnalyticsCache(cache_service)
