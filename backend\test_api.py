#!/usr/bin/env python3
"""
测试API端点的脚本
"""
import requests
import json

# API配置
BASE_URL = "http://localhost:8000/api/v1"
TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxIiwiZXhwIjoxNzUyODY5MDQyfQ.zmspM5MCpV40B7-nYuDuQSZgAy51EFcIe2qP3gda9aw"

# 请求头
headers = {
    "Authorization": f"Bearer {TOKEN}",
    "Content-Type": "application/json"
}

def test_crawler_test():
    """测试爬虫测试端点"""
    print("🔍 测试爬虫测试端点...")

    try:
        response = requests.get(f"{BASE_URL}/crawler/test", headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")

        if response.status_code == 200:
            data = response.json()
            print(f"✅ 测试端点调用成功!")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 测试端点调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")

    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_crawler_test_db():
    """测试爬虫数据库测试端点"""
    print("\n🔍 测试爬虫数据库测试端点...")

    try:
        response = requests.get(f"{BASE_URL}/crawler/test-db", headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")

        if response.status_code == 200:
            data = response.json()
            print(f"✅ 数据库测试端点调用成功!")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 数据库测试端点调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")

    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_crawler_test_response():
    """测试爬虫success_response测试端点"""
    print("\n🔍 测试爬虫success_response测试端点...")

    try:
        response = requests.get(f"{BASE_URL}/crawler/test-response", headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")

        if response.status_code == 200:
            data = response.json()
            print(f"✅ success_response测试端点调用成功!")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ success_response测试端点调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")

    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_crawler_status():
    """测试爬虫状态API"""
    print("\n🔍 测试爬虫状态API...")

    try:
        response = requests.get(f"{BASE_URL}/crawler/status", headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")

        if response.status_code == 200:
            data = response.json()
            print(f"✅ API调用成功!")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")

    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_user_info():
    """测试用户信息API"""
    print("\n🔍 测试用户信息API...")
    
    try:
        response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 用户信息获取成功!")
            print(f"用户数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 用户信息获取失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    print("🚀 开始API测试...")
    test_user_info()
    test_crawler_test()
    test_crawler_test_db()
    test_crawler_test_response()
    test_crawler_status()
    print("\n✅ API测试完成!")
